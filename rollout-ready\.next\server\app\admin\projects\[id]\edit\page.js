(()=>{var e={};e.id=26,e.ids=[26],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1328:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},2086:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(7413),i=t(4536),n=t.n(i),a=t(8963),d=t(3469),o=t(5069),l=t(9916);async function c(e){return await o.z.project.findUnique({where:{id:e},include:{projectRoles:{include:{role:!0}}}})}async function p({params:e}){let r=parseInt((await e).id);isNaN(r)&&(0,l.notFound)();let t=await c(r);return t||(0,l.notFound)(),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Edit Project: ",t.name]}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Modify project details and team assignments"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n(),{href:`/admin/projects/${t.id}`,children:(0,s.jsx)(d.$,{variant:"outline",children:"Back to Project"})}),(0,s.jsx)(n(),{href:"/admin",children:(0,s.jsx)(d.$,{variant:"outline",children:"Back to Admin"})})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"Project Edit"}),(0,s.jsx)(a.BT,{children:"Project editing functionality is not yet implemented. This is a placeholder page."})]}),(0,s.jsx)(a.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold",children:"Current Project Details:"}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Name:"})," ",t.name]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Description:"})," ",t.description||"No description"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Start Date:"})," ",new Date(t.startDate).toLocaleDateString()]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold",children:"Current Team:"}),(0,s.jsx)("ul",{className:"list-disc list-inside",children:t.projectRoles.map(e=>(0,s.jsxs)("li",{children:[e.role.name,": ",e.userName]},e.id))})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-yellow-800",children:"Coming Soon"}),(0,s.jsx)("p",{className:"text-yellow-700",children:"Project editing functionality will be implemented in a future update. This will include the ability to:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside text-yellow-700 mt-2",children:[(0,s.jsx)("li",{children:"Update project name and description"}),(0,s.jsx)("li",{children:"Modify start date"}),(0,s.jsx)("li",{children:"Reassign team members to roles"}),(0,s.jsx)("li",{children:"Add or remove team members"})]})]})]})})]})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6330:e=>{"use strict";e.exports=require("@prisma/client")},7776:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},8423:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=t(5239),i=t(8088),n=t(8170),a=t.n(n),d=t(893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let l={children:["",{children:["admin",{children:["projects",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2086)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\edit\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/projects/[id]/edit/page",pathname:"/admin/projects/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,982,277,923,287,223],()=>t(8423));module.exports=s})();