(()=>{var e={};e.id=26,e.ids=[26],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>p,eb:()=>x,gC:()=>u,l6:()=>o,yv:()=>c});var r=s(60687);s(43210);var a=s(16725),n=s(78272),i=s(13964),l=s(3589),d=s(4780);function o({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function p({className:e,size:t="default",children:s,...i}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:t,position:s="popper",...n}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,r.jsx)(m,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(h,{})]})})}function x({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function m({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"size-4"})})}function h({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38423:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["projects",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,82086)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\edit\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/projects/[id]/edit/page",pathname:"/admin/projects/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var r=s(60687),a=s(43210),n=s(14163),i=a.forwardRef((e,t)=>(0,r.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=s(4780);function d({className:e,...t}){return(0,r.jsx)(i,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},54824:(e,t,s)=>{Promise.resolve().then(s.bind(s,89808))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63523:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(60687),a=s(43210),n=s(15079),i=s(96834);function l({value:e,onValueChange:t,placeholder:s="Select a user",disabled:l=!1,excludeUserIds:d=[],projectRoleName:o}){let[c,p]=(0,a.useState)([]),[u,x]=(0,a.useState)(!0),[m,h]=(0,a.useState)(""),f=e=>e.firstName&&e.lastName?`${e.firstName} ${e.lastName} (@${e.username})`:`@${e.username}`,j=e=>{switch(e){case"ADMIN":return"destructive";case"MANAGER":return"default";case"USER":return"secondary";default:return"outline"}};return u?(0,r.jsx)(n.l6,{disabled:!0,children:(0,r.jsx)(n.bq,{children:(0,r.jsx)(n.yv,{placeholder:"Loading users..."})})}):m?(0,r.jsx)(n.l6,{disabled:!0,children:(0,r.jsx)(n.bq,{children:(0,r.jsx)(n.yv,{placeholder:m})})}):(0,r.jsxs)(n.l6,{value:e?e.toString():"none",onValueChange:e=>{"none"===e?t(void 0):t(parseInt(e))},disabled:l,children:[(0,r.jsx)(n.bq,{children:(0,r.jsx)(n.yv,{placeholder:o?`Select user for ${o} role`:s})}),(0,r.jsxs)(n.gC,{children:[(0,r.jsx)(n.eb,{value:"none",children:(0,r.jsx)("span",{className:"text-gray-500",children:"No user assigned"})}),c.map(e=>(0,r.jsx)(n.eb,{value:e.id.toString(),children:(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{children:f(e)}),e.jobRole&&(0,r.jsx)("span",{className:"text-xs text-gray-500",children:e.jobRole.name})]}),(0,r.jsxs)("div",{className:"flex gap-1",children:[e.jobRole&&(0,r.jsx)(i.E,{variant:"outline",className:"text-xs",children:e.jobRole.name}),(0,r.jsx)(i.E,{variant:j(e.systemRole),className:"text-xs",children:e.systemRole})]})]})},e.id))]})]})}},67976:(e,t,s)=>{Promise.resolve().then(s.bind(s,82086))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},82086:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\app\\\\admin\\\\projects\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\edit\\page.tsx","default")},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687);s(43210);var a=s(4780);function n({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},89808:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(60687),a=s(43210),n=s(16189),i=s(85814),l=s.n(i),d=s(44493),o=s(29523),c=s(89667),p=s(54300),u=s(63523);function x({params:e}){let t=(0,n.useRouter)(),[s,i]=(0,a.useState)(!1),[x,m]=(0,a.useState)(null),[h,f]=(0,a.useState)([]),[j,v]=(0,a.useState)({name:"",description:"",startDate:""}),[g,b]=(0,a.useState)({}),[y,N]=(0,a.useState)(""),w=async e=>{if(e.preventDefault(),x){i(!0),N("");try{let e={...j,roleAssignments:g},s=await fetch(`/api/projects/${x.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(s.ok)t.push(`/admin/projects/${x.id}`);else{let e=await s.json();N(e.message||"Failed to update project")}}catch(e){N("An error occurred while updating the project")}finally{i(!1)}}},R=e=>{v({...j,[e.target.name]:e.target.value})},P=(e,t)=>{b({...g,[e]:t})};return y?(0,r.jsx)("div",{className:"space-y-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Error"}),(0,r.jsx)("p",{className:"text-red-600 mt-2",children:y}),(0,r.jsx)(l(),{href:"/admin",children:(0,r.jsx)(o.$,{className:"mt-4",children:"Back to Admin"})})]})}):x?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Edit Project: ",x.name]}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Modify project details and team assignments"})]}),(0,r.jsxs)("form",{onSubmit:w,className:"space-y-8",children:[(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{children:[(0,r.jsx)(d.ZB,{children:"Project Details"}),(0,r.jsx)(d.BT,{children:"Update basic project information"})]}),(0,r.jsxs)(d.Wu,{className:"space-y-6",children:[y&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:y}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p.J,{htmlFor:"name",children:"Project Name *"}),(0,r.jsx)(c.p,{id:"name",name:"name",type:"text",required:!0,value:j.name,onChange:R,placeholder:"e.g., Deploy MES at Avonmouth"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p.J,{htmlFor:"description",children:"Description"}),(0,r.jsx)("textarea",{id:"description",name:"description",value:j.description,onChange:R,placeholder:"Brief description of the project",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p.J,{htmlFor:"startDate",children:"Start Date *"}),(0,r.jsx)(c.p,{id:"startDate",name:"startDate",type:"date",required:!0,value:j.startDate,onChange:R})]})]})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{children:[(0,r.jsx)(d.ZB,{children:"Team Assignments"}),(0,r.jsx)(d.BT,{children:"Assign team members to project roles"})]}),(0,r.jsx)(d.Wu,{children:0===h.length?(0,r.jsx)("p",{className:"text-gray-500",children:"Loading roles..."}):(0,r.jsx)("div",{className:"space-y-4",children:h.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,r.jsx)("div",{className:"w-64",children:(0,r.jsx)(u.A,{value:g[e.id],onValueChange:t=>P(e.id,t),placeholder:`Select user for ${e.name}`,excludeUserIds:Object.values(g).filter(t=>void 0!==t&&t!==g[e.id]),projectRoleName:e.name})})]},e.id))})})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(o.$,{type:"submit",disabled:s,children:s?"Updating...":"Update Project"}),(0,r.jsx)(l(),{href:`/admin/projects/${x.id}`,children:(0,r.jsx)(o.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})]}):(0,r.jsx)("div",{className:"space-y-8",children:(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Loading..."})})})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,205,277,425,811],()=>s(38423));module.exports=r})();