1:"$Sreact.fragment"
2:I[4541,["4277","static/chunks/4277-df121688a085fe5d.js","6874","static/chunks/6874-0f660959a8ea8a33.js","7177","static/chunks/app/layout-0678a0255ef1110a.js"],"AuthProvider"]
3:I[360,["4277","static/chunks/4277-df121688a085fe5d.js","6874","static/chunks/6874-0f660959a8ea8a33.js","7177","static/chunks/app/layout-0678a0255ef1110a.js"],"default"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[2103,["4277","static/chunks/4277-df121688a085fe5d.js","6874","static/chunks/6874-0f660959a8ea8a33.js","7177","static/chunks/app/layout-0678a0255ef1110a.js"],"default"]
7:I[9228,["4277","static/chunks/4277-df121688a085fe5d.js","6874","static/chunks/6874-0f660959a8ea8a33.js","7177","static/chunks/app/layout-0678a0255ef1110a.js"],"default"]
9:I[9665,[],"MetadataBoundary"]
b:I[9665,[],"OutletBoundary"]
e:I[4911,[],"AsyncMetadataOutlet"]
10:I[9665,[],"ViewportBoundary"]
12:I[6614,[],""]
:HL["/_next/static/css/309242e93e39bb37.css","style"]
0:{"P":null,"b":"kHb1jnDwd7KlUyH4wGfam","p":"","c":["","admin"],"i":false,"f":[[["",{"children":["admin",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/309242e93e39bb37.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}],["$","meta",null,{"name":"theme-color","content":"#3b82f6"}],["$","meta",null,{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta",null,{"name":"apple-mobile-web-app-title","content":"Rollout Ready"}],["$","meta",null,{"name":"mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"msapplication-TileColor","content":"#3b82f6"}],["$","meta",null,{"name":"msapplication-tap-highlight","content":"no"}],["$","link",null,{"rel":"icon","href":"/icons/icon-192x192.svg"}],["$","link",null,{"rel":"apple-touch-icon","href":"/icons/icon-192x192.svg"}],["$","link",null,{"rel":"manifest","href":"/manifest.json"}]]}],["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased min-h-screen bg-background","children":["$","$L2",null,{"children":[["$","$L3",null,{}],["$","main",null,{"className":"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}],["$","$L7",null,{}]]}]}]]}]]}],{"children":["admin",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":["$L8",["$","$L9",null,{"children":"$La"}],null,["$","$Lb",null,{"children":["$Lc","$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","yGN-qNflaNNnI0KcqYJC4",{"children":[["$","$L10",null,{"children":"$L11"}],null]}],null]}],false]],"m":"$undefined","G":["$12","$undefined"],"s":false,"S":true}
13:"$Sreact.suspense"
14:I[4911,[],"AsyncMetadata"]
a:["$","$13",null,{"fallback":null,"children":["$","$L14",null,{"promise":"$@15"}]}]
d:null
11:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
15:{"metadata":[["$","title","0",{"children":"Rollout Ready"}],["$","meta","1",{"name":"description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","link","2",{"rel":"manifest","href":"/manifest.json","crossOrigin":"$undefined"}],["$","meta","3",{"name":"format-detection","content":"telephone=no"}],["$","meta","4",{"name":"mobile-web-app-capable","content":"yes"}],["$","meta","5",{"name":"apple-mobile-web-app-title","content":"Rollout Ready"}],["$","meta","6",{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta","7",{"property":"og:title","content":"Rollout Ready"}],["$","meta","8",{"property":"og:description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","meta","9",{"property":"og:site_name","content":"Rollout Ready"}],["$","meta","10",{"property":"og:type","content":"website"}],["$","meta","11",{"name":"twitter:card","content":"summary"}],["$","meta","12",{"name":"twitter:title","content":"Rollout Ready"}],["$","meta","13",{"name":"twitter:description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","link","14",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
f:{"metadata":"$15:metadata","error":null,"digest":"$undefined"}
16:I[4695,["4277","static/chunks/4277-df121688a085fe5d.js","6874","static/chunks/6874-0f660959a8ea8a33.js","3698","static/chunks/app/admin/page-a5c26eb21d12e06c.js"],"default"]
17:I[5182,["4277","static/chunks/4277-df121688a085fe5d.js","6874","static/chunks/6874-0f660959a8ea8a33.js","3698","static/chunks/app/admin/page-a5c26eb21d12e06c.js"],"default"]
18:I[6874,["4277","static/chunks/4277-df121688a085fe5d.js","6874","static/chunks/6874-0f660959a8ea8a33.js","3698","static/chunks/app/admin/page-a5c26eb21d12e06c.js"],""]
8:["$","div",null,{"className":"space-y-8","children":[["$","$L16",null,{"items":[{"label":"Admin"}]}],["$","$L17",null,{"title":"System Admin Dashboard","description":"Complete system administration and project management","showBackButton":false,"showHomeButton":true}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Total Roles"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-2xl font-bold","children":5}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Templates"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-2xl font-bold","children":3}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Active Projects"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-2xl font-bold","children":5}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Total Tasks"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-2xl font-bold","children":30}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Active Users"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-2xl font-bold","children":8}]}]]}]]}],["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-4 gap-6","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"Manage Users"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Create and manage system users"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","$L18",null,{"href":"/admin/users","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 w-full","children":"Manage Users"}]}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"Manage Roles"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Create and manage project roles"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","$L18",null,{"href":"/admin/roles","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 w-full","children":"Manage Roles"}]}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"Manage Templates"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Create and edit task templates"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","$L18",null,{"href":"/admin/templates","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 w-full","children":"Manage Templates"}]}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"Create Project"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Start a new implementation project"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","$L18",null,{"href":"/admin/projects/new","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 w-full","children":"Create Project"}]}]}]]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"Recent Projects"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Latest projects and their status"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"space-y-4","children":[["$","div","5",{"className":"flex items-center justify-between p-4 border rounded-lg","children":[["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold","children":"Deploy MES at Avonmouth"}],["$","p",null,{"className":"text-sm text-gray-600","children":["Start Date: ","01/02/2024"]}],["$","div",null,{"className":"flex gap-2 mt-2","children":[["$","span","12",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Business Analyst",": ","alice"]}],["$","span","11",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Security Architect",": ","bob"]}],["$","span","10",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Infrastructure Lead",": ","charlie"]}],["$","span","9",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Project Manager",": ","manager"]}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm text-gray-600","children":[19," tasks"]}],["$","$L18",null,{"href":"/admin/projects/5","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5 mt-2","children":"View Project"}]}]]}]]}],["$","div","4",{"className":"flex items-center justify-between p-4 border rounded-lg","children":[["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold","children":"Deploy MES at Avonmouth"}],["$","p",null,{"className":"text-sm text-gray-600","children":["Start Date: ","01/02/2024"]}],["$","div",null,{"className":"flex gap-2 mt-2","children":[["$","span","6",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Business Analyst",": ","alice"]}],["$","span","8",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Security Architect",": ","bob"]}],["$","span","5",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Infrastructure Lead",": ","charlie"]}],["$","span","7",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Project Manager",": ","manager"]}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm text-gray-600","children":[11," tasks"]}],["$","$L18",null,{"href":"/admin/projects/4","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5 mt-2","children":"View Project"}]}]]}]]}],["$","div","3",{"className":"flex items-center justify-between p-4 border rounded-lg","children":[["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold","children":"Solution Architect Project"}],["$","p",null,{"className":"text-sm text-gray-600","children":["Start Date: ","18/06/2025"]}],["$","div",null,{"className":"flex gap-2 mt-2","children":[["$","span","4",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Solution Architect",": ","oli"]}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm text-gray-600","children":[0," tasks"]}],["$","$L18",null,{"href":"/admin/projects/3","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5 mt-2","children":"View Project"}]}]]}]]}],["$","div","2",{"className":"flex items-center justify-between p-4 border rounded-lg","children":[["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold","children":"Factory Implementation"}],["$","p",null,{"className":"text-sm text-gray-600","children":["Start Date: ","18/06/2025"]}],["$","div",null,{"className":"flex gap-2 mt-2","children":[["$","span","2",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Business Analyst",": ","iroy"]}],["$","span","3",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Solution Architect",": ","owheeler"]}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm text-gray-600","children":[0," tasks"]}],["$","$L18",null,{"href":"/admin/projects/2","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5 mt-2","children":"View Project"}]}]]}]]}],["$","div","1",{"className":"flex items-center justify-between p-4 border rounded-lg","children":[["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold","children":"Test Project"}],["$","p",null,{"className":"text-sm text-gray-600","children":["Start Date: ","18/06/2025"]}],["$","div",null,{"className":"flex gap-2 mt-2","children":[["$","span","1",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":["Business Analyst",": ","iroy"]}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","p",null,{"className":"text-sm text-gray-600","children":[0," tasks"]}],["$","$L18",null,{"href":"/admin/projects/1","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5 mt-2","children":"View Project"}]}]]}]]}]]}]}]]}]]}]
