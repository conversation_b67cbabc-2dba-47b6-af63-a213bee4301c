{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/roles/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\n\n// GET /api/roles/[id] - Get role by ID\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const resolvedParams = await params;\n    const roleId = parseInt(resolvedParams.id);\n    \n    if (isNaN(roleId)) {\n      return NextResponse.json(\n        { message: \"Invalid role ID\" },\n        { status: 400 }\n      );\n    }\n\n    const role = await prisma.role.findUnique({\n      where: { id: roleId },\n      include: {\n        templates: {\n          include: {\n            _count: {\n              select: {\n                templateTasks: true,\n              },\n            },\n          },\n          orderBy: { name: 'asc' },\n        },\n        _count: {\n          select: {\n            templates: true,\n            projectRoles: true,\n          },\n        },\n      },\n    });\n\n    if (!role) {\n      return NextResponse.json(\n        { message: \"Role not found\" },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json(role);\n  } catch (error) {\n    console.error(\"Error fetching role:\", error);\n    return NextResponse.json(\n      { message: \"Failed to fetch role\" },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/roles/[id] - Update role\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const resolvedParams = await params;\n    const roleId = parseInt(resolvedParams.id);\n    \n    if (isNaN(roleId)) {\n      return NextResponse.json(\n        { message: \"Invalid role ID\" },\n        { status: 400 }\n      );\n    }\n\n    const body = await request.json();\n    const { name, description } = body;\n\n    if (!name || name.trim() === \"\") {\n      return NextResponse.json(\n        { message: \"Role name is required\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if role exists\n    const existingRole = await prisma.role.findUnique({\n      where: { id: roleId },\n    });\n\n    if (!existingRole) {\n      return NextResponse.json(\n        { message: \"Role not found\" },\n        { status: 404 }\n      );\n    }\n\n    // Check if another role with this name already exists (excluding current role)\n    const duplicateRole = await prisma.role.findFirst({\n      where: { \n        name: name.trim(),\n        id: { not: roleId },\n      },\n    });\n\n    if (duplicateRole) {\n      return NextResponse.json(\n        { message: \"A role with this name already exists\" },\n        { status: 400 }\n      );\n    }\n\n    // Update role\n    const updatedRole = await prisma.role.update({\n      where: { id: roleId },\n      data: {\n        name: name.trim(),\n        description: description?.trim() || null,\n      },\n      include: {\n        _count: {\n          select: {\n            templates: true,\n            projectRoles: true,\n          },\n        },\n      },\n    });\n\n    return NextResponse.json(updatedRole);\n  } catch (error) {\n    console.error(\"Error updating role:\", error);\n    return NextResponse.json(\n      { message: \"Failed to update role\" },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE /api/roles/[id] - Delete role\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const resolvedParams = await params;\n    const roleId = parseInt(resolvedParams.id);\n    \n    if (isNaN(roleId)) {\n      return NextResponse.json(\n        { message: \"Invalid role ID\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if role exists\n    const existingRole = await prisma.role.findUnique({\n      where: { id: roleId },\n      include: {\n        _count: {\n          select: {\n            projectRoles: true,\n            templates: true,\n          },\n        },\n      },\n    });\n\n    if (!existingRole) {\n      return NextResponse.json(\n        { message: \"Role not found\" },\n        { status: 404 }\n      );\n    }\n\n    // Check if role is being used in projects\n    if (existingRole._count.projectRoles > 0) {\n      return NextResponse.json(\n        { message: \"Cannot delete role that is currently used in projects\" },\n        { status: 400 }\n      );\n    }\n\n    // Delete role (this will cascade delete templates and template tasks)\n    await prisma.role.delete({\n      where: { id: roleId },\n    });\n\n    return NextResponse.json({ message: \"Role deleted successfully\" });\n  } catch (error) {\n    console.error(\"Error deleting role:\", error);\n    return NextResponse.json(\n      { message: \"Failed to delete role\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;QAEzC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAO;YACpB,SAAS;gBACP,WAAW;oBACT,SAAS;wBACP,QAAQ;4BACN,QAAQ;gCACN,eAAe;4BACjB;wBACF;oBACF;oBACA,SAAS;wBAAE,MAAM;oBAAM;gBACzB;gBACA,QAAQ;oBACN,QAAQ;wBACN,WAAW;wBACX,cAAc;oBAChB;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAuB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;QAEzC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG;QAE9B,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,IAAI;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAwB,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,+EAA+E;QAC/E,MAAM,gBAAgB,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAChD,OAAO;gBACL,MAAM,KAAK,IAAI;gBACf,IAAI;oBAAE,KAAK;gBAAO;YACpB;QACF;QAEA,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAuC,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,cAAc,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,aAAa,aAAa,UAAU;YACtC;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,WAAW;wBACX,cAAc;oBAChB;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;QAEzC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI;YAAO;YACpB,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,cAAc;wBACd,WAAW;oBACb;gBACF;YACF;QACF;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,0CAA0C;QAC1C,IAAI,aAAa,MAAM,CAAC,YAAY,GAAG,GAAG;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAwD,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,sEAAsE;QACtE,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAA4B;IAClE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}