"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[667],{285:(e,t,a)=>{a.d(t,{$:()=>d});var r=a(5155);a(2115);var s=a(9708),n=a(2085),i=a(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:n,className:t})),...l})}},2523:(e,t,a)=>{a.d(t,{p:()=>n});var r=a(5155);a(2115);var s=a(9434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},4963:(e,t,a)=>{a.d(t,{A:()=>o});var r=a(5155),s=a(2115),n=a(9409),i=a(6126);function o(e){let{value:t,onValueChange:a,placeholder:o="Select a user",disabled:d=!1,excludeUserIds:l=[],projectRoleName:c}=e,[u,v]=(0,s.useState)([]),[f,g]=(0,s.useState)(!0),[x,p]=(0,s.useState)("");(0,s.useEffect)(()=>{m()},[c,l]);let b=(e,t)=>{if(e.jobRole)return e.jobRole.name.toLowerCase()===t.toLowerCase();let a=t.toLowerCase();return a.includes("manager")||a.includes("lead")||a.includes("director")?["MANAGER","ADMIN"].includes(e.systemRole):"USER"===e.systemRole},m=async()=>{try{let e=await fetch("/api/users");if(e.ok){let t=(await e.json()).filter(e=>e.isActive&&!l.includes(e.id));c&&(t=t.filter(e=>b(e,c))),v(t)}else p("Failed to load users")}catch(e){p("Error loading users")}finally{g(!1)}},h=e=>e.firstName&&e.lastName?"".concat(e.firstName," ").concat(e.lastName," (@").concat(e.username,")"):"@".concat(e.username),j=e=>{switch(e){case"ADMIN":return"destructive";case"MANAGER":return"default";case"USER":return"secondary";default:return"outline"}};return f?(0,r.jsx)(n.l6,{disabled:!0,children:(0,r.jsx)(n.bq,{children:(0,r.jsx)(n.yv,{placeholder:"Loading users..."})})}):x?(0,r.jsx)(n.l6,{disabled:!0,children:(0,r.jsx)(n.bq,{children:(0,r.jsx)(n.yv,{placeholder:x})})}):(0,r.jsxs)(n.l6,{value:t?t.toString():"none",onValueChange:e=>{"none"===e?a(void 0):a(parseInt(e))},disabled:d,children:[(0,r.jsx)(n.bq,{children:(0,r.jsx)(n.yv,{placeholder:c?"Select user for ".concat(c," role"):o})}),(0,r.jsxs)(n.gC,{children:[(0,r.jsx)(n.eb,{value:"none",children:(0,r.jsx)("span",{className:"text-gray-500",children:"No user assigned"})}),u.map(e=>(0,r.jsx)(n.eb,{value:e.id.toString(),children:(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{children:h(e)}),e.jobRole&&(0,r.jsx)("span",{className:"text-xs text-gray-500",children:e.jobRole.name})]}),(0,r.jsxs)("div",{className:"flex gap-1",children:[e.jobRole&&(0,r.jsx)(i.E,{variant:"outline",className:"text-xs",children:e.jobRole.name}),(0,r.jsx)(i.E,{variant:j(e.systemRole),className:"text-xs",children:e.systemRole})]})]})},e.id))]})]})}},5695:(e,t,a)=>{var r=a(8999);a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},6126:(e,t,a)=>{a.d(t,{E:()=>d});var r=a(5155);a(2115);var s=a(9708),n=a(2085),i=a(9434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,asChild:n=!1,...d}=e,l=n?s.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:a}),t),...d})}},6695:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i});var r=a(5155);a(2115);var s=a(9434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},8979:(e,t,a)=>{a.d(t,{J:()=>d});var r=a(5155),s=a(2115),n=a(3655),i=s.forwardRef((e,t)=>(0,r.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=a(9434);function d(e){let{className:t,...a}=e;return(0,r.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},9409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>f,gC:()=>v,l6:()=>l,yv:()=>c});var r=a(5155);a(2115);var s=a(1396),n=a(6474),i=a(5196),o=a(7863),d=a(9434);function l(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:i,...o}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[i,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function v(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,r.jsx)(g,{}),(0,r.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(x,{})]})})}function f(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(o.A,{className:"size-4"})})}function x(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}},9434:(e,t,a)=>{a.d(t,{cn:()=>n});var r=a(2596),s=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}}]);