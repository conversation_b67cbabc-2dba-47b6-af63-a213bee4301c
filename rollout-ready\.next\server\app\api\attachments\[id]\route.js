(()=>{var e={};e.id=730,e.ids=[730],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(96330);let a=globalThis.prisma??new r.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49023:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{DELETE:()=>m,GET:()=>l});var a=s(96559),n=s(48088),i=s(37719),o=s(32190),u=s(5069),p=s(79748),d=s(33873),c=s(29021);async function l(e,{params:t}){try{let e=await t,s=parseInt(e.id);if(isNaN(s))return o.NextResponse.json({message:"Invalid attachment ID"},{status:400});let r=await u.z.taskAttachment.findUnique({where:{id:s}});if(!r)return o.NextResponse.json({message:"Attachment not found"},{status:404});let a=(0,d.join)(process.cwd(),"uploads","tasks",r.fileName);if(!(0,c.existsSync)(a))return o.NextResponse.json({message:"File not found on disk"},{status:404});let n=await (0,p.readFile)(a);return new o.NextResponse(n,{headers:{"Content-Type":r.mimeType,"Content-Disposition":`attachment; filename="${r.originalName}"`,"Content-Length":r.fileSize.toString()}})}catch(e){return console.error("Error downloading attachment:",e),o.NextResponse.json({message:"Failed to download attachment"},{status:500})}}async function m(e,{params:t}){try{let e=await t,r=parseInt(e.id);if(isNaN(r))return o.NextResponse.json({message:"Invalid attachment ID"},{status:400});let a=await u.z.taskAttachment.findUnique({where:{id:r}});if(!a)return o.NextResponse.json({message:"Attachment not found"},{status:404});let n=(0,d.join)(process.cwd(),"uploads","tasks",a.fileName);if((0,c.existsSync)(n)){let{unlink:e}=await Promise.resolve().then(s.t.bind(s,79748,23));await e(n)}return await u.z.taskAttachment.delete({where:{id:r}}),o.NextResponse.json({message:"Attachment deleted successfully"})}catch(e){return console.error("Error deleting attachment:",e),o.NextResponse.json({message:"Failed to delete attachment"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/attachments/[id]/route",pathname:"/api/attachments/[id]",filename:"route",bundlePath:"app/api/attachments/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\attachments\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:g}=h;function w(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79748:e=>{"use strict";e.exports=require("fs/promises")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(49023));module.exports=r})();