{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface SwitchProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  checked?: boolean\n  onCheckedChange?: (checked: boolean) => void\n}\n\nconst Switch = React.forwardRef<HTMLInputElement, SwitchProps>(\n  ({ className, checked, onCheckedChange, ...props }, ref) => {\n    return (\n      <label className=\"relative inline-flex items-center cursor-pointer\">\n        <input\n          type=\"checkbox\"\n          className=\"sr-only peer\"\n          ref={ref}\n          checked={checked}\n          onChange={(e) => onCheckedChange?.(e.target.checked)}\n          {...props}\n        />\n        <div\n          className={cn(\n            \"relative w-11 h-6 bg-gray-200 rounded-full peer peer-checked:bg-blue-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 transition-colors\",\n            \"after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all\",\n            \"peer-checked:after:translate-x-full peer-checked:after:border-white\",\n            className\n          )}\n        />\n      </label>\n    )\n  }\n)\nSwitch.displayName = \"Switch\"\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,OAAO,EAAE;IAClD,qBACE,8OAAC;QAAM,WAAU;;0BACf,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,KAAK;gBACL,SAAS;gBACT,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,OAAO;gBAClD,GAAG,KAAK;;;;;;0BAEX,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iKACA,oLACA,uEACA;;;;;;;;;;;;AAKV;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/PageHeader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useRouter } from \"next/navigation\";\nimport { Button } from \"@/components/ui/button\";\nimport { ArrowLeft, Home } from \"lucide-react\";\nimport Link from \"next/link\";\n\ninterface PageHeaderProps {\n  title: string;\n  description?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  showHomeButton?: boolean;\n  children?: React.ReactNode;\n}\n\nexport default function PageHeader({\n  title,\n  description,\n  showBackButton = true,\n  backUrl,\n  showHomeButton = false,\n  children,\n}: PageHeaderProps) {\n  const router = useRouter();\n\n  const handleBack = () => {\n    if (backUrl) {\n      router.push(backUrl);\n    } else {\n      router.back();\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Navigation Buttons */}\n      {(showBackButton || showHomeButton) && (\n        <div className=\"flex items-center gap-2\">\n          {showBackButton && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleBack}\n              className=\"flex items-center gap-2\"\n            >\n              <ArrowLeft className=\"h-4 w-4\" />\n              Back\n            </Button>\n          )}\n          {showHomeButton && (\n            <Link href=\"/\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"flex items-center gap-2\"\n              >\n                <Home className=\"h-4 w-4\" />\n                Home\n              </Button>\n            </Link>\n          )}\n        </div>\n      )}\n\n      {/* Page Title and Description */}\n      <div className=\"flex items-start justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">{title}</h1>\n          {description && (\n            <p className=\"text-gray-600 mt-2\">{description}</p>\n          )}\n        </div>\n        {children && (\n          <div className=\"flex items-center gap-2\">\n            {children}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAgBe,SAAS,WAAW,EACjC,KAAK,EACL,WAAW,EACX,iBAAiB,IAAI,EACrB,OAAO,EACP,iBAAiB,KAAK,EACtB,QAAQ,EACQ;IAChB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,CAAC,kBAAkB,cAAc,mBAChC,8OAAC;gBAAI,WAAU;;oBACZ,gCACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;oBAIpC,gCACC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;0BAStC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;4BACjD,6BACC,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;oBAGtC,0BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/Breadcrumb.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { ChevronRight, Home } from \"lucide-react\";\n\ninterface BreadcrumbItem {\n  label: string;\n  href?: string;\n}\n\ninterface BreadcrumbProps {\n  items: BreadcrumbItem[];\n  showHome?: boolean;\n}\n\nexport default function Breadcrumb({ items, showHome = true }: BreadcrumbProps) {\n  return (\n    <nav className=\"flex items-center space-x-1 text-sm text-gray-500 mb-4\">\n      {showHome && (\n        <>\n          <Link\n            href=\"/\"\n            className=\"flex items-center hover:text-gray-700 transition-colors\"\n          >\n            <Home className=\"h-4 w-4\" />\n            <span className=\"ml-1\">Home</span>\n          </Link>\n          {items.length > 0 && <ChevronRight className=\"h-4 w-4\" />}\n        </>\n      )}\n      \n      {items.map((item, index) => (\n        <div key={index} className=\"flex items-center\">\n          {item.href ? (\n            <Link\n              href={item.href}\n              className=\"hover:text-gray-700 transition-colors\"\n            >\n              {item.label}\n            </Link>\n          ) : (\n            <span className=\"text-gray-900 font-medium\">{item.label}</span>\n          )}\n          {index < items.length - 1 && (\n            <ChevronRight className=\"h-4 w-4 ml-1\" />\n          )}\n        </div>\n      ))}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAee,SAAS,WAAW,EAAE,KAAK,EAAE,WAAW,IAAI,EAAmB;IAC5E,qBACE,8OAAC;QAAI,WAAU;;YACZ,0BACC;;kCACE,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,mMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;oBAExB,MAAM,MAAM,GAAG,mBAAK,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;YAIhD,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oBAAgB,WAAU;;wBACxB,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,KAAK;;;;;iDAGb,8OAAC;4BAAK,WAAU;sCAA6B,KAAK,KAAK;;;;;;wBAExD,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;mBAZlB;;;;;;;;;;;AAkBlB", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/admin/users/%5Bid%5D/edit/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { notFound } from \"next/navigation\";\nimport Link from \"next/link\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport PageHeader from \"@/components/PageHeader\";\nimport Breadcrumb from \"@/components/Breadcrumb\";\n\ninterface Role {\n  id: number;\n  name: string;\n  description: string;\n}\n\ninterface User {\n  id: number;\n  username: string;\n  email: string;\n  firstName: string | null;\n  lastName: string | null;\n  systemRole: string;\n  jobRoleId: number | null;\n  isActive: boolean;\n}\n\ninterface UserEditProps {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\nexport default function EditUserPage({ params }: UserEditProps) {\n  const router = useRouter();\n  const [userId, setUserId] = useState<number | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [user, setUser] = useState<User | null>(null);\n  const [formData, setFormData] = useState({\n    username: \"\",\n    email: \"\",\n    firstName: \"\",\n    lastName: \"\",\n    systemRole: \"USER\",\n    jobRoleId: \"none\",\n    isActive: true,\n  });\n\n  // Resolve params and set userId\n  useEffect(() => {\n    const resolveParams = async () => {\n      const resolvedParams = await params;\n      const id = parseInt(resolvedParams.id);\n      if (isNaN(id)) {\n        notFound();\n      }\n      setUserId(id);\n    };\n    resolveParams();\n  }, [params]);\n\n  // Fetch user data and roles\n  useEffect(() => {\n    if (!userId) return;\n\n    const fetchData = async () => {\n      try {\n        // Fetch user data\n        const userResponse = await fetch(`/api/users/${userId}`);\n        if (!userResponse.ok) {\n          if (userResponse.status === 404) {\n            notFound();\n          }\n          throw new Error(\"Failed to fetch user\");\n        }\n        const userData = await userResponse.json();\n        setUser(userData);\n\n        // Set form data\n        setFormData({\n          username: userData.username || \"\",\n          email: userData.email || \"\",\n          firstName: userData.firstName || \"\",\n          lastName: userData.lastName || \"\",\n          systemRole: userData.systemRole || \"USER\",\n          jobRoleId: userData.jobRoleId ? userData.jobRoleId.toString() : \"none\",\n          isActive: userData.isActive ?? true,\n        });\n\n        // Fetch roles\n        const rolesResponse = await fetch(\"/api/roles\");\n        if (rolesResponse.ok) {\n          const rolesData = await rolesResponse.json();\n          setRoles(rolesData);\n        }\n      } catch (error) {\n        console.error(\"Failed to fetch data:\", error);\n        setError(\"Failed to load user data\");\n      }\n    };\n\n    fetchData();\n  }, [userId]);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSystemRoleChange = (value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      systemRole: value\n    }));\n  };\n\n  const handleJobRoleChange = (value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      jobRoleId: value\n    }));\n  };\n\n  const handleActiveChange = (checked: boolean) => {\n    setFormData(prev => ({\n      ...prev,\n      isActive: checked\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!userId) return;\n\n    setIsLoading(true);\n    setError(\"\");\n\n    try {\n      const response = await fetch(`/api/users/${userId}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          username: formData.username,\n          email: formData.email,\n          firstName: formData.firstName || null,\n          lastName: formData.lastName || null,\n          systemRole: formData.systemRole,\n          jobRoleId: formData.jobRoleId === \"none\" ? null : parseInt(formData.jobRoleId),\n          isActive: formData.isActive,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        router.push(`/admin/users/${userId}`);\n      } else {\n        setError(data.message || \"Failed to update user\");\n      }\n    } catch (err) {\n      setError(\"An error occurred. Please try again.\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!user) {\n    return <div>Loading...</div>;\n  }\n\n  const userDisplayName = user.firstName && user.lastName \n    ? `${user.firstName} ${user.lastName}`\n    : user.username;\n\n  return (\n    <div className=\"space-y-8\">\n      <Breadcrumb\n        items={[\n          { label: \"Admin\", href: \"/admin\" },\n          { label: \"Users\", href: \"/admin/users\" },\n          { label: userDisplayName, href: `/admin/users/${userId}` },\n          { label: \"Edit\" },\n        ]}\n      />\n      \n      <PageHeader\n        title={`Edit User: ${userDisplayName}`}\n        description=\"Update user information and permissions\"\n        backUrl={`/admin/users/${userId}`}\n      />\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>User Information</CardTitle>\n            <CardDescription>\n              Update user details and credentials\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"firstName\">First Name</Label>\n                <Input\n                  id=\"firstName\"\n                  name=\"firstName\"\n                  type=\"text\"\n                  value={formData.firstName}\n                  onChange={handleChange}\n                  placeholder=\"John\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"lastName\">Last Name</Label>\n                <Input\n                  id=\"lastName\"\n                  name=\"lastName\"\n                  type=\"text\"\n                  value={formData.lastName}\n                  onChange={handleChange}\n                  placeholder=\"Doe\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"username\">Username *</Label>\n              <Input\n                id=\"username\"\n                name=\"username\"\n                type=\"text\"\n                required\n                value={formData.username}\n                onChange={handleChange}\n                placeholder=\"johndoe\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email *</Label>\n              <Input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                value={formData.email}\n                onChange={handleChange}\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"systemRole\">System Role *</Label>\n                <Select value={formData.systemRole} onValueChange={handleSystemRoleChange}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select a system role\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"USER\">User - Can view and update assigned tasks</SelectItem>\n                    <SelectItem value=\"MANAGER\">Manager - Can create projects and assign users</SelectItem>\n                    <SelectItem value=\"ADMIN\">Admin - Full system access</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"jobRole\">Job Role</Label>\n                <Select value={formData.jobRoleId} onValueChange={handleJobRoleChange}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select a job role (optional)\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"none\">No specific job role</SelectItem>\n                    {roles.map((role) => (\n                      <SelectItem key={role.id} value={role.id.toString()}>\n                        {role.name}\n                        {role.description && ` - ${role.description}`}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                <p className=\"text-xs text-gray-500\">\n                  Job role determines which project roles this user can be assigned to\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Switch\n                id=\"isActive\"\n                checked={formData.isActive}\n                onCheckedChange={handleActiveChange}\n              />\n              <Label htmlFor=\"isActive\">Account is active</Label>\n            </div>\n          </CardContent>\n        </Card>\n\n        <div className=\"flex gap-4\">\n          <Button type=\"submit\" disabled={isLoading}>\n            {isLoading ? \"Updating...\" : \"Update User\"}\n          </Button>\n          <Link href={`/admin/users/${userId}`}>\n            <Button type=\"button\" variant=\"outline\">\n              Cancel\n            </Button>\n          </Link>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAsCe,SAAS,aAAa,EAAE,MAAM,EAAiB;IAC5D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,YAAY;QACZ,WAAW;QACX,UAAU;IACZ;IAEA,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,iBAAiB,MAAM;YAC7B,MAAM,KAAK,SAAS,eAAe,EAAE;YACrC,IAAI,MAAM,KAAK;gBACb,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;YACT;YACA,UAAU;QACZ;QACA;IACF,GAAG;QAAC;KAAO;IAEX,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,MAAM,YAAY;YAChB,IAAI;gBACF,kBAAkB;gBAClB,MAAM,eAAe,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;gBACvD,IAAI,CAAC,aAAa,EAAE,EAAE;oBACpB,IAAI,aAAa,MAAM,KAAK,KAAK;wBAC/B,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;oBACT;oBACA,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,WAAW,MAAM,aAAa,IAAI;gBACxC,QAAQ;gBAER,gBAAgB;gBAChB,YAAY;oBACV,UAAU,SAAS,QAAQ,IAAI;oBAC/B,OAAO,SAAS,KAAK,IAAI;oBACzB,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;oBAC/B,YAAY,SAAS,UAAU,IAAI;oBACnC,WAAW,SAAS,SAAS,GAAG,SAAS,SAAS,CAAC,QAAQ,KAAK;oBAChE,UAAU,SAAS,QAAQ,IAAI;gBACjC;gBAEA,cAAc;gBACd,MAAM,gBAAgB,MAAM,MAAM;gBAClC,IAAI,cAAc,EAAE,EAAE;oBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;oBAC1C,SAAS;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,SAAS;YACX;QACF;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,yBAAyB,CAAC;QAC9B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY;YACd,CAAC;IACH;IAEA,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW;YACb,CAAC;IACH;IAEA,MAAM,qBAAqB,CAAC;QAC1B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,UAAU;YACZ,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,QAAQ;QAEb,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,OAAO,SAAS,KAAK;oBACrB,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;oBAC/B,YAAY,SAAS,UAAU;oBAC/B,WAAW,SAAS,SAAS,KAAK,SAAS,OAAO,SAAS,SAAS,SAAS;oBAC7E,UAAU,SAAS,QAAQ;gBAC7B;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;YACtC,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,MAAM,kBAAkB,KAAK,SAAS,IAAI,KAAK,QAAQ,GACnD,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,KAAK,QAAQ;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAU;gBACT,OAAO;oBACL;wBAAE,OAAO;wBAAS,MAAM;oBAAS;oBACjC;wBAAE,OAAO;wBAAS,MAAM;oBAAe;oBACvC;wBAAE,OAAO;wBAAiB,MAAM,CAAC,aAAa,EAAE,QAAQ;oBAAC;oBACzD;wBAAE,OAAO;oBAAO;iBACjB;;;;;;0BAGH,8OAAC,gIAAA,CAAA,UAAU;gBACT,OAAO,CAAC,WAAW,EAAE,iBAAiB;gBACtC,aAAY;gBACZ,SAAS,CAAC,aAAa,EAAE,QAAQ;;;;;;0BAGnC,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;oCACpB,uBACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,SAAS;wDACzB,UAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,8OAAC,kIAAA,CAAA,SAAM;wDAAC,OAAO,SAAS,UAAU;wDAAE,eAAe;;0EACjD,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAO;;;;;;kFACzB,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAU;;;;;;kFAC5B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAQ;;;;;;;;;;;;;;;;;;;;;;;;0DAKhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,kIAAA,CAAA,SAAM;wDAAC,OAAO,SAAS,SAAS;wDAAE,eAAe;;0EAChD,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAO;;;;;;oEACxB,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,kIAAA,CAAA,aAAU;4EAAe,OAAO,KAAK,EAAE,CAAC,QAAQ;;gFAC9C,KAAK,IAAI;gFACT,KAAK,WAAW,IAAI,CAAC,GAAG,EAAE,KAAK,WAAW,EAAE;;2EAF9B,KAAK,EAAE;;;;;;;;;;;;;;;;;kEAO9B,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAMzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,QAAQ;gDAC1B,iBAAiB;;;;;;0DAEnB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAKhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU;0CAC7B,YAAY,gBAAgB;;;;;;0CAE/B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,aAAa,EAAE,QAAQ;0CAClC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD", "debugId": null}}]}