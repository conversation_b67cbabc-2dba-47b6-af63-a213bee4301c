{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"Table\",\n);\nexport const TableBody = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableBody\",\n);\nexport const TableCaption = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableCaption\",\n);\nexport const TableCell = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableCell\",\n);\nexport const TableFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableFooter\",\n);\nexport const TableHead = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableHead\",\n);\nexport const TableHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableHeader\",\n);\nexport const TableRow = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableRow\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6DACA", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"Table\",\n);\nexport const TableBody = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableBody\",\n);\nexport const TableCaption = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableCaption\",\n);\nexport const TableCell = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableCell\",\n);\nexport const TableFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableFooter\",\n);\nexport const TableHead = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableHead\",\n);\nexport const TableHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableHeader\",\n);\nexport const TableRow = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableRow\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yCACA", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/admin/projects/%5Bid%5D/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\nimport { prisma } from \"@/lib/db\";\nimport { notFound } from \"next/navigation\";\nimport type { Metadata } from \"next\";\n\ninterface ProjectDetailProps {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\nasync function getProject(id: number) {\n  return await prisma.project.findUnique({\n    where: { id },\n    include: {\n      projectRoles: {\n        include: {\n          role: true,\n          user: true,\n        },\n      },\n      projectTasks: {\n        include: {\n          projectRole: {\n            include: {\n              role: true,\n            user: true,\n            },\n          },\n          templateTask: true,\n        },\n        orderBy: [\n          { dueDate: 'asc' },\n          { createdAt: 'desc' },\n        ],\n      },\n      _count: {\n        select: {\n          projectTasks: true,\n        },\n      },\n    },\n  });\n}\n\nfunction getStatusBadge(status: string) {\n  switch (status) {\n    case 'TODO':\n      return <Badge variant=\"secondary\">To Do</Badge>;\n    case 'IN_PROGRESS':\n      return <Badge variant=\"default\">In Progress</Badge>;\n    case 'DONE':\n      return <Badge variant=\"outline\" className=\"bg-green-50 text-green-700 border-green-200\">Done</Badge>;\n    default:\n      return <Badge variant=\"secondary\">{status}</Badge>;\n  }\n}\n\nfunction isOverdue(dueDate: Date, status: string) {\n  return status !== 'DONE' && new Date(dueDate) < new Date();\n}\n\nexport async function generateMetadata({ params }: ProjectDetailProps): Promise<Metadata> {\n  const resolvedParams = await params;\n  const projectId = parseInt(resolvedParams.id);\n\n  if (isNaN(projectId)) {\n    return { title: 'Project Not Found - Rollout Ready' };\n  }\n\n  const project = await getProject(projectId);\n\n  if (!project) {\n    return { title: 'Project Not Found - Rollout Ready' };\n  }\n\n  return {\n    title: `${project.name} - Rollout Ready`,\n    description: project.description || `Project details for ${project.name}`,\n  };\n}\n\nexport default async function ProjectDetailPage({ params }: ProjectDetailProps) {\n  const resolvedParams = await params;\n  const projectId = parseInt(resolvedParams.id);\n  \n  if (isNaN(projectId)) {\n    notFound();\n  }\n\n  const project = await getProject(projectId);\n\n  if (!project) {\n    notFound();\n  }\n\n  const taskStats = {\n    total: project.projectTasks.length,\n    todo: project.projectTasks.filter(t => t.status === 'TODO').length,\n    inProgress: project.projectTasks.filter(t => t.status === 'IN_PROGRESS').length,\n    done: project.projectTasks.filter(t => t.status === 'DONE').length,\n    overdue: project.projectTasks.filter(t => t.status !== 'DONE' && new Date(t.dueDate) < new Date()).length,\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-start\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">{project.name}</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Started: {new Date(project.startDate).toLocaleDateString()}\n          </p>\n          {project.description && (\n            <p className=\"text-gray-600 mt-1\">{project.description}</p>\n          )}\n        </div>\n        <div className=\"flex gap-2\">\n          <Link href={`/admin/projects/${project.id}/edit`}>\n            <Button variant=\"outline\">Edit Project</Button>\n          </Link>\n          <Link href=\"/admin\">\n            <Button variant=\"outline\">Back to Admin</Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Project Stats */}\n      <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Tasks</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{taskStats.total}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">To Do</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-gray-600\">{taskStats.todo}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">In Progress</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-blue-600\">{taskStats.inProgress}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Completed</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-green-600\">{taskStats.done}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Overdue</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-red-600\">{taskStats.overdue}</div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Team Assignments */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Team Assignments</CardTitle>\n          <CardDescription>\n            Role assignments for this project\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {project.projectRoles.map((projectRole) => (\n              <div key={projectRole.id} className=\"p-4 border rounded-lg\">\n                <h3 className=\"font-semibold text-lg\">{projectRole.role.name}</h3>\n                <p className=\"text-gray-600\">{projectRole.user.firstName} {projectRole.user.lastName} ({projectRole.user.username})</p>\n                <div className=\"mt-2\">\n                  <Link href={`/dashboard/${projectRole.user.username}`}>\n                    <Button variant=\"outline\" size=\"sm\">\n                      View Tasks\n                    </Button>\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Project Tasks */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Project Tasks</CardTitle>\n          <CardDescription>\n            All tasks for this project organized by due date\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {project.projectTasks.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No tasks generated for this project yet</p>\n            </div>\n          ) : (\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Task</TableHead>\n                  <TableHead>Assigned To</TableHead>\n                  <TableHead>Role</TableHead>\n                  <TableHead>Due Date</TableHead>\n                  <TableHead>Status</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {project.projectTasks.map((task) => (\n                  <TableRow key={task.id} className={isOverdue(task.dueDate, task.status) ? 'bg-red-50' : ''}>\n                    <TableCell className=\"font-medium\">\n                      {task.description}\n                      {isOverdue(task.dueDate, task.status) && (\n                        <Badge variant=\"destructive\" className=\"ml-2 text-xs\">\n                          Overdue\n                        </Badge>\n                      )}\n                    </TableCell>\n                    <TableCell>{task.projectRole.user.firstName} {task.projectRole.user.lastName}</TableCell>\n                    <TableCell>\n                      <Badge variant=\"outline\">{task.projectRole.role.name}</Badge>\n                    </TableCell>\n                    <TableCell>\n                      {new Date(task.dueDate).toLocaleDateString()}\n                    </TableCell>\n                    <TableCell>\n                      {getStatusBadge(task.status)}\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AASA,eAAe,WAAW,EAAU;IAClC,OAAO,MAAM,gHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QACrC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,cAAc;gBACZ,SAAS;oBACP,MAAM;oBACN,MAAM;gBACR;YACF;YACA,cAAc;gBACZ,SAAS;oBACP,aAAa;wBACX,SAAS;4BACP,MAAM;4BACR,MAAM;wBACN;oBACF;oBACA,cAAc;gBAChB;gBACA,SAAS;oBACP;wBAAE,SAAS;oBAAM;oBACjB;wBAAE,WAAW;oBAAO;iBACrB;YACH;YACA,QAAQ;gBACN,QAAQ;oBACN,cAAc;gBAChB;YACF;QACF;IACF;AACF;AAEA,SAAS,eAAe,MAAc;IACpC,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAY;;;;;;QACpC,KAAK;YACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAU;;;;;;QAClC,KAAK;YACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAA8C;;;;;;QAC1F;YACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAa;;;;;;IACvC;AACF;AAEA,SAAS,UAAU,OAAa,EAAE,MAAc;IAC9C,OAAO,WAAW,UAAU,IAAI,KAAK,WAAW,IAAI;AACtD;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAsB;IACnE,MAAM,iBAAiB,MAAM;IAC7B,MAAM,YAAY,SAAS,eAAe,EAAE;IAE5C,IAAI,MAAM,YAAY;QACpB,OAAO;YAAE,OAAO;QAAoC;IACtD;IAEA,MAAM,UAAU,MAAM,WAAW;IAEjC,IAAI,CAAC,SAAS;QACZ,OAAO;YAAE,OAAO;QAAoC;IACtD;IAEA,OAAO;QACL,OAAO,GAAG,QAAQ,IAAI,CAAC,gBAAgB,CAAC;QACxC,aAAa,QAAQ,WAAW,IAAI,CAAC,oBAAoB,EAAE,QAAQ,IAAI,EAAE;IAC3E;AACF;AAEe,eAAe,kBAAkB,EAAE,MAAM,EAAsB;IAC5E,MAAM,iBAAiB,MAAM;IAC7B,MAAM,YAAY,SAAS,eAAe,EAAE;IAE5C,IAAI,MAAM,YAAY;QACpB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,UAAU,MAAM,WAAW;IAEjC,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,YAAY;QAChB,OAAO,QAAQ,YAAY,CAAC,MAAM;QAClC,MAAM,QAAQ,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QAClE,YAAY,QAAQ,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;QAC/E,MAAM,QAAQ,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QAClE,SAAS,QAAQ,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,IAAI,KAAK,EAAE,OAAO,IAAI,IAAI,QAAQ,MAAM;IAC3G;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC,QAAQ,IAAI;;;;;;0CAC9D,8OAAC;gCAAE,WAAU;;oCAAqB;oCACtB,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;4BAEzD,QAAQ,WAAW,kBAClB,8OAAC;gCAAE,WAAU;0CAAsB,QAAQ,WAAW;;;;;;;;;;;;kCAG1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;0CAC9C,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;0CAE5B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,UAAU,KAAK;;;;;;;;;;;;;;;;;kCAIxD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAoC,UAAU,IAAI;;;;;;;;;;;;;;;;;kCAIrE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAoC,UAAU,UAAU;;;;;;;;;;;;;;;;;kCAI3E,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAqC,UAAU,IAAI;;;;;;;;;;;;;;;;;kCAItE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAmC,UAAU,OAAO;;;;;;;;;;;;;;;;;;;;;;;0BAMzE,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,4BACzB,8OAAC;oCAAyB,WAAU;;sDAClC,8OAAC;4CAAG,WAAU;sDAAyB,YAAY,IAAI,CAAC,IAAI;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;;gDAAiB,YAAY,IAAI,CAAC,SAAS;gDAAC;gDAAE,YAAY,IAAI,CAAC,QAAQ;gDAAC;gDAAG,YAAY,IAAI,CAAC,QAAQ;gDAAC;;;;;;;sDAClH,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,WAAW,EAAE,YAAY,IAAI,CAAC,QAAQ,EAAE;0DACnD,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;8DAAK;;;;;;;;;;;;;;;;;mCALhC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;0BAiBhC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACT,QAAQ,YAAY,CAAC,MAAM,KAAK,kBAC/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;iDAG/B,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,8OAAC,iIAAA,CAAA,YAAS;8CACP,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC,iIAAA,CAAA,WAAQ;4CAAe,WAAW,UAAU,KAAK,OAAO,EAAE,KAAK,MAAM,IAAI,cAAc;;8DACtF,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;;wDAClB,KAAK,WAAW;wDAChB,UAAU,KAAK,OAAO,EAAE,KAAK,MAAM,mBAClC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAc,WAAU;sEAAe;;;;;;;;;;;;8DAK1D,8OAAC,iIAAA,CAAA,YAAS;;wDAAE,KAAK,WAAW,CAAC,IAAI,CAAC,SAAS;wDAAC;wDAAE,KAAK,WAAW,CAAC,IAAI,CAAC,QAAQ;;;;;;;8DAC5E,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,KAAK,WAAW,CAAC,IAAI,CAAC,IAAI;;;;;;;;;;;8DAEtD,8OAAC,iIAAA,CAAA,YAAS;8DACP,IAAI,KAAK,KAAK,OAAO,EAAE,kBAAkB;;;;;;8DAE5C,8OAAC,iIAAA,CAAA,YAAS;8DACP,eAAe,KAAK,MAAM;;;;;;;2CAjBhB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BxC", "debugId": null}}]}