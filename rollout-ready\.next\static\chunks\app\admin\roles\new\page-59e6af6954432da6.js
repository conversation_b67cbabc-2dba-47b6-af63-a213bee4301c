(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[885],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var n=r(5155);r(2115);var i=r(9708),s=r(2085),a=r(9434);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,asChild:o=!1,...d}=e,c=o?i.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,a.cn)(l({variant:r,size:s,className:t})),...d})}},968:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var n=r(2115),i=r(3655),s=r(5155),a=n.forwardRef((e,t)=>(0,s.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=a},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(2596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:l}=t,o=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let s=i(t)||i(n);return a[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return s(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(5155);r(2115);var i=r(9434);function s(e){let{className:t,type:r,...s}=e;return(0,n.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>o,sG:()=>l});var n=r(2115),i=r(7650),s=r(9708),a=r(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:i,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:t,{...s,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function o(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},4300:(e,t,r)=>{Promise.resolve().then(r.bind(r,8811))},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>a});var n=r(5155);r(2115);var i=r(968),s=r(9434);function a(e){let{className:t,...r}=e;return(0,n.jsx)(i.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>s});var n=r(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function a(...e){return n.useCallback(s(...e),e)}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>s,aR:()=>a});var n=r(5155);r(2115);var i=r(9434);function s(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function a(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...r})}},8811:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(5155),i=r(2115),s=r(5695),a=r(6874),l=r.n(a),o=r(6695),d=r(285),c=r(2523),u=r(5057);function p(){let e=(0,s.useRouter)(),[t,r]=(0,i.useState)(!1),[a,p]=(0,i.useState)({name:"",description:""}),f=async t=>{t.preventDefault(),r(!0);try{let t=await fetch("/api/roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(t.ok)e.push("/admin/roles");else{let e=await t.json();alert("Error: ".concat(e.message))}}catch(e){alert("An error occurred while creating the role")}finally{r(!1)}},m=e=>{p({...a,[e.target.name]:e.target.value})};return(0,n.jsxs)("div",{className:"max-w-2xl mx-auto space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Role"}),(0,n.jsx)("p",{className:"text-gray-600 mt-2",children:"Add a new project role to the system"})]}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{children:"Role Details"}),(0,n.jsx)(o.BT,{children:"Enter the basic information for the new role"})]}),(0,n.jsx)(o.Wu,{children:(0,n.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"name",children:"Role Name *"}),(0,n.jsx)(c.p,{id:"name",name:"name",type:"text",required:!0,value:a.name,onChange:m,placeholder:"e.g., Project Manager, Infrastructure Lead"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"description",children:"Description"}),(0,n.jsx)("textarea",{id:"description",name:"description",value:a.description,onChange:m,placeholder:"Brief description of this role's responsibilities",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,n.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,n.jsx)(d.$,{type:"submit",disabled:t,children:t?"Creating...":"Create Role"}),(0,n.jsx)(l(),{href:"/admin/roles",children:(0,n.jsx)(d.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})})]}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsx)(o.aR,{children:(0,n.jsx)(o.ZB,{children:"Next Steps"})}),(0,n.jsxs)(o.Wu,{children:[(0,n.jsx)("p",{className:"text-gray-600",children:"After creating this role, you can:"}),(0,n.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1 text-gray-600",children:[(0,n.jsx)("li",{children:"Create templates associated with this role"}),(0,n.jsx)("li",{children:"Assign this role to team members in projects"}),(0,n.jsx)("li",{children:"Set up auto-assignment rules for templates"})]})]})]})]})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(2596),i=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,TL:()=>a});var n=r(2115),i=r(6101),s=r(5155);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var a;let e,l,o=(a=r,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,i.t)(t,o):o),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,l=n.Children.toArray(i),o=l.find(d);if(o){let e=o.props.children,i=l.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var l=a("Slot"),o=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var t=t=>e(e.s=t);e.O(0,[277,874,441,684,358],()=>t(4300)),_N_E=e.O()}]);