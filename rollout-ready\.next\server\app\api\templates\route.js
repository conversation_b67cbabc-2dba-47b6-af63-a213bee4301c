(()=>{var e={};e.id=295,e.ids=[295],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(96330);let a=globalThis.prisma??new r.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59624:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var r={};s.r(r),s.d(r,{GET:()=>u,POST:()=>l});var a=s(96559),n=s(48088),i=s(37719),o=s(32190),p=s(5069);async function u(){try{let e=await p.z.template.findMany({include:{role:!0,templateTasks:!0,_count:{select:{templateTasks:!0}}},orderBy:[{role:{name:"asc"}},{name:"asc"}]});return o.NextResponse.json(e)}catch(e){return console.error("Error fetching templates:",e),o.NextResponse.json({message:"Failed to fetch templates"},{status:500})}}async function l(e){try{let{name:t,description:s,roleId:r,autoAssign:a,tasks:n}=await e.json();if(!t||""===t.trim())return o.NextResponse.json({message:"Template name is required"},{status:400});if(!r)return o.NextResponse.json({message:"Role is required"},{status:400});if(!n||0===n.length)return o.NextResponse.json({message:"At least one task is required"},{status:400});if(!await p.z.role.findUnique({where:{id:r}}))return o.NextResponse.json({message:"Role not found"},{status:400});let i=await p.z.template.create({data:{name:t.trim(),description:s?.trim()||null,roleId:r,autoAssign:a||!1}}),u=await Promise.all(n.map(e=>p.z.templateTask.create({data:{templateId:i.id,description:e.description.trim(),offsetDays:e.offsetDays||0,isRecurring:e.isRecurring||!1,isCritical:e.isCritical||!1}})));return o.NextResponse.json({template:i,templateTasks:u.length},{status:201})}catch(e){return console.error("Error creating template:",e),o.NextResponse.json({message:"Failed to create template"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/templates/route",pathname:"/api/templates",filename:"route",bundlePath:"app/api/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\templates\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:x}=c;function g(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(59624));module.exports=r})();