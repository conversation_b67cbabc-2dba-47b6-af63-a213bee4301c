(()=>{var e={};e.id=1295,e.ids=[1295],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59624:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>l});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),p=r(5069);async function u(){try{let e=await p.z.template.findMany({include:{role:!0,templateTasks:!0,_count:{select:{templateTasks:!0}}},orderBy:[{role:{name:"asc"}},{name:"asc"}]});return i.NextResponse.json(e)}catch(e){return console.error("Error fetching templates:",e),i.NextResponse.json({message:"Failed to fetch templates"},{status:500})}}async function l(e){try{let{name:t,description:r,roleId:s,autoAssign:a,tasks:n}=await e.json();if(!t||!s)return i.NextResponse.json({message:"Name and role are required"},{status:400});let o=await p.z.template.create({data:{name:t,description:r||null,roleId:parseInt(s),autoAssign:a||!1,templateTasks:{create:n?.map(e=>({description:e.description,offsetDays:e.offsetDays,isRecurring:e.isRecurring||!1,isCritical:e.isCritical||!1}))||[]}},include:{role:!0,templateTasks:!0}});return i.NextResponse.json(o,{status:201})}catch(e){return console.error("Error creating template:",e),i.NextResponse.json({message:"Failed to create template"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/templates/route",pathname:"/api/templates",filename:"route",bundlePath:"app/api/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\templates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:x}=c;function g(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(59624));module.exports=s})();