{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/tasks/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\n\ninterface RouteParams {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\nexport async function PATCH(request: NextRequest, { params }: RouteParams) {\n  try {\n    const resolvedParams = await params;\n    const taskId = parseInt(resolvedParams.id);\n    \n    if (isNaN(taskId)) {\n      return NextResponse.json(\n        { message: \"Invalid task ID\" },\n        { status: 400 }\n      );\n    }\n\n    const body = await request.json();\n    const { status, comments } = body;\n\n    if (!status || !['TODO', 'IN_PROGRESS', 'DONE'].includes(status)) {\n      return NextResponse.json(\n        { message: \"Invalid status. Must be TODO, IN_PROGRESS, or DONE\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if task exists\n    const existingTask = await prisma.projectTask.findUnique({\n      where: { id: taskId },\n    });\n\n    if (!existingTask) {\n      return NextResponse.json(\n        { message: \"Task not found\" },\n        { status: 404 }\n      );\n    }\n\n    // Update the task\n    const updatedTask = await prisma.projectTask.update({\n      where: { id: taskId },\n      data: {\n        status: status,\n        comments: comments?.trim() || null,\n        updatedAt: new Date(),\n      },\n      include: {\n        project: true,\n        projectRole: {\n          include: {\n            role: true,\n          },\n        },\n        templateTask: true,\n      },\n    });\n\n    return NextResponse.json(updatedTask);\n\n  } catch (error) {\n    console.error(\"Error updating task:\", error);\n    return NextResponse.json(\n      { message: \"Failed to update task\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAQO,eAAe,MAAM,OAAoB,EAAE,EAAE,MAAM,EAAe;IACvE,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;QAEzC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;QAE7B,IAAI,CAAC,UAAU,CAAC;YAAC;YAAQ;YAAe;SAAO,CAAC,QAAQ,CAAC,SAAS;YAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAqD,GAChE;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,UAAU,CAAC;YACvD,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,cAAc,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBACJ,QAAQ;gBACR,UAAU,UAAU,UAAU;gBAC9B,WAAW,IAAI;YACjB;YACA,SAAS;gBACP,SAAS;gBACT,aAAa;oBACX,SAAS;wBACP,MAAM;oBACR;gBACF;gBACA,cAAc;YAChB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}