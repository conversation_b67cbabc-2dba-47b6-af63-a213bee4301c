{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/tasks/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\n\ninterface RouteParams {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\n// GET /api/tasks/[id] - Get task by ID\nexport async function GET(request: NextRequest, { params }: RouteParams) {\n  try {\n    const resolvedParams = await params;\n    const taskId = parseInt(resolvedParams.id);\n\n    if (isNaN(taskId)) {\n      return NextResponse.json(\n        { message: \"Invalid task ID\" },\n        { status: 400 }\n      );\n    }\n\n    const task = await prisma.projectTask.findUnique({\n      where: { id: taskId },\n      include: {\n        project: {\n          select: {\n            id: true,\n            name: true,\n            description: true,\n            startDate: true\n          }\n        },\n        projectRole: {\n          include: {\n            role: {\n              select: {\n                id: true,\n                name: true,\n                description: true\n              }\n            },\n            user: {\n              select: {\n                id: true,\n                username: true,\n                firstName: true,\n                lastName: true\n              }\n            }\n          }\n        },\n        templateTask: {\n          select: {\n            id: true,\n            description: true,\n            isRecurring: true,\n            isCritical: true\n          }\n        }\n      }\n    });\n\n    if (!task) {\n      return NextResponse.json(\n        { message: \"Task not found\" },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json(task);\n  } catch (error) {\n    console.error(\"Error fetching task:\", error);\n    return NextResponse.json(\n      { message: \"Failed to fetch task\" },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function PATCH(request: NextRequest, { params }: RouteParams) {\n  try {\n    const resolvedParams = await params;\n    const taskId = parseInt(resolvedParams.id);\n\n    if (isNaN(taskId)) {\n      return NextResponse.json(\n        { message: \"Invalid task ID\" },\n        { status: 400 }\n      );\n    }\n\n    const body = await request.json();\n    const { status, comments, timeSpentMinutes, completedAt } = body;\n\n    if (status && !['TODO', 'IN_PROGRESS', 'DONE'].includes(status)) {\n      return NextResponse.json(\n        { message: \"Invalid status. Must be TODO, IN_PROGRESS, or DONE\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if task exists\n    const existingTask = await prisma.projectTask.findUnique({\n      where: { id: taskId },\n    });\n\n    if (!existingTask) {\n      return NextResponse.json(\n        { message: \"Task not found\" },\n        { status: 404 }\n      );\n    }\n\n    // Update the task\n    const updatedTask = await prisma.projectTask.update({\n      where: { id: taskId },\n      data: {\n        ...(status && { status }),\n        ...(comments !== undefined && { comments: comments?.trim() || null }),\n        ...(timeSpentMinutes !== undefined && { timeSpentMinutes }),\n        ...(completedAt !== undefined && { completedAt: completedAt ? new Date(completedAt) : null }),\n        updatedAt: new Date(),\n      },\n      include: {\n        project: true,\n        projectRole: {\n          include: {\n            role: true,\n          },\n        },\n        templateTask: true,\n      },\n    });\n\n    return NextResponse.json(updatedTask);\n\n  } catch (error) {\n    console.error(\"Error updating task:\", error);\n    return NextResponse.json(\n      { message: \"Failed to update task\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AASO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAAe;IACrE,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;QAEzC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,UAAU,CAAC;YAC/C,OAAO;gBAAE,IAAI;YAAO;YACpB,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,aAAa;wBACb,WAAW;oBACb;gBACF;gBACA,aAAa;oBACX,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,MAAM;gCACN,aAAa;4BACf;wBACF;wBACA,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,UAAU;gCACV,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;gBACF;gBACA,cAAc;oBACZ,QAAQ;wBACN,IAAI;wBACJ,aAAa;wBACb,aAAa;wBACb,YAAY;oBACd;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAuB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,MAAM,OAAoB,EAAE,EAAE,MAAM,EAAe;IACvE,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;QAEzC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,WAAW,EAAE,GAAG;QAE5D,IAAI,UAAU,CAAC;YAAC;YAAQ;YAAe;SAAO,CAAC,QAAQ,CAAC,SAAS;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAqD,GAChE;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,UAAU,CAAC;YACvD,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,cAAc,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBACJ,GAAI,UAAU;oBAAE;gBAAO,CAAC;gBACxB,GAAI,aAAa,aAAa;oBAAE,UAAU,UAAU,UAAU;gBAAK,CAAC;gBACpE,GAAI,qBAAqB,aAAa;oBAAE;gBAAiB,CAAC;gBAC1D,GAAI,gBAAgB,aAAa;oBAAE,aAAa,cAAc,IAAI,KAAK,eAAe;gBAAK,CAAC;gBAC5F,WAAW,IAAI;YACjB;YACA,SAAS;gBACP,SAAS;gBACT,aAAa;oBACX,SAAS;wBACP,MAAM;oBACR;gBACF;gBACA,cAAc;YAChB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}