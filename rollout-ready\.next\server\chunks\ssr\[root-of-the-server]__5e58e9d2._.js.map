{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"Table\",\n);\nexport const TableBody = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableBody\",\n);\nexport const TableCaption = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableCaption\",\n);\nexport const TableCell = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableCell\",\n);\nexport const TableFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableFooter\",\n);\nexport const TableHead = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableHead\",\n);\nexport const TableHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableHeader\",\n);\nexport const TableRow = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableRow\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6DACA", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"Table\",\n);\nexport const TableBody = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableBody\",\n);\nexport const TableCaption = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableCaption\",\n);\nexport const TableCell = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableCell\",\n);\nexport const TableFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableFooter\",\n);\nexport const TableHead = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableHead\",\n);\nexport const TableHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableHeader\",\n);\nexport const TableRow = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableRow\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yCACA", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/admin/templates/%5Bid%5D/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\nimport { prisma } from \"@/lib/db\";\nimport { notFound } from \"next/navigation\";\n\ninterface TemplateDetailProps {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\nasync function getTemplate(id: number) {\n  return await prisma.template.findUnique({\n    where: { id },\n    include: {\n      role: true,\n      templateTasks: {\n        orderBy: { offsetDays: 'asc' },\n      },\n      _count: {\n        select: {\n          templateTasks: true,\n        },\n      },\n    },\n  });\n}\n\nexport default async function TemplateDetailPage({ params }: TemplateDetailProps) {\n  const resolvedParams = await params;\n  const templateId = parseInt(resolvedParams.id);\n\n  if (isNaN(templateId)) {\n    notFound();\n  }\n\n  const template = await getTemplate(templateId);\n\n  if (!template) {\n    notFound();\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-start\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">{template.name}</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Template for {template.role.name}\n          </p>\n          {template.description && (\n            <p className=\"text-gray-600 mt-1\">{template.description}</p>\n          )}\n        </div>\n        <div className=\"flex gap-2\">\n          <Link href={`/admin/templates/${template.id}/edit`}>\n            <Button variant=\"outline\">Edit Template</Button>\n          </Link>\n          <Link href=\"/admin/templates\">\n            <Button variant=\"outline\">Back to Templates</Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Template Info */}\n      <div className=\"grid md:grid-cols-4 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Role</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <Badge variant=\"outline\" className=\"text-base px-3 py-1\">\n              {template.role.name}\n            </Badge>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Total Tasks</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-3xl font-bold\">{template._count.templateTasks}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Auto-Assign</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {template.autoAssign ? (\n              <Badge variant=\"default\" className=\"bg-green-100 text-green-800 border-green-200\">\n                Enabled\n              </Badge>\n            ) : (\n              <Badge variant=\"outline\">Disabled</Badge>\n            )}\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Critical Tasks</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-3xl font-bold text-red-600\">\n              {template.templateTasks.filter(t => t.isCritical).length}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Template Tasks */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Template Tasks</CardTitle>\n          <CardDescription>\n            Tasks that will be created when this template is applied to a project\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {template.templateTasks.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500 mb-4\">No tasks defined for this template</p>\n              <Link href={`/admin/templates/${template.id}/edit`}>\n                <Button>Add Tasks</Button>\n              </Link>\n            </div>\n          ) : (\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Task Description</TableHead>\n                  <TableHead>Timeline</TableHead>\n                  <TableHead>Due Date Offset</TableHead>\n                  <TableHead>Properties</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {template.templateTasks.map((task) => (\n                  <TableRow key={task.id}>\n                    <TableCell className=\"font-medium\">\n                      {task.description}\n                    </TableCell>\n                    <TableCell>\n                      <Badge variant=\"outline\">\n                        {task.offsetDays >= 0 ? `T+${task.offsetDays}` : `T${task.offsetDays}`} days\n                      </Badge>\n                    </TableCell>\n                    <TableCell>\n                      {task.offsetDays === 0 ? (\n                        <span className=\"text-blue-600 font-medium\">Project Start</span>\n                      ) : task.offsetDays > 0 ? (\n                        <span className=\"text-green-600\">{task.offsetDays} days after start</span>\n                      ) : (\n                        <span className=\"text-orange-600\">{Math.abs(task.offsetDays)} days before start</span>\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"flex gap-1\">\n                        {task.isCritical && (\n                          <Badge variant=\"destructive\" className=\"text-xs\">\n                            Critical\n                          </Badge>\n                        )}\n                        {task.isRecurring && (\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            Recurring\n                          </Badge>\n                        )}\n                        {!task.isCritical && !task.isRecurring && (\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            Standard\n                          </Badge>\n                        )}\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Timeline Visualization */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Task Timeline</CardTitle>\n          <CardDescription>\n            Visual representation of when tasks are due relative to project start\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {template.templateTasks\n              .sort((a, b) => a.offsetDays - b.offsetDays)\n              .map((task) => (\n                <div key={task.id} className=\"flex items-center gap-4\">\n                  <div className=\"w-20 text-right\">\n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      {task.offsetDays >= 0 ? `T+${task.offsetDays}` : `T${task.offsetDays}`}\n                    </Badge>\n                  </div>\n                  <div className=\"flex-1 p-3 border rounded-lg bg-gray-50\">\n                    <p className=\"font-medium\">{task.description}</p>\n                    <div className=\"flex gap-1 mt-1\">\n                      {task.isCritical && (\n                        <Badge variant=\"destructive\" className=\"text-xs\">Critical</Badge>\n                      )}\n                      {task.isRecurring && (\n                        <Badge variant=\"secondary\" className=\"text-xs\">Recurring</Badge>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAQA,eAAe,YAAY,EAAU;IACnC,OAAO,MAAM,gHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QACtC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,MAAM;YACN,eAAe;gBACb,SAAS;oBAAE,YAAY;gBAAM;YAC/B;YACA,QAAQ;gBACN,QAAQ;oBACN,eAAe;gBACjB;YACF;QACF;IACF;AACF;AAEe,eAAe,mBAAmB,EAAE,MAAM,EAAuB;IAC9E,MAAM,iBAAiB,MAAM;IAC7B,MAAM,aAAa,SAAS,eAAe,EAAE;IAE7C,IAAI,MAAM,aAAa;QACrB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,WAAW,MAAM,YAAY;IAEnC,IAAI,CAAC,UAAU;QACb,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC,SAAS,IAAI;;;;;;0CAC/D,8OAAC;gCAAE,WAAU;;oCAAqB;oCAClB,SAAS,IAAI,CAAC,IAAI;;;;;;;4BAEjC,SAAS,WAAW,kBACnB,8OAAC;gCAAE,WAAU;0CAAsB,SAAS,WAAW;;;;;;;;;;;;kCAG3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;0CAChD,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;0CAE5B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;;;;;;0CAEjC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAChC,SAAS,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;kCAKzB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;;;;;;0CAEjC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,SAAS,MAAM,CAAC,aAAa;;;;;;;;;;;;;;;;;kCAItE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;;;;;;0CAEjC,8OAAC,gIAAA,CAAA,cAAW;0CACT,SAAS,UAAU,iBAClB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAA+C;;;;;yDAIlF,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;kCAK/B,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;;;;;;0CAEjC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACT,SAAS,aAAa,CAAC,MAAM,KAAK,kBACjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;8CAChD,cAAA,8OAAC,kIAAA,CAAA,SAAM;kDAAC;;;;;;;;;;;;;;;;iDAIZ,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,8OAAC,iIAAA,CAAA,YAAS;8CACP,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,KAAK,WAAW;;;;;;8DAEnB,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;;4DACZ,KAAK,UAAU,IAAI,IAAI,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,UAAU,EAAE;4DAAC;;;;;;;;;;;;8DAG3E,8OAAC,iIAAA,CAAA,YAAS;8DACP,KAAK,UAAU,KAAK,kBACnB,8OAAC;wDAAK,WAAU;kEAA4B;;;;;+DAC1C,KAAK,UAAU,GAAG,kBACpB,8OAAC;wDAAK,WAAU;;4DAAkB,KAAK,UAAU;4DAAC;;;;;;6EAElD,8OAAC;wDAAK,WAAU;;4DAAmB,KAAK,GAAG,CAAC,KAAK,UAAU;4DAAE;;;;;;;;;;;;8DAGjE,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,UAAU,kBACd,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAc,WAAU;0EAAU;;;;;;4DAIlD,KAAK,WAAW,kBACf,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;0EAAU;;;;;;4DAIhD,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,WAAW,kBACpC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAU;;;;;;;;;;;;;;;;;;2CA/BtC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA8ClC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,aAAa,CACpB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,EAC1C,GAAG,CAAC,CAAC,qBACJ,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAChC,KAAK,UAAU,IAAI,IAAI,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,UAAU,EAAE;;;;;;;;;;;sDAG1E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAe,KAAK,WAAW;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,UAAU,kBACd,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAc,WAAU;sEAAU;;;;;;wDAElD,KAAK,WAAW,kBACf,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;mCAb7C,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBjC", "debugId": null}}]}