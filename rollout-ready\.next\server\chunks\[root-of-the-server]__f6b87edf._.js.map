{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { prisma } from './db';\nimport { SystemRole } from '@prisma/client';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';\nconst SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days\n\nexport interface AuthUser {\n  id: number;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  systemRole: SystemRole;\n}\n\nexport interface SessionData {\n  userId: number;\n  username: string;\n  systemRole: SystemRole;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token\nexport function generateToken(payload: SessionData): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });\n}\n\n// Verify JWT token\nexport function verifyToken(token: string): SessionData | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as SessionData;\n  } catch {\n    return null;\n  }\n}\n\n// Create user session\nexport async function createSession(userId: number): Promise<string> {\n  const user = await prisma.user.findUnique({\n    where: { id: userId },\n    select: { id: true, username: true, systemRole: true }\n  });\n\n  if (!user) {\n    throw new Error('User not found');\n  }\n\n  const token = generateToken({\n    userId: user.id,\n    username: user.username,\n    systemRole: user.systemRole\n  });\n\n  await prisma.session.create({\n    data: {\n      userId,\n      token,\n      expiresAt: new Date(Date.now() + SESSION_DURATION)\n    }\n  });\n\n  return token;\n}\n\n// Validate session\nexport async function validateSession(token: string): Promise<AuthUser | null> {\n  try {\n    // Check if session exists in database\n    const session = await prisma.session.findUnique({\n      where: { token },\n      include: { user: true }\n    });\n\n    if (!session || session.expiresAt < new Date()) {\n      // Clean up expired session\n      if (session) {\n        await prisma.session.delete({ where: { id: session.id } });\n      }\n      return null;\n    }\n\n    // Verify JWT token\n    const payload = verifyToken(token);\n    if (!payload) {\n      return null;\n    }\n\n    return {\n      id: session.user.id,\n      username: session.user.username,\n      email: session.user.email,\n      firstName: session.user.firstName || undefined,\n      lastName: session.user.lastName || undefined,\n      systemRole: session.user.systemRole\n    };\n  } catch {\n    return null;\n  }\n}\n\n// Logout (delete session)\nexport async function logout(token: string): Promise<void> {\n  await prisma.session.deleteMany({\n    where: { token }\n  });\n}\n\n// Clean up expired sessions\nexport async function cleanupExpiredSessions(): Promise<void> {\n  await prisma.session.deleteMany({\n    where: {\n      expiresAt: {\n        lt: new Date()\n      }\n    }\n  });\n}\n\n// Check if user has required system role\nexport function hasSystemRole(userRole: SystemRole, requiredRole: SystemRole): boolean {\n  const roleHierarchy = {\n    [SystemRole.USER]: 0,\n    [SystemRole.MANAGER]: 1,\n    [SystemRole.ADMIN]: 2\n  };\n\n  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n}\n\n// Get user's project roles\nexport async function getUserProjectRoles(userId: number) {\n  return prisma.projectRole.findMany({\n    where: { userId },\n    include: {\n      project: true,\n      role: true\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,mBAAmB,IAAI,KAAK,KAAK,KAAK,MAAM,SAAS;AAkBpD,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAoB;IAChD,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAe,cAAc,MAAc;IAChD,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE,IAAI;QAAO;QACpB,QAAQ;YAAE,IAAI;YAAM,UAAU;YAAM,YAAY;QAAK;IACvD;IAEA,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,cAAc;QAC1B,QAAQ,KAAK,EAAE;QACf,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;IAC7B;IAEA,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1B,MAAM;YACJ;YACA;YACA,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACnC;IACF;IAEA,OAAO;AACT;AAGO,eAAe,gBAAgB,KAAa;IACjD,IAAI;QACF,sCAAsC;QACtC,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE;YAAM;YACf,SAAS;gBAAE,MAAM;YAAK;QACxB;QAEA,IAAI,CAAC,WAAW,QAAQ,SAAS,GAAG,IAAI,QAAQ;YAC9C,2BAA2B;YAC3B,IAAI,SAAS;gBACX,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBAAE,OAAO;wBAAE,IAAI,QAAQ,EAAE;oBAAC;gBAAE;YAC1D;YACA,OAAO;QACT;QAEA,mBAAmB;QACnB,MAAM,UAAU,YAAY;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,OAAO;YACL,IAAI,QAAQ,IAAI,CAAC,EAAE;YACnB,UAAU,QAAQ,IAAI,CAAC,QAAQ;YAC/B,OAAO,QAAQ,IAAI,CAAC,KAAK;YACzB,WAAW,QAAQ,IAAI,CAAC,SAAS,IAAI;YACrC,UAAU,QAAQ,IAAI,CAAC,QAAQ,IAAI;YACnC,YAAY,QAAQ,IAAI,CAAC,UAAU;QACrC;IACF,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAe,OAAO,KAAa;IACxC,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC9B,OAAO;YAAE;QAAM;IACjB;AACF;AAGO,eAAe;IACpB,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC9B,OAAO;YACL,WAAW;gBACT,IAAI,IAAI;YACV;QACF;IACF;AACF;AAGO,SAAS,cAAc,QAAoB,EAAE,YAAwB;IAC1E,MAAM,gBAAgB;QACpB,CAAC,6HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,EAAE;QACnB,CAAC,6HAAA,CAAA,aAAU,CAAC,OAAO,CAAC,EAAE;QACtB,CAAC,6HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,EAAE;IACtB;IAEA,OAAO,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,aAAa;AAC/D;AAGO,eAAe,oBAAoB,MAAc;IACtD,OAAO,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;QACjC,OAAO;YAAE;QAAO;QAChB,SAAS;YACP,SAAS;YACT,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/users/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\nimport { hashPassword } from \"@/lib/auth\";\nimport { SystemRole } from \"@prisma/client\";\n\n// GET /api/users/[id] - Get user by ID\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const resolvedParams = await params;\n    const userId = parseInt(resolvedParams.id);\n    \n    if (isNaN(userId)) {\n      return NextResponse.json(\n        { message: \"Invalid user ID\" },\n        { status: 400 }\n      );\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstName: true,\n        lastName: true,\n        systemRole: true,\n        jobRoleId: true,\n        jobRole: {\n          select: {\n            id: true,\n            name: true,\n            description: true,\n          },\n        },\n        isActive: true,\n        createdAt: true,\n        projectRoles: {\n          include: {\n            role: true,\n            project: true,\n          },\n        },\n      },\n    });\n\n    if (!user) {\n      return NextResponse.json(\n        { message: \"User not found\" },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json(user);\n  } catch (error) {\n    console.error(\"Error fetching user:\", error);\n    return NextResponse.json(\n      { message: \"Failed to fetch user\" },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/users/[id] - Update user\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const resolvedParams = await params;\n    const userId = parseInt(resolvedParams.id);\n    \n    if (isNaN(userId)) {\n      return NextResponse.json(\n        { message: \"Invalid user ID\" },\n        { status: 400 }\n      );\n    }\n\n    const body = await request.json();\n    const { username, email, password, firstName, lastName, systemRole, jobRoleId, isActive } = body;\n\n    // Check if user exists\n    const existingUser = await prisma.user.findUnique({\n      where: { id: userId },\n    });\n\n    if (!existingUser) {\n      return NextResponse.json(\n        { message: \"User not found\" },\n        { status: 404 }\n      );\n    }\n\n    // Check for username/email conflicts (excluding current user)\n    if (username || email) {\n      const conflictUser = await prisma.user.findFirst({\n        where: {\n          AND: [\n            { id: { not: userId } },\n            {\n              OR: [\n                ...(username ? [{ username: username.toLowerCase() }] : []),\n                ...(email ? [{ email: email.toLowerCase() }] : []),\n              ],\n            },\n          ],\n        },\n      });\n\n      if (conflictUser) {\n        return NextResponse.json(\n          { message: \"Username or email already exists\" },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Validate job role if provided\n    if (jobRoleId !== undefined && jobRoleId !== null) {\n      const jobRole = await prisma.role.findUnique({\n        where: { id: parseInt(jobRoleId) }\n      });\n\n      if (!jobRole) {\n        return NextResponse.json(\n          { message: \"Invalid job role\" },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Prepare update data\n    const updateData: any = {};\n\n    if (username !== undefined) updateData.username = username.toLowerCase();\n    if (email !== undefined) updateData.email = email.toLowerCase();\n    if (firstName !== undefined) updateData.firstName = firstName?.trim() || null;\n    if (lastName !== undefined) updateData.lastName = lastName?.trim() || null;\n    if (systemRole !== undefined) updateData.systemRole = systemRole;\n    if (jobRoleId !== undefined) updateData.jobRoleId = jobRoleId ? parseInt(jobRoleId) : null;\n    if (isActive !== undefined) updateData.isActive = isActive;\n    \n    // Hash new password if provided\n    if (password && password.length >= 6) {\n      updateData.password = await hashPassword(password);\n    }\n\n    // Update user\n    const user = await prisma.user.update({\n      where: { id: userId },\n      data: updateData,\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstName: true,\n        lastName: true,\n        systemRole: true,\n        jobRoleId: true,\n        isActive: true,\n        createdAt: true,\n      },\n    });\n\n    return NextResponse.json(user);\n  } catch (error) {\n    console.error(\"Error updating user:\", error);\n    return NextResponse.json(\n      { message: \"Failed to update user\" },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE /api/users/[id] - Delete user (soft delete by setting isActive to false)\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const userId = parseInt(params.id);\n    \n    if (isNaN(userId)) {\n      return NextResponse.json(\n        { message: \"Invalid user ID\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if user exists\n    const existingUser = await prisma.user.findUnique({\n      where: { id: userId },\n    });\n\n    if (!existingUser) {\n      return NextResponse.json(\n        { message: \"User not found\" },\n        { status: 404 }\n      );\n    }\n\n    // Soft delete by setting isActive to false\n    await prisma.user.update({\n      where: { id: userId },\n      data: { isActive: false },\n    });\n\n    return NextResponse.json({ message: \"User deactivated successfully\" });\n  } catch (error) {\n    console.error(\"Error deleting user:\", error);\n    return NextResponse.json(\n      { message: \"Failed to delete user\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAIO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;QAEzC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAO;YACpB,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,YAAY;gBACZ,WAAW;gBACX,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,aAAa;oBACf;gBACF;gBACA,UAAU;gBACV,WAAW;gBACX,cAAc;oBACZ,SAAS;wBACP,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAuB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;QAEzC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;QAE5F,uBAAuB;QACvB,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,8DAA8D;QAC9D,IAAI,YAAY,OAAO;YACrB,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBAC/C,OAAO;oBACL,KAAK;wBACH;4BAAE,IAAI;gCAAE,KAAK;4BAAO;wBAAE;wBACtB;4BACE,IAAI;mCACE,WAAW;oCAAC;wCAAE,UAAU,SAAS,WAAW;oCAAG;iCAAE,GAAG,EAAE;mCACtD,QAAQ;oCAAC;wCAAE,OAAO,MAAM,WAAW;oCAAG;iCAAE,GAAG,EAAE;6BAClD;wBACH;qBACD;gBACH;YACF;YAEA,IAAI,cAAc;gBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;gBAAmC,GAC9C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,gCAAgC;QAChC,IAAI,cAAc,aAAa,cAAc,MAAM;YACjD,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC3C,OAAO;oBAAE,IAAI,SAAS;gBAAW;YACnC;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;gBAAmB,GAC9B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,sBAAsB;QACtB,MAAM,aAAkB,CAAC;QAEzB,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG,SAAS,WAAW;QACtE,IAAI,UAAU,WAAW,WAAW,KAAK,GAAG,MAAM,WAAW;QAC7D,IAAI,cAAc,WAAW,WAAW,SAAS,GAAG,WAAW,UAAU;QACzE,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG,UAAU,UAAU;QACtE,IAAI,eAAe,WAAW,WAAW,UAAU,GAAG;QACtD,IAAI,cAAc,WAAW,WAAW,SAAS,GAAG,YAAY,SAAS,aAAa;QACtF,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG;QAElD,gCAAgC;QAChC,IAAI,YAAY,SAAS,MAAM,IAAI,GAAG;YACpC,WAAW,QAAQ,GAAG,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;QAC3C;QAEA,cAAc;QACd,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;YACN,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,SAAS,SAAS,OAAO,EAAE;QAEjC,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAkB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,2CAA2C;QAC3C,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,UAAU;YAAM;QAC1B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAgC;IACtE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}