(()=>{var e={};e.id=734,e.ids=[734],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(5986),a=t(8974);function i(...e){return(0,a.QP)((0,s.$)(e))}},1195:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(5239),a=t(8088),i=t(8170),n=t.n(i),o=t(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["offline",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2016)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\offline\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\offline\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/offline/page",pathname:"/offline",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2016:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(7413),a=t(8963),i=t(3469);function n(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 px-4",children:(0,s.jsxs)(a.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(a.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2v20M2 12h20"})})}),(0,s.jsx)(a.ZB,{children:"You're Offline"}),(0,s.jsx)(a.BT,{children:"No internet connection detected. Some features may be limited."})]}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsx)("p",{className:"mb-2",children:"While offline, you can still:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{children:"View previously loaded tasks"}),(0,s.jsx)("li",{children:"Browse cached project data"}),(0,s.jsx)("li",{children:"Access your dashboard"})]})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsx)("p",{className:"mb-2",children:"When you're back online:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{children:"Your changes will sync automatically"}),(0,s.jsx)("li",{children:"New data will be updated"}),(0,s.jsx)("li",{children:"All features will be available"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 pt-4",children:[(0,s.jsx)(i.$,{onClick:()=>window.location.reload(),className:"w-full",children:"Try Again"}),(0,s.jsx)(i.$,{variant:"outline",onClick:()=>window.history.back(),className:"w-full",children:"Go Back"})]})]})]})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3469:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(7413);t(1120);var a=t(403),i=t(662),n=t(974);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:i=!1,...d}){let l=i?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:t,className:e})),...d})}},3873:e=>{"use strict";e.exports=require("path")},6487:()=>{},8335:()=>{},8963:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=t(7413);t(1120);var a=t(974);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,40,658,923,41],()=>t(1195));module.exports=s})();