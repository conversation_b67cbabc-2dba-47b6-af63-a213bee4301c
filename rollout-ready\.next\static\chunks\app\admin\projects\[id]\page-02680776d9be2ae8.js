(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[477],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(5155);r(2115);var a=r(9708),n=r(2085),l=r(9434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...o}=e,c=d?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(i({variant:r,size:n,className:t})),...o})}},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var s=r(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,l=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,d=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],s=null==i?void 0:i[e];if(null===t)return null;let n=a(t)||a(s);return l[e][n]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,d,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...o}[t]):({...i,...o})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2937:(e,t,r)=>{Promise.resolve().then(r.bind(r,7961))},5127:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>i,TableCell:()=>c,TableHead:()=>o,TableHeader:()=>l,TableRow:()=>d});var s=r(5155);r(2115);var a=r(9434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",t),...r})})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>n});var s=r(2115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function l(...e){return s.useCallback(n(...e),e)}},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(5155);r(2115);var a=r(9708),n=r(2085),l=r(9434);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,asChild:n=!1,...d}=e,o=n?a.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(i({variant:r}),t),...d})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>l});var s=r(5155);r(2115);var a=r(9434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}},7961:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var s=r(5155),a=r(2115),n=r(5695),l=r(6874),i=r.n(l),d=r(6695),o=r(285),c=r(6126),u=r(5127);function x(e){let{project:t}=e,r=(0,n.useRouter)(),[l,x]=(0,a.useState)(!1),[h,f]=(0,a.useState)(""),m=async()=>{if(confirm('Are you sure you want to delete the project "'.concat(t.name,'"? This will delete all associated tasks and cannot be undone.'))){x(!0),f("");try{let e=await fetch("/api/projects/".concat(t.id),{method:"DELETE"});if(e.ok)r.push("/admin");else{let t=await e.json();f(t.message||"Failed to delete project")}}catch(e){f("An error occurred while deleting the project")}finally{x(!1)}}},v={total:t.projectTasks.length,todo:t.projectTasks.filter(e=>"TODO"===e.status).length,inProgress:t.projectTasks.filter(e=>"IN_PROGRESS"===e.status).length,done:t.projectTasks.filter(e=>"DONE"===e.status).length,overdue:t.projectTasks.filter(e=>"DONE"!==e.status&&new Date(e.dueDate)<new Date).length},p=e=>{switch(e){case"TODO":return(0,s.jsx)(c.E,{variant:"outline",children:"To Do"});case"IN_PROGRESS":return(0,s.jsx)(c.E,{variant:"default",className:"bg-blue-100 text-blue-800 border-blue-200",children:"In Progress"});case"DONE":return(0,s.jsx)(c.E,{variant:"default",className:"bg-green-100 text-green-800 border-green-200",children:"Done"});default:return(0,s.jsx)(c.E,{variant:"outline",children:e})}};return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:t.name}),(0,s.jsxs)("p",{className:"text-gray-600 mt-2",children:["Started: ",new Date(t.startDate).toLocaleDateString()]}),t.description&&(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:t.description})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(i(),{href:"/admin/projects/".concat(t.id,"/edit"),children:(0,s.jsx)(o.$,{variant:"outline",children:"Edit Project"})}),(0,s.jsx)(o.$,{variant:"destructive",onClick:m,disabled:l,children:l?"Deleting...":"Delete Project"}),(0,s.jsx)(i(),{href:"/admin",children:(0,s.jsx)(o.$,{variant:"outline",children:"Back to Admin"})})]})]}),h&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:h}),(0,s.jsxs)("div",{className:"grid md:grid-cols-5 gap-6",children:[(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg",children:"Total Tasks"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold",children:v.total})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg",children:"To Do"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-600",children:v.todo})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg",children:"In Progress"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:v.inProgress})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg",children:"Completed"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold text-green-600",children:v.done})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg",children:"Overdue"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold text-red-600",children:v.overdue})})]})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsx)(d.ZB,{children:"Team Assignments"}),(0,s.jsx)(d.BT,{children:"Role assignments for this project"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:t.projectRoles.map(e=>(0,s.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:e.role.name}),(0,s.jsxs)("p",{className:"text-gray-600",children:[e.user.firstName," ",e.user.lastName," (",e.user.username,")"]}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)(i(),{href:"/dashboard/".concat(e.user.username),children:(0,s.jsx)(o.$,{variant:"outline",size:"sm",children:"View Tasks"})})})]},e.id))})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsx)(d.ZB,{children:"Project Tasks"}),(0,s.jsx)(d.BT,{children:"All tasks for this project"})]}),(0,s.jsx)(d.Wu,{children:0===t.projectTasks.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"No tasks found for this project"})}):(0,s.jsxs)(u.Table,{children:[(0,s.jsx)(u.TableHeader,{children:(0,s.jsxs)(u.TableRow,{children:[(0,s.jsx)(u.TableHead,{children:"Task"}),(0,s.jsx)(u.TableHead,{children:"Assigned To"}),(0,s.jsx)(u.TableHead,{children:"Role"}),(0,s.jsx)(u.TableHead,{children:"Due Date"}),(0,s.jsx)(u.TableHead,{children:"Status"}),(0,s.jsx)(u.TableHead,{children:"Actions"})]})}),(0,s.jsx)(u.TableBody,{children:t.projectTasks.map(e=>(0,s.jsxs)(u.TableRow,{children:[(0,s.jsx)(u.TableCell,{className:"font-medium",children:e.description}),(0,s.jsxs)(u.TableCell,{children:[e.projectRole.user.firstName," ",e.projectRole.user.lastName]}),(0,s.jsx)(u.TableCell,{children:(0,s.jsx)(c.E,{variant:"outline",children:e.projectRole.role.name})}),(0,s.jsx)(u.TableCell,{children:(0,s.jsx)("span",{className:new Date(e.dueDate)<new Date&&"DONE"!==e.status?"text-red-600 font-medium":"",children:new Date(e.dueDate).toLocaleDateString()})}),(0,s.jsx)(u.TableCell,{children:p(e.status)}),(0,s.jsx)(u.TableCell,{children:(0,s.jsx)(i(),{href:"/tasks/".concat(e.id),children:(0,s.jsx)(o.$,{variant:"outline",size:"sm",children:"View"})})})]},e.id))})]})})]})]})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,TL:()=>l});var s=r(2115),a=r(6101),n=r(5155);function l(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var l;let e,i,d=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),o=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(o.ref=t?(0,a.t)(t,d):d),s.cloneElement(r,o)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...l}=e,i=s.Children.toArray(a),d=i.find(o);if(d){let e=d.props.children,a=i.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...l,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),d=Symbol("radix.slottable");function o(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,8441,1684,7358],()=>t(2937)),_N_E=e.O()}]);