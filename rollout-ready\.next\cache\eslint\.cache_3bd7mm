[{"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\new\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\new\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\new\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\[id]\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\projects\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\roles\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\templates\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\dashboard\\[user]\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\offline\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ConnectionStatus.tsx": "15", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\PWAInstallPrompt.tsx": "16", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\badge.tsx": "17", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\button.tsx": "18", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\card.tsx": "19", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\input.tsx": "20", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\label.tsx": "21", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\select.tsx": "22", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx": "23", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\lib\\db.ts": "24", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\lib\\seed.ts": "25", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\lib\\utils.ts": "26", "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\types\\next-pwa.d.ts": "27"}, {"size": 5990, "mtime": 1750172331607, "results": "28", "hashOfConfig": "29"}, {"size": 5986, "mtime": 1750174297708, "results": "30", "hashOfConfig": "29"}, {"size": 3921, "mtime": 1750174308942, "results": "31", "hashOfConfig": "29"}, {"size": 4685, "mtime": 1750172356437, "results": "32", "hashOfConfig": "29"}, {"size": 10256, "mtime": 1750174330062, "results": "33", "hashOfConfig": "29"}, {"size": 7208, "mtime": 1750173708319, "results": "34", "hashOfConfig": "29"}, {"size": 7875, "mtime": 1750173926146, "results": "35", "hashOfConfig": "29"}, {"size": 2795, "mtime": 1750172837107, "results": "36", "hashOfConfig": "29"}, {"size": 1601, "mtime": 1750172400922, "results": "37", "hashOfConfig": "29"}, {"size": 2622, "mtime": 1750174341271, "results": "38", "hashOfConfig": "29"}, {"size": 6165, "mtime": 1750174351802, "results": "39", "hashOfConfig": "29"}, {"size": 3202, "mtime": 1750173554726, "results": "40", "hashOfConfig": "29"}, {"size": 2494, "mtime": 1750173456460, "results": "41", "hashOfConfig": "29"}, {"size": 3468, "mtime": 1750174220848, "results": "42", "hashOfConfig": "29"}, {"size": 1574, "mtime": 1750173531558, "results": "43", "hashOfConfig": "29"}, {"size": 3683, "mtime": 1750173488249, "results": "44", "hashOfConfig": "29"}, {"size": 1631, "mtime": 1750172196392, "results": "45", "hashOfConfig": "29"}, {"size": 2123, "mtime": 1750172196296, "results": "46", "hashOfConfig": "29"}, {"size": 1989, "mtime": 1750172196326, "results": "47", "hashOfConfig": "29"}, {"size": 967, "mtime": 1750172196331, "results": "48", "hashOfConfig": "29"}, {"size": 611, "mtime": 1750172196340, "results": "49", "hashOfConfig": "29"}, {"size": 6253, "mtime": 1750172196374, "results": "50", "hashOfConfig": "29"}, {"size": 2448, "mtime": 1750172196384, "results": "51", "hashOfConfig": "29"}, {"size": 308, "mtime": 1750172016668, "results": "52", "hashOfConfig": "29"}, {"size": 7828, "mtime": 1750172489651, "results": "53", "hashOfConfig": "29"}, {"size": 166, "mtime": 1750172171364, "results": "54", "hashOfConfig": "29"}, {"size": 542, "mtime": 1750174189957, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12qd8tj", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\projects\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\roles\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\templates\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\dashboard\\[user]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx", ["137"], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\offline\\page.tsx", ["138", "139"], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ConnectionStatus.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\PWAInstallPrompt.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\lib\\db.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\lib\\seed.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\types\\next-pwa.d.ts", [], [], {"ruleId": "140", "severity": 2, "message": "141", "line": 73, "column": 17, "nodeType": "142", "endLine": 73, "endColumn": 116}, {"ruleId": "143", "severity": 2, "message": "144", "line": 25, "column": 25, "nodeType": "145", "messageId": "146", "suggestions": "147"}, {"ruleId": "143", "severity": 2, "message": "144", "line": 41, "column": 41, "nodeType": "145", "messageId": "146", "suggestions": "148"}, "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["149", "150", "151", "152"], ["153", "154", "155", "156"], {"messageId": "157", "data": "158", "fix": "159", "desc": "160"}, {"messageId": "157", "data": "161", "fix": "162", "desc": "163"}, {"messageId": "157", "data": "164", "fix": "165", "desc": "166"}, {"messageId": "157", "data": "167", "fix": "168", "desc": "169"}, {"messageId": "157", "data": "170", "fix": "171", "desc": "160"}, {"messageId": "157", "data": "172", "fix": "173", "desc": "163"}, {"messageId": "157", "data": "174", "fix": "175", "desc": "166"}, {"messageId": "157", "data": "176", "fix": "177", "desc": "169"}, "replaceWithAlt", {"alt": "178"}, {"range": "179", "text": "180"}, "Replace with `&apos;`.", {"alt": "181"}, {"range": "182", "text": "183"}, "Replace with `&lsquo;`.", {"alt": "184"}, {"range": "185", "text": "186"}, "Replace with `&#39;`.", {"alt": "187"}, {"range": "188", "text": "189"}, "Replace with `&rsquo;`.", {"alt": "178"}, {"range": "190", "text": "191"}, {"alt": "181"}, {"range": "192", "text": "193"}, {"alt": "184"}, {"range": "194", "text": "195"}, {"alt": "187"}, {"range": "196", "text": "197"}, "&apos;", [1037, 1051], "You&apos;re Offline", "&lsquo;", [1037, 1051], "You&lsquo;re Offline", "&#39;", [1037, 1051], "You&#39;re Offline", "&rsquo;", [1037, 1051], "You&rsquo;re Offline", [1714, 1738], "When you&apos;re back online:", [1714, 1738], "When you&lsquo;re back online:", [1714, 1738], "When you&#39;re back online:", [1714, 1738], "When you&rsquo;re back online:"]