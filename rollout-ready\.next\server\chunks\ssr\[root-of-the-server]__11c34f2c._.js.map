{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"Table\",\n);\nexport const TableBody = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableBody\",\n);\nexport const TableCaption = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableCaption\",\n);\nexport const TableCell = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableCell\",\n);\nexport const TableFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableFooter\",\n);\nexport const TableHead = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableHead\",\n);\nexport const TableHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableHeader\",\n);\nexport const TableRow = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableRow\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6DACA", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"Table\",\n);\nexport const TableBody = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableBody\",\n);\nexport const TableCaption = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableCaption\",\n);\nexport const TableCell = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableCell\",\n);\nexport const TableFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableFooter\",\n);\nexport const TableHead = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableHead\",\n);\nexport const TableHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableHeader\",\n);\nexport const TableRow = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableRow\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yCACA", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/TaskUpdateButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TaskUpdateButton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TaskUpdateButton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/TaskUpdateButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TaskUpdateButton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TaskUpdateButton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/dashboard/%5Buser%5D/page.tsx"], "sourcesContent": ["import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\nimport { prisma } from \"@/lib/db\";\nimport TaskUpdateButton from \"@/components/TaskUpdateButton\";\n\ninterface UserDashboardProps {\n  params: Promise<{\n    user: string;\n  }>;\n}\n\nasync function getUserTasks(userName: string) {\n  return await prisma.projectTask.findMany({\n    where: {\n      projectRole: {\n        user: {\n          username: userName,\n        },\n      },\n    },\n    include: {\n      project: true,\n      projectRole: {\n        include: {\n          role: true,\n          user: true,\n        },\n      },\n      templateTask: {\n        select: {\n          isRecurring: true,\n          isCritical: true,\n        },\n      },\n    },\n    orderBy: [\n      { dueDate: 'asc' },\n      { createdAt: 'desc' },\n    ],\n  });\n}\n\nasync function getUserProjects(userName: string) {\n  return await prisma.project.findMany({\n    where: {\n      projectRoles: {\n        some: {\n          user: {\n            username: userN<PERSON>,\n          },\n        },\n      },\n    },\n    include: {\n      projectRoles: {\n        where: {\n          user: {\n            username: userName,\n          },\n        },\n        include: {\n          role: true,\n          user: true,\n        },\n      },\n      _count: {\n        select: {\n          projectTasks: {\n            where: {\n              projectRole: {\n                user: {\n                  username: userName,\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    orderBy: { startDate: 'desc' },\n  });\n}\n\nasync function getTaskStats(userName: string) {\n  const tasks = await getUserTasks(userName);\n  const projects = await getUserProjects(userName);\n\n  const stats = {\n    total: tasks.length,\n    todo: tasks.filter(t => t.status === 'TODO').length,\n    inProgress: tasks.filter(t => t.status === 'IN_PROGRESS').length,\n    done: tasks.filter(t => t.status === 'DONE').length,\n    overdue: tasks.filter(t => t.status !== 'DONE' && new Date(t.dueDate) < new Date()).length,\n    critical: tasks.filter(t => t.templateTask?.isCritical && t.status !== 'DONE').length,\n    projects: projects.length,\n  };\n\n  return { tasks, stats, projects };\n}\n\nfunction getStatusBadge(status: string) {\n  switch (status) {\n    case 'TODO':\n      return <Badge variant=\"secondary\">To Do</Badge>;\n    case 'IN_PROGRESS':\n      return <Badge variant=\"default\">In Progress</Badge>;\n    case 'DONE':\n      return <Badge variant=\"outline\" className=\"bg-green-50 text-green-700 border-green-200\">Done</Badge>;\n    default:\n      return <Badge variant=\"secondary\">{status}</Badge>;\n  }\n}\n\nfunction isOverdue(dueDate: Date, status: string) {\n  return status !== 'DONE' && new Date(dueDate) < new Date();\n}\n\nexport default async function UserDashboard({ params }: UserDashboardProps) {\n  const resolvedParams = await params;\n  const userName = decodeURIComponent(resolvedParams.user);\n  const { tasks, stats, projects } = await getTaskStats(userName);\n\n  return (\n    <div className=\"space-y-8\">\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900 capitalize\">\n          {userName}&apos;s Dashboard\n        </h1>\n        <p className=\"text-gray-600 mt-2\">\n          Your assigned tasks across all projects\n        </p>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-2 md:grid-cols-6 gap-4\">\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Tasks</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.total}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">To Do</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-gray-600\">{stats.todo}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">In Progress</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-blue-600\">{stats.inProgress}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Completed</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-green-600\">{stats.done}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Overdue</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-red-600\">{stats.overdue}</div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Projects</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-purple-600\">{stats.projects}</div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* My Projects */}\n      <Card>\n        <CardHeader>\n          <CardTitle>My Projects</CardTitle>\n          <CardDescription>\n            Projects where you have assigned roles\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {projects.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No projects assigned yet</p>\n            </div>\n          ) : (\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {projects.map((project) => (\n                <Card key={project.id} className=\"border-l-4 border-l-blue-500\">\n                  <CardHeader className=\"pb-2\">\n                    <CardTitle className=\"text-lg\">{project.name}</CardTitle>\n                    <CardDescription className=\"text-sm\">\n                      {project.description || 'No description'}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-2\">\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Your Role:</span>\n                        <Badge variant=\"outline\">\n                          {project.projectRoles[0]?.role.name}\n                        </Badge>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Your Tasks:</span>\n                        <span className=\"font-medium\">{project._count.projectTasks}</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Start Date:</span>\n                        <span>{new Date(project.startDate).toLocaleDateString()}</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Tasks Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Your Tasks</CardTitle>\n          <CardDescription>\n            All tasks assigned to you across projects\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {tasks.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No tasks assigned yet</p>\n            </div>\n          ) : (\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Task</TableHead>\n                  <TableHead>Project</TableHead>\n                  <TableHead>Role</TableHead>\n                  <TableHead>Due Date</TableHead>\n                  <TableHead>Status</TableHead>\n                  <TableHead>Actions</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {tasks.map((task) => (\n                  <TableRow key={task.id} className={isOverdue(task.dueDate, task.status) ? 'bg-red-50' : ''}>\n                    <TableCell className=\"font-medium\">\n                      <div className=\"flex items-center gap-2\">\n                        <a\n                          href={`/tasks/${task.id}`}\n                          className=\"text-blue-600 hover:text-blue-800 hover:underline\"\n                        >\n                          {task.description}\n                        </a>\n                        {task.templateTask?.isCritical && (\n                          <Badge variant=\"destructive\" className=\"text-xs\">\n                            Critical\n                          </Badge>\n                        )}\n                        {isOverdue(task.dueDate, task.status) && (\n                          <Badge variant=\"destructive\" className=\"text-xs\">\n                            Overdue\n                          </Badge>\n                        )}\n                      </div>\n                    </TableCell>\n                    <TableCell>{task.project.name}</TableCell>\n                    <TableCell>{task.projectRole.role.name}</TableCell>\n                    <TableCell>\n                      {new Date(task.dueDate).toLocaleDateString()}\n                    </TableCell>\n                    <TableCell>\n                      {getStatusBadge(task.status)}\n                    </TableCell>\n                    <TableCell>\n                      <TaskUpdateButton\n                        taskId={task.id}\n                        currentStatus={task.status}\n                      />\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAQA,eAAe,aAAa,QAAgB;IAC1C,OAAO,MAAM,gHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;QACvC,OAAO;YACL,aAAa;gBACX,MAAM;oBACJ,UAAU;gBACZ;YACF;QACF;QACA,SAAS;YACP,SAAS;YACT,aAAa;gBACX,SAAS;oBACP,MAAM;oBACN,MAAM;gBACR;YACF;YACA,cAAc;gBACZ,QAAQ;oBACN,aAAa;oBACb,YAAY;gBACd;YACF;QACF;QACA,SAAS;YACP;gBAAE,SAAS;YAAM;YACjB;gBAAE,WAAW;YAAO;SACrB;IACH;AACF;AAEA,eAAe,gBAAgB,QAAgB;IAC7C,OAAO,MAAM,gHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YACL,cAAc;gBACZ,MAAM;oBACJ,MAAM;wBACJ,UAAU;oBACZ;gBACF;YACF;QACF;QACA,SAAS;YACP,cAAc;gBACZ,OAAO;oBACL,MAAM;wBACJ,UAAU;oBACZ;gBACF;gBACA,SAAS;oBACP,MAAM;oBACN,MAAM;gBACR;YACF;YACA,QAAQ;gBACN,QAAQ;oBACN,cAAc;wBACZ,OAAO;4BACL,aAAa;gCACX,MAAM;oCACJ,UAAU;gCACZ;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEA,eAAe,aAAa,QAAgB;IAC1C,MAAM,QAAQ,MAAM,aAAa;IACjC,MAAM,WAAW,MAAM,gBAAgB;IAEvC,MAAM,QAAQ;QACZ,OAAO,MAAM,MAAM;QACnB,MAAM,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QACnD,YAAY,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;QAChE,MAAM,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QACnD,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,IAAI,KAAK,EAAE,OAAO,IAAI,IAAI,QAAQ,MAAM;QAC1F,UAAU,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,KAAK,QAAQ,MAAM;QACrF,UAAU,SAAS,MAAM;IAC3B;IAEA,OAAO;QAAE;QAAO;QAAO;IAAS;AAClC;AAEA,SAAS,eAAe,MAAc;IACpC,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAY;;;;;;QACpC,KAAK;YACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAU;;;;;;QAClC,KAAK;YACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAA8C;;;;;;QAC1F;YACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAa;;;;;;IACvC;AACF;AAEA,SAAS,UAAU,OAAa,EAAE,MAAc;IAC9C,OAAO,WAAW,UAAU,IAAI,KAAK,WAAW,IAAI;AACtD;AAEe,eAAe,cAAc,EAAE,MAAM,EAAsB;IACxE,MAAM,iBAAiB,MAAM;IAC7B,MAAM,WAAW,mBAAmB,eAAe,IAAI;IACvD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,aAAa;IAEtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;;4BACX;4BAAS;;;;;;;kCAEZ,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,MAAM,KAAK;;;;;;;;;;;;;;;;;kCAIpD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAoC,MAAM,IAAI;;;;;;;;;;;;;;;;;kCAIjE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAoC,MAAM,UAAU;;;;;;;;;;;;;;;;;kCAIvE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAqC,MAAM,IAAI;;;;;;;;;;;;;;;;;kCAIlE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAmC,MAAM,OAAO;;;;;;;;;;;;;;;;;kCAInE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAMzE,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACT,SAAS,MAAM,KAAK,kBACnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;iDAG/B,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,gIAAA,CAAA,OAAI;oCAAkB,WAAU;;sDAC/B,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,QAAQ,IAAI;;;;;;8DAC5C,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,QAAQ,WAAW,IAAI;;;;;;;;;;;;sDAG5B,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EACZ,QAAQ,YAAY,CAAC,EAAE,EAAE,KAAK;;;;;;;;;;;;kEAGnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAe,QAAQ,MAAM,CAAC,YAAY;;;;;;;;;;;;kEAE5D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;0EAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;mCArBlD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAiC/B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACT,MAAM,MAAM,KAAK,kBAChB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;iDAG/B,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,8OAAC,iIAAA,CAAA,YAAS;8CACP,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,iIAAA,CAAA,WAAQ;4CAAe,WAAW,UAAU,KAAK,OAAO,EAAE,KAAK,MAAM,IAAI,cAAc;;8DACtF,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DACnB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gEACzB,WAAU;0EAET,KAAK,WAAW;;;;;;4DAElB,KAAK,YAAY,EAAE,4BAClB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAc,WAAU;0EAAU;;;;;;4DAIlD,UAAU,KAAK,OAAO,EAAE,KAAK,MAAM,mBAClC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAc,WAAU;0EAAU;;;;;;;;;;;;;;;;;8DAMvD,8OAAC,iIAAA,CAAA,YAAS;8DAAE,KAAK,OAAO,CAAC,IAAI;;;;;;8DAC7B,8OAAC,iIAAA,CAAA,YAAS;8DAAE,KAAK,WAAW,CAAC,IAAI,CAAC,IAAI;;;;;;8DACtC,8OAAC,iIAAA,CAAA,YAAS;8DACP,IAAI,KAAK,KAAK,OAAO,EAAE,kBAAkB;;;;;;8DAE5C,8OAAC,iIAAA,CAAA,YAAS;8DACP,eAAe,KAAK,MAAM;;;;;;8DAE7B,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC,sIAAA,CAAA,UAAgB;wDACf,QAAQ,KAAK,EAAE;wDACf,eAAe,KAAK,MAAM;;;;;;;;;;;;2CAhCjB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CxC", "debugId": null}}]}