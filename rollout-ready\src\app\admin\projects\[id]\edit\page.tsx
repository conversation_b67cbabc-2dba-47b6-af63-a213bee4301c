import Link from "next/link";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { prisma } from "@/lib/db";
import { notFound } from "next/navigation";

interface ProjectEditProps {
  params: Promise<{
    id: string;
  }>;
}

async function getProject(id: number) {
  return await prisma.project.findUnique({
    where: { id },
    include: {
      projectRoles: {
        include: {
          role: true,
        },
      },
    },
  });
}

export default async function ProjectEditPage({ params }: ProjectEditProps) {
  const resolvedParams = await params;
  const projectId = parseInt(resolvedParams.id);
  
  if (isNaN(projectId)) {
    notFound();
  }

  const project = await getProject(projectId);

  if (!project) {
    notFound();
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Project: {project.name}</h1>
          <p className="text-gray-600 mt-2">
            Modify project details and team assignments
          </p>
        </div>
        <div className="flex gap-2">
          <Link href={`/admin/projects/${project.id}`}>
            <Button variant="outline">Back to Project</Button>
          </Link>
          <Link href="/admin">
            <Button variant="outline">Back to Admin</Button>
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Project Edit</CardTitle>
          <CardDescription>
            Project editing functionality is not yet implemented. This is a placeholder page.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold">Current Project Details:</h3>
              <p><strong>Name:</strong> {project.name}</p>
              <p><strong>Description:</strong> {project.description || 'No description'}</p>
              <p><strong>Start Date:</strong> {new Date(project.startDate).toLocaleDateString()}</p>
            </div>
            
            <div>
              <h3 className="font-semibold">Current Team:</h3>
              <ul className="list-disc list-inside">
                {project.projectRoles.map((pr) => (
                  <li key={pr.id}>
                    {pr.role.name}: {pr.userName}
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800">Coming Soon</h4>
              <p className="text-yellow-700">
                Project editing functionality will be implemented in a future update. 
                This will include the ability to:
              </p>
              <ul className="list-disc list-inside text-yellow-700 mt-2">
                <li>Update project name and description</li>
                <li>Modify start date</li>
                <li>Reassign team members to roles</li>
                <li>Add or remove team members</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
