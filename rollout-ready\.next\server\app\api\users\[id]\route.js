(()=>{var e={};e.id=100,e.ids=[100],e.modules={2098:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var t={};r.r(t),r.d(t,{DELETE:()=>p,GET:()=>l,PUT:()=>c});var a=r(96559),i=r(48088),n=r(37719),u=r(32190),o=r(5069),d=r(12909);async function l(e,{params:s}){try{let e=parseInt(s.id);if(isNaN(e))return u.NextResponse.json({message:"Invalid user ID"},{status:400});let r=await o.z.user.findUnique({where:{id:e},select:{id:!0,username:!0,email:!0,firstName:!0,lastName:!0,systemRole:!0,isActive:!0,createdAt:!0,projectRoles:{include:{role:!0,project:!0}}}});if(!r)return u.NextResponse.json({message:"User not found"},{status:404});return u.NextResponse.json(r)}catch(e){return console.error("Error fetching user:",e),u.NextResponse.json({message:"Failed to fetch user"},{status:500})}}async function c(e,{params:s}){try{let r=parseInt(s.id);if(isNaN(r))return u.NextResponse.json({message:"Invalid user ID"},{status:400});let{username:t,email:a,password:i,firstName:n,lastName:l,systemRole:c,isActive:p}=await e.json();if(!await o.z.user.findUnique({where:{id:r}}))return u.NextResponse.json({message:"User not found"},{status:404});if((t||a)&&await o.z.user.findFirst({where:{AND:[{id:{not:r}},{OR:[...t?[{username:t.toLowerCase()}]:[],...a?[{email:a.toLowerCase()}]:[]]}]}}))return u.NextResponse.json({message:"Username or email already exists"},{status:400});let m={};void 0!==t&&(m.username=t.toLowerCase()),void 0!==a&&(m.email=a.toLowerCase()),void 0!==n&&(m.firstName=n?.trim()||null),void 0!==l&&(m.lastName=l?.trim()||null),void 0!==c&&(m.systemRole=c),void 0!==p&&(m.isActive=p),i&&i.length>=6&&(m.password=await (0,d.Er)(i));let f=await o.z.user.update({where:{id:r},data:m,select:{id:!0,username:!0,email:!0,firstName:!0,lastName:!0,systemRole:!0,isActive:!0,createdAt:!0}});return u.NextResponse.json(f)}catch(e){return console.error("Error updating user:",e),u.NextResponse.json({message:"Failed to update user"},{status:500})}}async function p(e,{params:s}){try{let e=parseInt(s.id);if(isNaN(e))return u.NextResponse.json({message:"Invalid user ID"},{status:400});if(!await o.z.user.findUnique({where:{id:e}}))return u.NextResponse.json({message:"User not found"},{status:404});return await o.z.user.update({where:{id:e},data:{isActive:!1}}),u.NextResponse.json({message:"User deactivated successfully"})}catch(e){return console.error("Error deleting user:",e),u.NextResponse.json({message:"Failed to delete user"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/users/[id]/route",pathname:"/api/users/[id]",filename:"route",bundlePath:"app/api/users/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\users\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:x}=m;function y(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,s,r)=>{"use strict";r.d(s,{z:()=>a});var t=r(96330);let a=globalThis.prisma??new t.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,s,r)=>{"use strict";r.d(s,{BE:()=>d,Er:()=>o,dk:()=>c,jw:()=>l,ri:()=>p});var t=r(85663),a=r(43205),i=r.n(a),n=r(5069);r(96330);let u=process.env.JWT_SECRET||"your-secret-key-change-in-production";async function o(e){return t.Ay.hash(e,12)}async function d(e,s){return t.Ay.compare(e,s)}async function l(e){var s;let r=await n.z.user.findUnique({where:{id:e},select:{id:!0,username:!0,systemRole:!0}});if(!r)throw Error("User not found");let t=(s={userId:r.id,username:r.username,systemRole:r.systemRole},i().sign(s,u,{expiresIn:"7d"}));return await n.z.session.create({data:{userId:e,token:t,expiresAt:new Date(Date.now()+6048e5)}}),t}async function c(e){try{let s=await n.z.session.findUnique({where:{token:e},include:{user:!0}});if(!s||s.expiresAt<new Date)return s&&await n.z.session.delete({where:{id:s.id}}),null;if(!function(e){try{return i().verify(e,u)}catch{return null}}(e))return null;return{id:s.user.id,username:s.user.username,email:s.user.email,firstName:s.user.firstName||void 0,lastName:s.user.lastName||void 0,systemRole:s.user.systemRole}}catch{return null}}async function p(e){await n.z.session.deleteMany({where:{token:e}})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,580,315],()=>r(2098));module.exports=t})();