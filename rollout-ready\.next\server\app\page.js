(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,r,s)=>{"use strict";s.d(r,{cn:()=>n});var t=s(5986),a=s(8974);function n(...e){return(0,a.QP)((0,t.$)(e))}},1204:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var t=s(7413),a=s(4536),n=s.n(a),i=s(8963),d=s(3469);function o(){return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Welcome to Rollout Ready"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"A role-based checklist and task management system designed to ensure that no steps are missed during large-scale implementation projects."})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Admin Dashboard"}),(0,t.jsx)(i.BT,{children:"Manage roles, templates, and create new projects"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)(n(),{href:"/admin",children:(0,t.jsx)(d.$,{className:"w-full",children:"Go to Admin"})})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"User Dashboards"}),(0,t.jsx)(i.BT,{children:"View tasks assigned to specific team members"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n(),{href:"/dashboard/ollie",children:(0,t.jsx)(d.$,{variant:"outline",className:"w-full",children:"Ollie's Tasks"})}),(0,t.jsx)(n(),{href:"/dashboard/sarah",children:(0,t.jsx)(d.$,{variant:"outline",className:"w-full",children:"Sarah's Tasks"})})]})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Quick Start"}),(0,t.jsx)(i.BT,{children:"Get started with your first project"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)(n(),{href:"/admin/projects/new",children:(0,t.jsx)(d.$,{className:"w-full",children:"Create Project"})})})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Key Features"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Role-Based Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Assign specific roles to team members and automatically generate relevant tasks."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Template System"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Create reusable templates with predefined tasks and timelines."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Task Tracking"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Monitor progress with status updates and due date management."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"User Dashboards"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Personalized views showing only relevant tasks for each team member."})]})]})]})]})}},1328:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,5814,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3469:(e,r,s)=>{"use strict";s.d(r,{$:()=>o});var t=s(7413);s(1120);var a=s(403),n=s(662),i=s(974);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:r,size:s,asChild:n=!1,...o}){let l=n?a.DX:"button";return(0,t.jsx)(l,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:s,className:e})),...o})}},3873:e=>{"use strict";e.exports=require("path")},4536:(e,r,s)=>{let{createProxy:t}=s(9844);e.exports=t("C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\node_modules\\next\\dist\\client\\app-dir\\link.js")},7299:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l});var t=s(5239),a=s(8088),n=s(8170),i=s.n(n),d=s(893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(r,o);let l={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1204)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},7776:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},8963:(e,r,s)=>{"use strict";s.d(r,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>i});var t=s(7413);s(1120);var a=s(974);function n({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function d({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function o({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,40,658,814,923,41],()=>s(7299));module.exports=t})();