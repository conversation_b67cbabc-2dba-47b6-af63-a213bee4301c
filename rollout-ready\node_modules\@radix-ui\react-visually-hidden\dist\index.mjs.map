{"version": 3, "sources": ["../src/visually-hidden.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: 'absolute',\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: 'hidden',\n  clip: 'rect(0, 0, 0, 0)',\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}) satisfies React.CSSProperties;\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n  //\n  VISUALLY_HIDDEN_STYLES,\n};\nexport type { VisuallyHiddenProps };\n"], "mappings": ";AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AA6BpB;AAvBN,IAAM,yBAAyB,OAAO,OAAO;AAAA;AAAA,EAE3C,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU;AACZ,CAAC;AAED,IAAM,OAAO;AAMb,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAO,iBAAiB;AACvB,WACE;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACE,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,OAAO,EAAE,GAAG,wBAAwB,GAAG,MAAM,MAAM;AAAA;AAAA,IACrD;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAI7B,IAAM,OAAO;", "names": []}