(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[88,317,477,848],{5127:(e,t,a)=>{"use strict";a.d(t,{Table:()=>s,TableBody:()=>n,TableCell:()=>b,TableHead:()=>d,TableHeader:()=>o,TableRow:()=>c});var l=a(5155);a(2115);var r=a(9434);function s(e){let{className:t,...a}=e;return(0,l.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,l.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...a})})}function o(e){let{className:t,...a}=e;return(0,l.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...a})}function n(e){let{className:t,...a}=e;return(0,l.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...a})}function c(e){let{className:t,...a}=e;return(0,l.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function d(e){let{className:t,...a}=e;return(0,l.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function b(e){let{className:t,...a}=e;return(0,l.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},7468:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.bind(a,5127))},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var l=a(2596),r=a(9688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,l.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,277,441,684,358],()=>t(7468)),_N_E=e.O()}]);