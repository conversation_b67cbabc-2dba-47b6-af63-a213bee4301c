#!/usr/bin/env node

/**
 * Rollout Ready - Unified Deployment System
 * Cross-Platform Node.js Version
 * 
 * This is the SINGLE ENTRY POINT for all backend service launches.
 * All deployment modes (dev/prod) must use this unified start button.
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

function showHeader() {
    console.clear();
    console.log('');
    console.log(colorize('========================================', 'green'));
    console.log(colorize('   ROLLOUT READY - DEPLOYMENT CENTER', 'green'));
    console.log(colorize('========================================', 'green'));
    console.log('');
    console.log(colorize('🚀 Unified Start/Deploy Button', 'yellow'));
    console.log('');
}

function showMenu() {
    console.log(colorize('Select deployment mode:', 'cyan'));
    console.log('');
    console.log(colorize('[1] Development Mode (Hot Reload)', 'white'));
    console.log(colorize('[2] Production Mode (Optimized)', 'white'));
    console.log(colorize('[3] Build Only (No Server)', 'white'));
    console.log(colorize('[4] Database Setup (First Time)', 'white'));
    console.log(colorize('[5] Full Reset (Clean + Setup)', 'white'));
    console.log(colorize('[6] Exit', 'white'));
    console.log('');
}

function runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true,
            ...options
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Command failed with exit code ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

async function startDevelopment() {
    console.log('');
    console.log(colorize('🔧 Starting Development Mode...', 'yellow'));
    console.log(colorize('================================', 'yellow'));
    console.log('');

    try {
        console.log(colorize('Installing dependencies...', 'cyan'));
        await runCommand('npm', ['install']);

        console.log('');
        console.log(colorize('Setting up database...', 'cyan'));
        await runCommand('npx', ['prisma', 'db', 'push']);

        console.log('');
        console.log(colorize('Seeding database...', 'cyan'));
        try {
            await runCommand('npm', ['run', 'seed']);
        } catch (error) {
            console.log(colorize('⚠️ Database seeding failed, continuing anyway...', 'yellow'));
        }

        console.log('');
        console.log(colorize('🚀 Starting development server...', 'green'));
        console.log('');
        console.log(colorize('✅ Server will be available at: http://localhost:3000', 'green'));
        console.log(colorize('💡 Press Ctrl+C to stop the server', 'yellow'));
        console.log('');

        await runCommand('npm', ['run', 'dev']);
    } catch (error) {
        console.log(colorize(`❌ Error: ${error.message}`, 'red'));
        await waitForEnter();
    }
}

async function startProduction() {
    console.log('');
    console.log(colorize('🏭 Starting Production Mode...', 'yellow'));
    console.log(colorize('===============================', 'yellow'));
    console.log('');

    try {
        console.log(colorize('Installing dependencies...', 'cyan'));
        await runCommand('npm', ['install', '--production']);

        console.log('');
        console.log(colorize('Building application...', 'cyan'));
        await runCommand('npm', ['run', 'build']);

        console.log('');
        console.log(colorize('Setting up database...', 'cyan'));
        await runCommand('npx', ['prisma', 'db', 'push']);

        console.log('');
        console.log(colorize('Seeding database...', 'cyan'));
        try {
            await runCommand('npm', ['run', 'seed']);
        } catch (error) {
            console.log(colorize('⚠️ Database seeding failed, continuing anyway...', 'yellow'));
        }

        console.log('');
        console.log(colorize('🚀 Starting production server...', 'green'));
        console.log('');
        console.log(colorize('✅ Server will be available at: http://localhost:3000', 'green'));
        console.log(colorize('💡 Press Ctrl+C to stop the server', 'yellow'));
        console.log('');

        await runCommand('npm', ['start']);
    } catch (error) {
        console.log(colorize(`❌ Error: ${error.message}`, 'red'));
        await waitForEnter();
    }
}

async function buildOnly() {
    console.log('');
    console.log(colorize('🔨 Build Only Mode...', 'yellow'));
    console.log(colorize('====================', 'yellow'));
    console.log('');

    try {
        console.log(colorize('Installing dependencies...', 'cyan'));
        await runCommand('npm', ['install']);

        console.log('');
        console.log(colorize('Building application...', 'cyan'));
        await runCommand('npm', ['run', 'build']);

        console.log('');
        console.log(colorize('✅ Build completed successfully!', 'green'));
        console.log(colorize('📁 Built files are in the .next directory', 'cyan'));
        console.log('');
        await waitForEnter();
    } catch (error) {
        console.log(colorize(`❌ Error: ${error.message}`, 'red'));
        await waitForEnter();
    }
}

async function setupDatabase() {
    console.log('');
    console.log(colorize('🛠️ Database Setup Mode...', 'yellow'));
    console.log(colorize('=========================', 'yellow'));
    console.log('');

    try {
        console.log(colorize('Installing dependencies...', 'cyan'));
        await runCommand('npm', ['install']);

        console.log('');
        console.log(colorize('Setting up database schema...', 'cyan'));
        await runCommand('npx', ['prisma', 'db', 'push']);

        console.log('');
        console.log(colorize('Generating Prisma client...', 'cyan'));
        await runCommand('npx', ['prisma', 'generate']);

        console.log('');
        console.log(colorize('Seeding database with initial data...', 'cyan'));
        await runCommand('npm', ['run', 'seed']);

        console.log('');
        console.log(colorize('✅ Database setup completed successfully!', 'green'));
        console.log(colorize('📊 Demo users and data have been created', 'cyan'));
        console.log('');
        console.log(colorize('Default Admin Login:', 'yellow'));
        console.log(colorize('Username: admin', 'white'));
        console.log(colorize('Password: admin123', 'white'));
        console.log('');
        await waitForEnter();
    } catch (error) {
        console.log(colorize(`❌ Error: ${error.message}`, 'red'));
        await waitForEnter();
    }
}

async function fullReset() {
    console.log('');
    console.log(colorize('🔄 Full Reset Mode...', 'yellow'));
    console.log(colorize('====================', 'yellow'));
    console.log('');
    console.log(colorize('⚠️  WARNING: This will delete all data!', 'red'));
    console.log('');

    const confirm = await askQuestion("Are you sure? Type 'YES' to continue: ");
    if (confirm !== 'YES') {
        console.log(colorize('Reset cancelled.', 'yellow'));
        await waitForEnter();
        return;
    }

    try {
        console.log('');
        console.log(colorize('Cleaning node_modules...', 'cyan'));
        if (fs.existsSync('node_modules')) {
            await runCommand('rm', ['-rf', 'node_modules'], { shell: true });
        }

        console.log(colorize('Cleaning build files...', 'cyan'));
        if (fs.existsSync('.next')) {
            await runCommand('rm', ['-rf', '.next'], { shell: true });
        }
        if (fs.existsSync('dist')) {
            await runCommand('rm', ['-rf', 'dist'], { shell: true });
        }

        console.log(colorize('Cleaning database...', 'cyan'));
        if (fs.existsSync('prisma/dev.db')) {
            fs.unlinkSync('prisma/dev.db');
        }

        console.log('');
        console.log(colorize('Installing fresh dependencies...', 'cyan'));
        await runCommand('npm', ['install']);

        console.log('');
        console.log(colorize('Setting up fresh database...', 'cyan'));
        await runCommand('npx', ['prisma', 'db', 'push']);

        console.log('');
        console.log(colorize('Seeding fresh database...', 'cyan'));
        await runCommand('npm', ['run', 'seed']);

        console.log('');
        console.log(colorize('✅ Full reset completed successfully!', 'green'));
        console.log(colorize('🎉 System is ready for fresh start', 'cyan'));
        console.log('');
        await waitForEnter();
    } catch (error) {
        console.log(colorize(`❌ Error: ${error.message}`, 'red'));
        await waitForEnter();
    }
}

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

function waitForEnter() {
    return askQuestion('Press Enter to continue...');
}

async function main() {
    while (true) {
        showHeader();
        showMenu();

        const choice = await askQuestion('Enter your choice (1-6): ');

        switch (choice) {
            case '1':
                await startDevelopment();
                console.log('');
                console.log(colorize('🛑 Server stopped.', 'yellow'));
                console.log('');
                await waitForEnter();
                break;
            case '2':
                await startProduction();
                console.log('');
                console.log(colorize('🛑 Server stopped.', 'yellow'));
                console.log('');
                await waitForEnter();
                break;
            case '3':
                await buildOnly();
                break;
            case '4':
                await setupDatabase();
                break;
            case '5':
                await fullReset();
                break;
            case '6':
                console.log('');
                console.log(colorize('👋 Goodbye!', 'green'));
                console.log('');
                rl.close();
                process.exit(0);
            default:
                console.log('');
                console.log(colorize('❌ Invalid choice. Please select 1-6.', 'red'));
                console.log('');
                await waitForEnter();
                break;
        }
    }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
    console.log('');
    console.log(colorize('👋 Goodbye!', 'green'));
    console.log('');
    rl.close();
    process.exit(0);
});

// Start the application
main().catch((error) => {
    console.error(colorize(`Fatal error: ${error.message}`, 'red'));
    rl.close();
    process.exit(1);
});
