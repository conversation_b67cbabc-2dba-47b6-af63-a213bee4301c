import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, startDate, roleAssignments } = body;

    if (!name || name.trim() === "") {
      return NextResponse.json(
        { message: "Project name is required" },
        { status: 400 }
      );
    }

    if (!startDate) {
      return NextResponse.json(
        { message: "Start date is required" },
        { status: 400 }
      );
    }

    // Create the project
    const project = await prisma.project.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        startDate: new Date(startDate),
      },
    });

    // Create project roles for assigned users
    const projectRoles = [];
    for (const [roleIdStr, username] of Object.entries(roleAssignments)) {
      if (username && username.trim() !== "") {
        const roleId = parseInt(roleIdStr);

        // Find the user by username
        const user = await prisma.user.findUnique({
          where: { username: username.trim() },
        });

        if (!user) {
          return NextResponse.json(
            { message: `User "${username}" not found` },
            { status: 400 }
          );
        }

        const projectRole = await prisma.projectRole.create({
          data: {
            projectId: project.id,
            roleId: roleId,
            userId: user.id,
          },
        });
        projectRoles.push(projectRole);
      }
    }

    // Get templates that should be auto-assigned
    const autoAssignTemplates = await prisma.template.findMany({
      where: {
        autoAssign: true,
        roleId: {
          in: projectRoles.map(pr => pr.roleId),
        },
      },
      include: {
        templateTasks: true,
      },
    });

    // Generate project tasks from auto-assigned templates
    const projectTasks = [];
    for (const template of autoAssignTemplates) {
      const projectRole = projectRoles.find(pr => pr.roleId === template.roleId);
      if (projectRole) {
        for (const templateTask of template.templateTasks) {
          const dueDate = new Date(project.startDate);
          dueDate.setDate(dueDate.getDate() + templateTask.offsetDays);

          const projectTask = await prisma.projectTask.create({
            data: {
              projectId: project.id,
              templateTaskId: templateTask.id,
              projectRoleId: projectRole.id,
              description: templateTask.description,
              dueDate: dueDate,
              status: 'TODO',
            },
          });
          projectTasks.push(projectTask);
        }
      }
    }

    return NextResponse.json({
      project,
      projectRoles: projectRoles.length,
      projectTasks: projectTasks.length,
    }, { status: 201 });

  } catch (error) {
    console.error("Error creating project:", error);
    return NextResponse.json(
      { message: "Failed to create project" },
      { status: 500 }
    );
  }
}
