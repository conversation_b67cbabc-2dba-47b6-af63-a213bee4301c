{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/admin/projects/%5Bid%5D/edit/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport UserPicker from \"@/components/UserPicker\";\n\ninterface ProjectEditProps {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\ninterface Role {\n  id: number;\n  name: string;\n  description: string;\n}\n\ninterface Project {\n  id: number;\n  name: string;\n  description: string;\n  startDate: string;\n  projectRoles: {\n    id: number;\n    roleId: number;\n    userId: number;\n    role: Role;\n    user: {\n      id: number;\n      username: string;\n      firstName?: string;\n      lastName?: string;\n    };\n  }[];\n}\n\nexport default function ProjectEditPage({ params }: ProjectEditProps) {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [project, setProject] = useState<Project | null>(null);\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    description: \"\",\n    startDate: \"\",\n  });\n  const [roleAssignments, setRoleAssignments] = useState<Record<number, number | undefined>>({});\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadProjectData();\n  }, []);\n\n  const loadProjectData = async () => {\n    try {\n      const resolvedParams = await params;\n      const projectId = parseInt(resolvedParams.id);\n\n      if (isNaN(projectId)) {\n        setError(\"Invalid project ID\");\n        return;\n      }\n\n      // Load project data\n      const projectResponse = await fetch(`/api/projects/${projectId}`);\n      if (!projectResponse.ok) {\n        setError(\"Project not found\");\n        return;\n      }\n\n      const projectData = await projectResponse.json();\n      setProject(projectData);\n\n      // Set form data\n      setFormData({\n        name: projectData.name,\n        description: projectData.description || \"\",\n        startDate: projectData.startDate.split('T')[0], // Format for date input\n      });\n\n      // Set current role assignments\n      const assignments: Record<number, number | undefined> = {};\n      projectData.projectRoles.forEach((pr: any) => {\n        assignments[pr.roleId] = pr.userId;\n      });\n      setRoleAssignments(assignments);\n\n      // Load roles\n      const rolesResponse = await fetch(\"/api/roles\");\n      if (rolesResponse.ok) {\n        const rolesData = await rolesResponse.json();\n        setRoles(rolesData);\n      }\n    } catch (err) {\n      setError(\"Failed to load project data\");\n    }\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-start\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Edit Project: {project.name}</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Modify project details and team assignments\n          </p>\n        </div>\n        <div className=\"flex gap-2\">\n          <Link href={`/admin/projects/${project.id}`}>\n            <Button variant=\"outline\">Back to Project</Button>\n          </Link>\n          <Link href=\"/admin\">\n            <Button variant=\"outline\">Back to Admin</Button>\n          </Link>\n        </div>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Project Edit</CardTitle>\n          <CardDescription>\n            Project editing functionality is not yet implemented. This is a placeholder page.\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div>\n              <h3 className=\"font-semibold\">Current Project Details:</h3>\n              <p><strong>Name:</strong> {project.name}</p>\n              <p><strong>Description:</strong> {project.description || 'No description'}</p>\n              <p><strong>Start Date:</strong> {new Date(project.startDate).toLocaleDateString()}</p>\n            </div>\n            \n            <div>\n              <h3 className=\"font-semibold\">Current Team:</h3>\n              <ul className=\"list-disc list-inside\">\n                {project.projectRoles.map((pr) => (\n                  <li key={pr.id}>\n                    {pr.role.name}: {pr.userName}\n                  </li>\n                ))}\n              </ul>\n            </div>\n            \n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <h4 className=\"font-semibold text-yellow-800\">Coming Soon</h4>\n              <p className=\"text-yellow-700\">\n                Project editing functionality will be implemented in a future update. \n                This will include the ability to:\n              </p>\n              <ul className=\"list-disc list-inside text-yellow-700 mt-2\">\n                <li>Update project name and description</li>\n                <li>Modify start date</li>\n                <li>Reassign team members to roles</li>\n                <li>Add or remove team members</li>\n              </ul>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AA0Ce,SAAS,gBAAgB,EAAE,MAAM,EAAoB;;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,WAAW;IACb;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC,CAAC;IAC5F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,iBAAiB,MAAM;YAC7B,MAAM,YAAY,SAAS,eAAe,EAAE;YAE5C,IAAI,MAAM,YAAY;gBACpB,SAAS;gBACT;YACF;YAEA,oBAAoB;YACpB,MAAM,kBAAkB,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;YAChE,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBACvB,SAAS;gBACT;YACF;YAEA,MAAM,cAAc,MAAM,gBAAgB,IAAI;YAC9C,WAAW;YAEX,gBAAgB;YAChB,YAAY;gBACV,MAAM,YAAY,IAAI;gBACtB,aAAa,YAAY,WAAW,IAAI;gBACxC,WAAW,YAAY,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAChD;YAEA,+BAA+B;YAC/B,MAAM,cAAkD,CAAC;YACzD,YAAY,YAAY,CAAC,OAAO,CAAC,CAAC;gBAChC,WAAW,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM;YACpC;YACA,mBAAmB;YAEnB,aAAa;YACb,MAAM,gBAAgB,MAAM,MAAM;YAClC,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAAmC;oCAAe,QAAQ,IAAI;;;;;;;0CAC5E,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;0CACzC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;0CAE5B,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKhC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAc;gDAAE,QAAQ,IAAI;;;;;;;sDACvC,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAqB;gDAAE,QAAQ,WAAW,IAAI;;;;;;;sDACzD,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAoB;gDAAE,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;8CAGjF,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAG,WAAU;sDACX,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,mBACzB,6LAAC;;wDACE,GAAG,IAAI,CAAC,IAAI;wDAAC;wDAAG,GAAG,QAAQ;;mDADrB,GAAG,EAAE;;;;;;;;;;;;;;;;8CAOpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;sDAI/B,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GA9HwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}