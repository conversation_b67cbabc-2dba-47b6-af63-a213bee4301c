if(!self.define){let e,s={};const i=(i,c)=>(i=new URL(i+".js",c).href,s[i]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()}).then(()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e}));self.define=(c,a)=>{const n=e||("document"in self?document.currentScript.src:"")||location.href;if(s[n])return;let t={};const r=e=>i(e,n),o={module:{uri:n},exports:t,require:r};s[n]=Promise.all(c.map(e=>o[e]||r(e))).then(e=>(a(...e),t))}}define(["./workbox-cb477421"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"18443d566b428627d905b5ef02caa6af"},{url:"/_next/static/BPNIjeJbHzz8gtmHCOUmU/_buildManifest.js",revision:"b66e66c65fd85a4cfaea25e6f4a34483"},{url:"/_next/static/BPNIjeJbHzz8gtmHCOUmU/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/232-7343ba8c62855626.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/277-9852fd43da8910cd.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/341.df3329d77a5faa19.js",revision:"df3329d77a5faa19"},{url:"/_next/static/chunks/472.a3826d29d6854395.js",revision:"a3826d29d6854395"},{url:"/_next/static/chunks/4bd1b696-949528373f925410.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/684-cd400e2aa496ccd7.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/874-01f110b4d2036f39.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/_not-found/page-9323410865d62f25.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/admin/page-2687bc48a84d3b1f.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/admin/projects/new/page-3e762a0b8cec4499.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/admin/roles/new/page-59e6af6954432da6.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/admin/roles/page-e19c24c99d6dd770.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/admin/templates/%5Bid%5D/page-312dc855623452b4.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/admin/templates/new/page-78a6746dd72e8b94.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/admin/templates/page-bcf1ca3ebd9fc89c.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/api/projects/route-8cebe9b69f7d28d9.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/api/roles/route-e71e3b081daef3fa.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/api/templates/route-1cee708aac08953f.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/dashboard/%5Buser%5D/page-97be9ca16f632997.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/layout-1a9a99ea145db334.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/offline/page-b10c1d2ed92b8663.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/app/page-f5fd57aecce18a55.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/framework-f593a28cde54158e.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/main-0c140a19a364156c.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/main-app-a9eed8468c4c4736.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/pages/_app-92f2aae776f86b9c.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/pages/_error-71d2b6a7b832d02a.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-7830a1e56b896d24.js",revision:"BPNIjeJbHzz8gtmHCOUmU"},{url:"/_next/static/css/1dfb4ed24b68eb58.css",revision:"1dfb4ed24b68eb58"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/747892c23ea88013-s.woff2",revision:"a0761690ccf4441ace5cec893b82d4ab"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/93f479601ee12b01-s.p.woff2",revision:"da83d5f06d825c5ae65b7cca706cb312"},{url:"/_next/static/media/9610d9e46709d722-s.woff2",revision:"7b7c0ef93df188a852344fc272fc096b"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/file.svg",revision:"d09f95206c3fa0bb9bd9fefabfd0ea71"},{url:"/globe.svg",revision:"2aaafa6a49b6563925fe440891e32717"},{url:"/icons/icon-128x128.png.svg",revision:"91e0b41b71332177b5e65fff61f77b54"},{url:"/icons/icon-128x128.svg",revision:"91e0b41b71332177b5e65fff61f77b54"},{url:"/icons/icon-144x144.png.svg",revision:"15c0c472642a64ac90e299779ec2e8fa"},{url:"/icons/icon-144x144.svg",revision:"15c0c472642a64ac90e299779ec2e8fa"},{url:"/icons/icon-152x152.png.svg",revision:"37affca5822161e0848c5ef145131c1c"},{url:"/icons/icon-152x152.svg",revision:"37affca5822161e0848c5ef145131c1c"},{url:"/icons/icon-192x192.png.svg",revision:"4746a11e8967868b31edca0d0c00bb58"},{url:"/icons/icon-192x192.svg",revision:"4746a11e8967868b31edca0d0c00bb58"},{url:"/icons/icon-384x384.png.svg",revision:"35be52350e4cd06ba64c9a5224d69d34"},{url:"/icons/icon-384x384.svg",revision:"35be52350e4cd06ba64c9a5224d69d34"},{url:"/icons/icon-512x512.png.svg",revision:"7ee38e29fcf4dec4ea8e239c258127bd"},{url:"/icons/icon-512x512.svg",revision:"7ee38e29fcf4dec4ea8e239c258127bd"},{url:"/icons/icon-72x72.png.svg",revision:"d500e2b8aa1e4983be4fe8cce5a9d27a"},{url:"/icons/icon-72x72.svg",revision:"d500e2b8aa1e4983be4fe8cce5a9d27a"},{url:"/icons/icon-96x96.png.svg",revision:"964751215f922f7d264d5390de7cd696"},{url:"/icons/icon-96x96.svg",revision:"964751215f922f7d264d5390de7cd696"},{url:"/manifest.json",revision:"d96e727bd68efdded4a04da494d6c536"},{url:"/next.svg",revision:"8e061864f388b47f33a1c3780831193e"},{url:"/vercel.svg",revision:"c0af2f507b369b085b35ef4bbe3bcf1e"},{url:"/window.svg",revision:"a2760511c65806022ad20adf74370ff3"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:i,state:c})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https?.*/,new e.NetworkFirst({cacheName:"offlineCache",plugins:[new e.ExpirationPlugin({maxEntries:200})]}),"GET")});
