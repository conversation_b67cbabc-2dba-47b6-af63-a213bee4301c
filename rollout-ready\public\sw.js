if(!self.define){let e,s={};const i=(i,a)=>(i=new URL(i+".js",a).href,s[i]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()}).then(()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e}));self.define=(a,n)=>{const c=e||("document"in self?document.currentScript.src:"")||location.href;if(s[c])return;let r={};const t=e=>i(e,c),o={module:{uri:c},exports:r,require:t};s[c]=Promise.all(a.map(e=>o[e]||t(e))).then(e=>(n(...e),r))}}define(["./workbox-cb477421"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"4340fc9e8708dc643778959128bab04d"},{url:"/_next/static/chunks/277-9852fd43da8910cd.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/341.df3329d77a5faa19.js",revision:"df3329d77a5faa19"},{url:"/_next/static/chunks/472.a3826d29d6854395.js",revision:"a3826d29d6854395"},{url:"/_next/static/chunks/4bd1b696-8453d55469c7f353.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/537-e992c5a4b8c4c555.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/667-7faa4ec29f6932a1.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/684-b03edb75b8facb6c.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/874-09cd528922313eb8.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/_not-found/page-8e52dd5fe5af6fe9.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/page-2687bc48a84d3b1f.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/projects/%5Bid%5D/edit/page-e4450de2d40f7100.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/projects/%5Bid%5D/page-34216e1aae800bd8.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/projects/new/page-f788f6e785af52ae.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/roles/%5Bid%5D/edit/page-b30013b1aaf62712.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/roles/%5Bid%5D/page-3228197c3a9e6dc8.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/roles/new/page-310d4287a8c90e73.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/roles/page-d17ac1b450d8192b.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/templates/%5Bid%5D/page-6934cda0630b11e2.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/templates/new/page-18d54de59ab516f1.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/templates/page-46ed92c084f35eab.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/users/new/page-209b9841836cc4bc.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/admin/users/page-776351f257db8413.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/attachments/%5Bid%5D/route-0830854cac054308.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/auth/login/route-f17f86be948ab049.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/auth/logout/route-33a2353315738ee4.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/auth/me/route-fd6c7a7f6a414674.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/projects/%5Bid%5D/route-3326cbc778b3d0ad.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/projects/route-6f5aa540e2348043.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/roles/%5Bid%5D/route-df8d65b51d987f11.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/roles/route-b96689bae7525d1e.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/tasks/%5Bid%5D/attachments/route-20aa3b09a64fba89.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/tasks/%5Bid%5D/route-4adf2a710d8ef7ab.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/tasks/user/%5Busername%5D/route-2b55d6656526f9ee.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/templates/route-2158e22e5ecab48f.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/users/%5Bid%5D/route-6a08ba365109964b.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/api/users/route-6c438f150edbc0a5.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/dashboard/%5Buser%5D/page-0cc9e43ab39e1ae3.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/layout-1bfd1c6ae93e5692.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/login/page-5d35566e54c1e41b.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/page-f5fd57aecce18a55.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/app/tasks/%5Bid%5D/page-02d8dba99decc776.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/framework-f593a28cde54158e.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/main-app-a9eed8468c4c4736.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/main-c2cf9251ebe089dc.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/pages/_app-92f2aae776f86b9c.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/pages/_error-71d2b6a7b832d02a.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-7830a1e56b896d24.js",revision:"fHyMQ4NdRRrUgRAp7ojAw"},{url:"/_next/static/css/e519b5ae65c21a0a.css",revision:"e519b5ae65c21a0a"},{url:"/_next/static/fHyMQ4NdRRrUgRAp7ojAw/_buildManifest.js",revision:"f73a7fd24c8cdf7a2572821774d4f6a6"},{url:"/_next/static/fHyMQ4NdRRrUgRAp7ojAw/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/747892c23ea88013-s.woff2",revision:"a0761690ccf4441ace5cec893b82d4ab"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/93f479601ee12b01-s.p.woff2",revision:"da83d5f06d825c5ae65b7cca706cb312"},{url:"/_next/static/media/9610d9e46709d722-s.woff2",revision:"7b7c0ef93df188a852344fc272fc096b"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/file.svg",revision:"d09f95206c3fa0bb9bd9fefabfd0ea71"},{url:"/globe.svg",revision:"2aaafa6a49b6563925fe440891e32717"},{url:"/icons/icon-128x128.png.svg",revision:"91e0b41b71332177b5e65fff61f77b54"},{url:"/icons/icon-128x128.svg",revision:"91e0b41b71332177b5e65fff61f77b54"},{url:"/icons/icon-144x144.png.svg",revision:"15c0c472642a64ac90e299779ec2e8fa"},{url:"/icons/icon-144x144.svg",revision:"15c0c472642a64ac90e299779ec2e8fa"},{url:"/icons/icon-152x152.png.svg",revision:"37affca5822161e0848c5ef145131c1c"},{url:"/icons/icon-152x152.svg",revision:"37affca5822161e0848c5ef145131c1c"},{url:"/icons/icon-192x192.png.svg",revision:"4746a11e8967868b31edca0d0c00bb58"},{url:"/icons/icon-192x192.svg",revision:"4746a11e8967868b31edca0d0c00bb58"},{url:"/icons/icon-384x384.png.svg",revision:"35be52350e4cd06ba64c9a5224d69d34"},{url:"/icons/icon-384x384.svg",revision:"35be52350e4cd06ba64c9a5224d69d34"},{url:"/icons/icon-512x512.png.svg",revision:"7ee38e29fcf4dec4ea8e239c258127bd"},{url:"/icons/icon-512x512.svg",revision:"7ee38e29fcf4dec4ea8e239c258127bd"},{url:"/icons/icon-72x72.png.svg",revision:"d500e2b8aa1e4983be4fe8cce5a9d27a"},{url:"/icons/icon-72x72.svg",revision:"d500e2b8aa1e4983be4fe8cce5a9d27a"},{url:"/icons/icon-96x96.png.svg",revision:"964751215f922f7d264d5390de7cd696"},{url:"/icons/icon-96x96.svg",revision:"964751215f922f7d264d5390de7cd696"},{url:"/manifest.json",revision:"d96e727bd68efdded4a04da494d6c536"},{url:"/next.svg",revision:"8e061864f388b47f33a1c3780831193e"},{url:"/vercel.svg",revision:"c0af2f507b369b085b35ef4bbe3bcf1e"},{url:"/window.svg",revision:"a2760511c65806022ad20adf74370ff3"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:i,state:a})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https?.*/,new e.NetworkFirst({cacheName:"offlineCache",plugins:[new e.ExpirationPlugin({maxEntries:200})]}),"GET")});
