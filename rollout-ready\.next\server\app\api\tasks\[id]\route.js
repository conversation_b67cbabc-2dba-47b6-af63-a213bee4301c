(()=>{var e={};e.id=658,e.ids=[658],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(96330);let a=globalThis.prisma??new r.PrismaClient({log:["query"]})},7729:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>k,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>j});var r={};s.r(r),s.d(r,{DELETE:()=>c,GET:()=>d,PATCH:()=>p});var a=s(96559),n=s(48088),i=s(37719),o=s(32190),u=s(5069);async function d(e,{params:t}){try{let e=await t,s=parseInt(e.id);if(isNaN(s))return o.NextResponse.json({message:"Invalid task ID"},{status:400});let r=await u.z.projectTask.findUnique({where:{id:s},include:{project:{select:{id:!0,name:!0,description:!0,startDate:!0}},projectRole:{include:{role:{select:{id:!0,name:!0,description:!0}},user:{select:{id:!0,username:!0,firstName:!0,lastName:!0}}}},templateTask:{select:{id:!0,description:!0,isRecurring:!0,isCritical:!0}}}});if(!r)return o.NextResponse.json({message:"Task not found"},{status:404});return o.NextResponse.json(r)}catch(e){return console.error("Error fetching task:",e),o.NextResponse.json({message:"Failed to fetch task"},{status:500})}}async function p(e,{params:t}){try{let s=await t,r=parseInt(s.id);if(isNaN(r))return o.NextResponse.json({message:"Invalid task ID"},{status:400});let{status:a,comments:n,timeSpentMinutes:i,completedAt:d}=await e.json();if(a&&!["TODO","IN_PROGRESS","DONE"].includes(a))return o.NextResponse.json({message:"Invalid status. Must be TODO, IN_PROGRESS, or DONE"},{status:400});if(!await u.z.projectTask.findUnique({where:{id:r}}))return o.NextResponse.json({message:"Task not found"},{status:404});let p=await u.z.projectTask.update({where:{id:r},data:{...a&&{status:a},...void 0!==n&&{comments:n?.trim()||null},...void 0!==i&&{timeSpentMinutes:i},...void 0!==d&&{completedAt:d?new Date(d):null},updatedAt:new Date},include:{project:!0,projectRole:{include:{role:!0}},templateTask:!0}});return o.NextResponse.json(p)}catch(e){return console.error("Error updating task:",e),o.NextResponse.json({message:"Failed to update task"},{status:500})}}async function c(e,{params:t}){try{let e=await t,s=parseInt(e.id);if(isNaN(s))return o.NextResponse.json({message:"Invalid task ID"},{status:400});let r=await u.z.projectTask.findUnique({where:{id:s},include:{project:!0,projectRole:{include:{user:!0,role:!0}}}});if(!r)return o.NextResponse.json({message:"Task not found"},{status:404});return await u.z.projectTask.delete({where:{id:s}}),o.NextResponse.json({message:"Task deleted successfully",deletedTask:{id:r.id,description:r.description,project:r.project.name,assignedTo:`${r.projectRole.user.firstName} ${r.projectRole.user.lastName}`}})}catch(e){return console.error("Error deleting task:",e),o.NextResponse.json({message:"Failed to delete task"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tasks/[id]/route",pathname:"/api/tasks/[id]",filename:"route",bundlePath:"app/api/tasks/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\tasks\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:j,serverHooks:k}=l;function x(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:j})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(7729));module.exports=r})();