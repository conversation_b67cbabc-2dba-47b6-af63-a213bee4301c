(()=>{var e={};e.id=658,e.ids=[658],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(6330);let a=globalThis.prisma??new r.PrismaClient({log:["query"]})},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},7729:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>k,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var r={};s.r(r),s.d(r,{PATCH:()=>p});var a=s(6559),n=s(8088),i=s(7719),o=s(2190),u=s(5069);async function p(e,{params:t}){try{let s=await t,r=parseInt(s.id);if(isNaN(r))return o.NextResponse.json({message:"Invalid task ID"},{status:400});let{status:a,comments:n}=await e.json();if(!a||!["TODO","IN_PROGRESS","DONE"].includes(a))return o.NextResponse.json({message:"Invalid status. Must be TODO, IN_PROGRESS, or DONE"},{status:400});if(!await u.z.projectTask.findUnique({where:{id:r}}))return o.NextResponse.json({message:"Task not found"},{status:404});let i=await u.z.projectTask.update({where:{id:r},data:{status:a,comments:n?.trim()||null,updatedAt:new Date},include:{project:!0,projectRole:{include:{role:!0}},templateTask:!0}});return o.NextResponse.json(i)}catch(e){return console.error("Error updating task:",e),o.NextResponse.json({message:"Failed to update task"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tasks/[id]/route",pathname:"/api/tasks/[id]",filename:"route",bundlePath:"app/api/tasks/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\tasks\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:x}=d;function k(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(7729));module.exports=r})();