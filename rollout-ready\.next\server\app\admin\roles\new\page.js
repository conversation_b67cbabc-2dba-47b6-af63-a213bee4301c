(()=>{var e={};e.id=885,e.ids=[885],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1159:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\app\\\\admin\\\\roles\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\new\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4163:(e,r,t)=>{"use strict";t.d(r,{hO:()=>l,sG:()=>o});var s=t(3210),n=t(1215),i=t(8730),a=t(687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,i.TL)(`Primitive.${r}`),n=s.forwardRef((e,s)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n?t:r,{...i,ref:s})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{});function l(e,r){e&&n.flushSync(()=>e.dispatchEvent(r))}},4300:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(687),n=t(3210),i=t(4163),a=n.forwardRef((e,r)=>(0,s.jsx)(i.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));a.displayName="Label";var o=t(4780);function l({className:e,...r}){return(0,s.jsx)(a,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},4596:(e,r,t)=>{Promise.resolve().then(t.bind(t,1159))},6189:(e,r,t)=>{"use strict";var s=t(5773);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},6458:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(687),n=t(3210),i=t(6189),a=t(5814),o=t.n(a),l=t(4493),d=t(9523),c=t(9667),u=t(4300);function p(){let e=(0,i.useRouter)(),[r,t]=(0,n.useState)(!1),[a,p]=(0,n.useState)({name:"",description:""}),m=async r=>{r.preventDefault(),t(!0);try{let r=await fetch("/api/roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(r.ok)e.push("/admin/roles");else{let e=await r.json();alert(`Error: ${e.message}`)}}catch{alert("An error occurred while creating the role")}finally{t(!1)}},x=e=>{p({...a,[e.target.name]:e.target.value})};return(0,s.jsxs)("div",{className:"max-w-2xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Role"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Add a new project role to the system"})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"Role Details"}),(0,s.jsx)(l.BT,{children:"Enter the basic information for the new role"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"name",children:"Role Name *"}),(0,s.jsx)(c.p,{id:"name",name:"name",type:"text",required:!0,value:a.name,onChange:x,placeholder:"e.g., Project Manager, Infrastructure Lead"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"description",children:"Description"}),(0,s.jsx)("textarea",{id:"description",name:"description",value:a.description,onChange:x,placeholder:"Brief description of this role's responsibilities",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,s.jsx)(d.$,{type:"submit",disabled:r,children:r?"Creating...":"Create Role"}),(0,s.jsx)(o(),{href:"/admin/roles",children:(0,s.jsx)(d.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Next Steps"})}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsx)("p",{className:"text-gray-600",children:"After creating this role, you can:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1 text-gray-600",children:[(0,s.jsx)("li",{children:"Create templates associated with this role"}),(0,s.jsx)("li",{children:"Assign this role to team members in projects"}),(0,s.jsx)("li",{children:"Set up auto-assignment rules for templates"})]})]})]})]})}},7332:(e,r,t)=>{Promise.resolve().then(t.bind(t,6458))},7459:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(5239),n=t(8088),i=t(8170),a=t.n(i),o=t(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["admin",{children:["roles",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1159)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\new\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\new\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/roles/new/page",pathname:"/admin/roles/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(687);t(3210);var n=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,982,277,287],()=>t(7459));module.exports=s})();