"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[537],{1396:(e,t,n)=>{n.d(t,{UC:()=>rt,In:()=>n8,q7:()=>rr,VF:()=>rl,p4:()=>ro,ZL:()=>re,bL:()=>n3,wn:()=>ra,PP:()=>ri,l9:()=>n9,WT:()=>n4,LM:()=>rn});var r,o,l,i,a=n(2115),u=n.t(a,2),c=n(7650);function s(e,[t,n]){return Math.min(n,Math.max(t,e))}function d(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function f(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function p(e,t){var n=f(e,t,"get");return n.get?n.get.call(e):n.value}function h(e,t,n){var r=f(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var v=n(5155);function m(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let r=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=a.createContext(r),l=n.length;n=[...n,r];let i=t=>{let{scope:n,children:r,...i}=t,u=n?.[e]?.[l]||o,c=a.useMemo(()=>i,Object.values(i));return(0,v.jsx)(u.Provider,{value:c,children:r})};return i.displayName=t+"Provider",[i,function(n,i){let u=i?.[e]?.[l]||o,c=a.useContext(u);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var g=n(6101),y=n(9708),w=new WeakMap;function b(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=x(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function x(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap;var E=a.createContext(void 0),S=n(3655);function C(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var R="dismissableLayer.update",A=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),T=a.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:c,onInteractOutside:s,onDismiss:f,...p}=e,h=a.useContext(A),[m,y]=a.useState(null),w=null!=(r=null==m?void 0:m.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,b]=a.useState({}),x=(0,g.s)(t,e=>y(e)),E=Array.from(h.layers),[T]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),P=E.indexOf(T),N=m?E.indexOf(m):-1,j=h.layersWithOutsidePointerEventsDisabled.size>0,M=N>=P,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=C(e),o=a.useRef(!1),l=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){k("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));M&&!n&&(null==u||u(e),null==s||s(e),e.defaultPrevented||null==f||f())},w),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=C(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&k("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==c||c(e),null==s||s(e),e.defaultPrevented||null==f||f())},w);return!function(e,t=globalThis?.document){let n=C(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===h.layers.size-1&&(null==i||i(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},w),a.useEffect(()=>{if(m)return o&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(l=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(m)),h.layers.add(m),L(),()=>{o&&1===h.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=l)}},[m,w,o,h]),a.useEffect(()=>()=>{m&&(h.layers.delete(m),h.layersWithOutsidePointerEventsDisabled.delete(m),L())},[m,h]),a.useEffect(()=>{let e=()=>b({});return document.addEventListener(R,e),()=>document.removeEventListener(R,e)},[]),(0,v.jsx)(S.sG.div,{...p,ref:x,style:{pointerEvents:j?M?"auto":"none":void 0,...e.style},onFocusCapture:d(e.onFocusCapture,D.onFocusCapture),onBlurCapture:d(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:d(e.onPointerDownCapture,O.onPointerDownCapture)})});function L(){let e=new CustomEvent(R);document.dispatchEvent(e)}function k(e,t,n,r){let{discrete:o}=r,l=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&l.addEventListener(e,t,{once:!0}),o?(0,S.hO)(l,i):l.dispatchEvent(i)}T.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(A),r=a.useRef(null),o=(0,g.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(S.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var P=0;function N(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var j="focusScope.autoFocusOnMount",M="focusScope.autoFocusOnUnmount",O={bubbles:!1,cancelable:!0},D=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...i}=e,[u,c]=a.useState(null),s=C(o),d=C(l),f=a.useRef(null),p=(0,g.s)(t,e=>c(e)),h=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(h.paused||!u)return;let t=e.target;u.contains(t)?f.current=t:F(f.current,{select:!0})},t=function(e){if(h.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||F(f.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&F(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,h.paused]),a.useEffect(()=>{if(u){H.add(h);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(j,O);u.addEventListener(j,s),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(F(r,{select:t}),document.activeElement!==n)return}(I(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&F(u))}return()=>{u.removeEventListener(j,s),setTimeout(()=>{let t=new CustomEvent(M,O);u.addEventListener(M,d),u.dispatchEvent(t),t.defaultPrevented||F(null!=e?e:document.body,{select:!0}),u.removeEventListener(M,d),H.remove(h)},0)}}},[u,s,d,h]);let m=a.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,l]=function(e){let t=I(e);return[W(t,e),W(t.reverse(),e)]}(t);r&&l?e.shiftKey||o!==l?e.shiftKey&&o===r&&(e.preventDefault(),n&&F(l,{select:!0})):(e.preventDefault(),n&&F(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,v.jsx)(S.sG.div,{tabIndex:-1,...i,ref:p,onKeyDown:m})});function I(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function W(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function F(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}D.displayName="FocusScope";var H=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=B(e,t)).unshift(t)},remove(t){var n;null==(n=(e=B(e,t))[0])||n.resume()}}}();function B(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var _=globalThis?.document?a.useLayoutEffect:()=>{},V=u[" useId ".trim().toString()]||(()=>void 0),z=0;function G(e){let[t,n]=a.useState(V());return _(()=>{e||n(e=>e??String(z++))},[e]),e||(t?`radix-${t}`:"")}let K=["top","right","bottom","left"],$=Math.min,U=Math.max,X=Math.round,Y=Math.floor,q=e=>({x:e,y:e}),Z={left:"right",right:"left",bottom:"top",top:"bottom"},J={start:"end",end:"start"};function Q(e,t){return"function"==typeof e?e(t):e}function ee(e){return e.split("-")[0]}function et(e){return e.split("-")[1]}function en(e){return"x"===e?"y":"x"}function er(e){return"y"===e?"height":"width"}function eo(e){return["top","bottom"].includes(ee(e))?"y":"x"}function el(e){return e.replace(/start|end/g,e=>J[e])}function ei(e){return e.replace(/left|right|bottom|top/g,e=>Z[e])}function ea(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function eu(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ec(e,t,n){let r,{reference:o,floating:l}=e,i=eo(t),a=en(eo(t)),u=er(a),c=ee(t),s="y"===i,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,p=o[u]/2-l[u]/2;switch(c){case"top":r={x:d,y:o.y-l.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-l.width,y:f};break;default:r={x:o.x,y:o.y}}switch(et(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let es=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),u=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=ec(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[l]:{...p[l],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=ec(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function ed(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Q(t,e),h=ea(p),v=a[f?"floating"===d?"reference":"floating":d],m=eu(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(v)))||n?v:v.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),w=await (null==l.isElement?void 0:l.isElement(y))&&await (null==l.getScale?void 0:l.getScale(y))||{x:1,y:1},b=eu(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-b.top+h.top)/w.y,bottom:(b.bottom-m.bottom+h.bottom)/w.y,left:(m.left-b.left+h.left)/w.x,right:(b.right-m.right+h.right)/w.x}}function ef(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ep(e){return K.some(t=>e[t]>=0)}async function eh(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=ee(n),a=et(n),u="y"===eo(n),c=["left","top"].includes(i)?-1:1,s=l&&u?-1:1,d=Q(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function ev(){return"undefined"!=typeof window}function em(e){return ew(e)?(e.nodeName||"").toLowerCase():"#document"}function eg(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ey(e){var t;return null==(t=(ew(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ew(e){return!!ev()&&(e instanceof Node||e instanceof eg(e).Node)}function eb(e){return!!ev()&&(e instanceof Element||e instanceof eg(e).Element)}function ex(e){return!!ev()&&(e instanceof HTMLElement||e instanceof eg(e).HTMLElement)}function eE(e){return!!ev()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eg(e).ShadowRoot)}function eS(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eL(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function eC(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eR(e){let t=eA(),n=eb(e)?eL(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function eA(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eT(e){return["html","body","#document"].includes(em(e))}function eL(e){return eg(e).getComputedStyle(e)}function ek(e){return eb(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eP(e){if("html"===em(e))return e;let t=e.assignedSlot||e.parentNode||eE(e)&&e.host||ey(e);return eE(t)?t.host:t}function eN(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eP(t);return eT(n)?t.ownerDocument?t.ownerDocument.body:t.body:ex(n)&&eS(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=eg(o);if(l){let e=ej(i);return t.concat(i,i.visualViewport||[],eS(o)?o:[],e&&n?eN(e):[])}return t.concat(o,eN(o,[],n))}function ej(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eM(e){let t=eL(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ex(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=X(n)!==l||X(r)!==i;return a&&(n=l,r=i),{width:n,height:r,$:a}}function eO(e){return eb(e)?e:e.contextElement}function eD(e){let t=eO(e);if(!ex(t))return q(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=eM(t),i=(l?X(n.width):n.width)/r,a=(l?X(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let eI=q(0);function eW(e){let t=eg(e);return eA()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eI}function eF(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=eO(e),a=q(1);t&&(r?eb(r)&&(a=eD(r)):a=eD(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===eg(i))&&o)?eW(i):q(0),c=(l.left+u.x)/a.x,s=(l.top+u.y)/a.y,d=l.width/a.x,f=l.height/a.y;if(i){let e=eg(i),t=r&&eb(r)?eg(r):r,n=e,o=ej(n);for(;o&&r&&t!==n;){let e=eD(o),t=o.getBoundingClientRect(),r=eL(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=l,s+=i,o=ej(n=eg(o))}}return eu({width:d,height:f,x:c,y:s})}function eH(e,t){let n=ek(e).scrollLeft;return t?t.left+n:eF(ey(e)).left+n}function eB(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eH(e,r)),y:r.top+t.scrollTop}}function e_(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=eg(e),r=ey(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,u=0;if(o){l=o.width,i=o.height;let e=eA();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:l,height:i,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=ey(e),n=ek(e),r=e.ownerDocument.body,o=U(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=U(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),i=-n.scrollLeft+eH(e),a=-n.scrollTop;return"rtl"===eL(r).direction&&(i+=U(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:i,y:a}}(ey(e));else if(eb(t))r=function(e,t){let n=eF(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=ex(e)?eD(e):q(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=eW(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return eu(r)}function eV(e){return"static"===eL(e).position}function ez(e,t){if(!ex(e)||"fixed"===eL(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ey(e)===n&&(n=n.ownerDocument.body),n}function eG(e,t){let n=eg(e);if(eC(e))return n;if(!ex(e)){let t=eP(e);for(;t&&!eT(t);){if(eb(t)&&!eV(t))return t;t=eP(t)}return n}let r=ez(e,t);for(;r&&["table","td","th"].includes(em(r))&&eV(r);)r=ez(r,t);return r&&eT(r)&&eV(r)&&!eR(r)?n:r||function(e){let t=eP(e);for(;ex(t)&&!eT(t);){if(eR(t))return t;if(eC(t))break;t=eP(t)}return null}(e)||n}let eK=async function(e){let t=this.getOffsetParent||eG,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ex(t),o=ey(t),l="fixed"===n,i=eF(e,!0,l,t),a={scrollLeft:0,scrollTop:0},u=q(0);if(r||!r&&!l)if(("body"!==em(t)||eS(o))&&(a=ek(t)),r){let e=eF(t,!0,l,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eH(o));l&&!r&&o&&(u.x=eH(o));let c=!o||r||l?q(0):eB(o,a);return{x:i.left+a.scrollLeft-u.x-c.x,y:i.top+a.scrollTop-u.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},e$={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=ey(r),a=!!t&&eC(t.floating);if(r===i||a&&l)return n;let u={scrollLeft:0,scrollTop:0},c=q(1),s=q(0),d=ex(r);if((d||!d&&!l)&&(("body"!==em(r)||eS(i))&&(u=ek(r)),ex(r))){let e=eF(r);c=eD(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!i||d||l?q(0):eB(i,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:ey,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?eC(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eN(e,[],!1).filter(e=>eb(e)&&"body"!==em(e)),o=null,l="fixed"===eL(e).position,i=l?eP(e):e;for(;eb(i)&&!eT(i);){let t=eL(i),n=eR(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||eS(i)&&!n&&function e(t,n){let r=eP(t);return!(r===n||!eb(r)||eT(r))&&("fixed"===eL(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=eP(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=l[0],a=l.reduce((e,n)=>{let r=e_(t,n,o);return e.top=U(r.top,e.top),e.right=$(r.right,e.right),e.bottom=$(r.bottom,e.bottom),e.left=U(r.left,e.left),e},e_(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eG,getElementRects:eK,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eM(e);return{width:t,height:n}},getScale:eD,isElement:eb,isRTL:function(e){return"rtl"===eL(e).direction}};function eU(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eX=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:i,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=Q(e,t)||{};if(null==c)return{};let d=ea(s),f={x:n,y:r},p=en(eo(o)),h=er(p),v=await i.getDimensions(c),m="y"===p,g=m?"clientHeight":"clientWidth",y=l.reference[h]+l.reference[p]-f[p]-l.floating[h],w=f[p]-l.reference[p],b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(c)),x=b?b[g]:0;x&&await (null==i.isElement?void 0:i.isElement(b))||(x=a.floating[g]||l.floating[h]);let E=x/2-v[h]/2-1,S=$(d[m?"top":"left"],E),C=$(d[m?"bottom":"right"],E),R=x-v[h]-C,A=x/2-v[h]/2+(y/2-w/2),T=U(S,$(A,R)),L=!u.arrow&&null!=et(o)&&A!==T&&l.reference[h]/2-(A<S?S:C)-v[h]/2<0,k=L?A<S?A-S:A-R:0;return{[p]:f[p]+k,data:{[p]:T,centerOffset:A-T-k,...L&&{alignmentOffset:k}},reset:L}}}),eY=(e,t,n)=>{let r=new Map,o={platform:e$,...n},l={...o.platform,_c:r};return es(e,t,{...o,platform:l})};var eq="undefined"!=typeof document?a.useLayoutEffect:function(){};function eZ(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eZ(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eZ(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eJ(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eQ(e,t){let n=eJ(e);return Math.round(t*n)/n}function e0(e){let t=a.useRef(e);return eq(()=>{t.current=e}),t}let e1=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eX({element:n.current,padding:r}).fn(t):{}:n?eX({element:n,padding:r}).fn(t):{}}}),e2=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,u=await eh(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:l+u.y,data:{...u,placement:i}}}}}(e),options:[e,t]}),e5=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=Q(e,t),c={x:n,y:r},s=await ed(t,u),d=eo(ee(o)),f=en(d),p=c[f],h=c[d];if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=U(n,$(p,r))}if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=U(n,$(h,r))}let v=a.fn({...t,[f]:p,[d]:h});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[f]:l,[d]:i}}}}}}(e),options:[e,t]}),e6=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=Q(e,t),s={x:n,y:r},d=eo(o),f=en(d),p=s[f],h=s[d],v=Q(a,t),m="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(u){let e="y"===f?"height":"width",t=l.reference[f]-l.floating[e]+m.mainAxis,n=l.reference[f]+l.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(ee(o)),n=l.reference[d]-l.floating[e]+(t&&(null==(g=i.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=l.reference[d]+l.reference[e]+(t?0:(null==(y=i.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),e7=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=Q(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=ee(a),x=eo(s),E=ee(s)===s,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=v||(E||!y?[ei(s)]:function(e){let t=ei(e);return[el(e),t,el(t)]}(s)),R="none"!==g;!v&&R&&C.push(...function(e,t,n,r){let o=et(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(ee(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(el)))),l}(s,y,g,S));let A=[s,...C],T=await ed(t,w),L=[],k=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&L.push(T[b]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=et(e),o=en(eo(e)),l=er(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=ei(i)),[i,ei(i)]}(a,c,S);L.push(T[e[0]],T[e[1]])}if(k=[...k,{placement:a,overflows:L}],!L.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=A[e];if(t&&("alignment"!==h||x===eo(t)||k.every(e=>e.overflows[0]>0&&eo(e.placement)===x)))return{data:{index:e,overflows:k},reset:{placement:t}};let n=null==(l=k.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(m){case"bestFit":{let e=null==(i=k.filter(e=>{if(R){let t=eo(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),e3=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:i,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=Q(e,t),f=await ed(t,d),p=ee(i),h=et(i),v="y"===eo(i),{width:m,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,l=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(l=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=m-f.left-f.right,b=$(g-f[o],y),x=$(m-f[l],w),E=!t.middlewareData.shift,S=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!h){let e=U(f.left,0),t=U(f.right,0),n=U(f.top,0),r=U(f.bottom,0);v?C=m-2*(0!==e||0!==t?e+t:U(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:U(f.top,f.bottom))}await s({...t,availableWidth:C,availableHeight:S});let R=await u.getDimensions(c.floating);return m!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e9=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=Q(e,t);switch(r){case"referenceHidden":{let e=ef(await ed(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ep(e)}}}case"escaped":{let e=ef(await ed(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ep(e)}}}default:return{}}}}}(e),options:[e,t]}),e4=(e,t)=>({...e1(e),options:[e,t]});var e8=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,v.jsx)(S.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e8.displayName="Arrow";var te="Popper",[tt,tn]=m(te),[tr,to]=tt(te),tl=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,v.jsx)(tr,{scope:t,anchor:r,onAnchorChange:o,children:n})};tl.displayName=te;var ti="PopperAnchor",ta=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,l=to(ti,n),i=a.useRef(null),u=(0,g.s)(t,i);return a.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||i.current)}),r?null:(0,v.jsx)(S.sG.div,{...o,ref:u})});ta.displayName=ti;var tu="PopperContent",[tc,ts]=tt(tu),td=a.forwardRef((e,t)=>{var n,r,o,l,i,u,s,d;let{__scopePopper:f,side:p="bottom",sideOffset:h=0,align:m="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:T="optimized",onPlaced:L,...k}=e,P=to(tu,f),[N,j]=a.useState(null),M=(0,g.s)(t,e=>j(e)),[O,D]=a.useState(null),I=function(e){let[t,n]=a.useState(void 0);return _(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(O),W=null!=(s=null==I?void 0:I.width)?s:0,F=null!=(d=null==I?void 0:I.height)?d:0,H="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},B=Array.isArray(x)?x:[x],V=B.length>0,z={padding:H,boundary:B.filter(tv),altBoundary:V},{refs:G,floatingStyles:K,placement:X,isPositioned:q,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:l,floating:i}={},transform:u=!0,whileElementsMounted:s,open:d}=e,[f,p]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,v]=a.useState(r);eZ(h,r)||v(r);let[m,g]=a.useState(null),[y,w]=a.useState(null),b=a.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=a.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=l||m,S=i||y,C=a.useRef(null),R=a.useRef(null),A=a.useRef(f),T=null!=s,L=e0(s),k=e0(o),P=e0(d),N=a.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};k.current&&(e.platform=k.current),eY(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};j.current&&!eZ(A.current,t)&&(A.current=t,c.flushSync(()=>{p(t)}))})},[h,t,n,k,P]);eq(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let j=a.useRef(!1);eq(()=>(j.current=!0,()=>{j.current=!1}),[]),eq(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(L.current)return L.current(E,S,N);N()}},[E,S,N,L,T]);let M=a.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),O=a.useMemo(()=>({reference:E,floating:S}),[E,S]),D=a.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eQ(O.floating,f.x),r=eQ(O.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eJ(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,O.floating,f.x,f.y]);return a.useMemo(()=>({...f,update:N,refs:M,elements:O,floatingStyles:D}),[f,N,M,O,D])}({strategy:"fixed",placement:p+("center"!==m?"-"+m:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eO(e),d=l||i?[...s?eN(s):[],...eN(t)]:[];d.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=ey(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function i(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),l();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=Y(d),v=Y(o.clientWidth-(s+f)),m={rootMargin:-h+"px "+-v+"px "+-Y(o.clientHeight-(d+p))+"px "+-Y(s)+"px",threshold:U(0,$(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return i();r?i(!1,r):n=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==r||eU(c,e.getBoundingClientRect())||i(),g=!1}try{r=new IntersectionObserver(y,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,m)}r.observe(e)}(!0),l}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let v=c?eF(e):null;return c&&function t(){let r=eF(e);v&&!eU(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{l&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===T})},elements:{reference:P.anchor},middleware:[e2({mainAxis:h+F,alignmentAxis:y}),b&&e5({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?e6():void 0,...z}),b&&e7({...z}),e3({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),O&&e4({element:O,padding:w}),tm({arrowWidth:W,arrowHeight:F}),A&&e9({strategy:"referenceHidden",...z})]}),[J,Q]=tg(X),ee=C(L);_(()=>{q&&(null==ee||ee())},[q,ee]);let et=null==(n=Z.arrow)?void 0:n.x,en=null==(r=Z.arrow)?void 0:r.y,er=(null==(o=Z.arrow)?void 0:o.centerOffset)!==0,[eo,el]=a.useState();return _(()=>{N&&el(window.getComputedStyle(N).zIndex)},[N]),(0,v.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:q?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eo,"--radix-popper-transform-origin":[null==(l=Z.transformOrigin)?void 0:l.x,null==(i=Z.transformOrigin)?void 0:i.y].join(" "),...(null==(u=Z.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(tc,{scope:f,placedSide:J,onArrowChange:D,arrowX:et,arrowY:en,shouldHideArrow:er,children:(0,v.jsx)(S.sG.div,{"data-side":J,"data-align":Q,...k,ref:M,style:{...k.style,animation:q?void 0:"none"}})})})});td.displayName=tu;var tf="PopperArrow",tp={top:"bottom",right:"left",bottom:"top",left:"right"},th=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ts(tf,n),l=tp[o.placedSide];return(0,v.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(e8,{...r,ref:t,style:{...r.style,display:"block"}})})});function tv(e){return null!==e}th.displayName=tf;var tm=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=tg(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(l=null==(r=c.arrow)?void 0:r.x)?l:0)+d/2,g=(null!=(i=null==(o=c.arrow)?void 0:o.y)?i:0)+f/2,y="",w="";return"bottom"===p?(y=s?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function tg(e){let[t,n="center"]=e.split("-");return[t,n]}var ty=a.forwardRef((e,t)=>{var n,r;let{container:o,...l}=e,[i,u]=a.useState(!1);_(()=>u(!0),[]);let s=o||i&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return s?c.createPortal((0,v.jsx)(S.sG.div,{...l,ref:t}),s):null});ty.displayName="Portal";var tw=u[" useInsertionEffect ".trim().toString()]||_;function tb({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,i]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),l=a.useRef(t);return tw(()=>{l.current=t},[t]),a.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,a.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&i.current?.(n)}else l(t)},[u,e,l,i])]}Symbol("RADIX:SYNC_STATE");var tx=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});a.forwardRef((e,t)=>(0,v.jsx)(S.sG.span,{...e,ref:t,style:{...tx,...e.style}})).displayName="VisuallyHidden";var tE=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tS=new WeakMap,tC=new WeakMap,tR={},tA=0,tT=function(e){return e&&(e.host||tT(e.parentNode))},tL=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tT(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tR[n]||(tR[n]=new WeakMap);var l=tR[n],i=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tS.get(e)||0)+1,c=(l.get(e)||0)+1;tS.set(e,u),l.set(e,c),i.push(e),1===u&&o&&tC.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tA++,function(){i.forEach(function(e){var t=tS.get(e)-1,o=l.get(e)-1;tS.set(e,t),l.set(e,o),t||(tC.has(e)||e.removeAttribute(r),tC.delete(e)),o||e.removeAttribute(n)}),--tA||(tS=new WeakMap,tS=new WeakMap,tC=new WeakMap,tR={})}},tk=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||tE(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tL(r,o,n,"aria-hidden")):function(){return null}},tP=function(){return(tP=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tN(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tj=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tM="width-before-scroll-bar";function tO(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tD="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,tI=new WeakMap;function tW(e){return e}var tF=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=tW),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var l=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(l)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return l.options=tP({async:!0,ssr:!1},e),l}(),tH=function(){},tB=a.forwardRef(function(e,t){var n,r,o,l,i=a.useRef(null),u=a.useState({onScrollCapture:tH,onWheelCapture:tH,onTouchMoveCapture:tH}),c=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,v=e.enabled,m=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,E=e.as,S=e.gapMode,C=tN(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[i,t],r=function(e){return n.forEach(function(t){return tO(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,tD(function(){var e=tI.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||tO(e,null)}),r.forEach(function(e){t.has(e)||tO(e,o)})}tI.set(l,n)},[n]),l),A=tP(tP({},C),c);return a.createElement(a.Fragment,null,v&&a.createElement(g,{sideCar:tF,removeScrollBar:h,shards:m,noRelative:y,noIsolation:w,inert:b,setCallbacks:s,allowPinchZoom:!!x,lockRef:i,gapMode:S}),d?a.cloneElement(a.Children.only(f),tP(tP({},A),{ref:R})):a.createElement(void 0===E?"div":E,tP({},A,{className:p,ref:R}),f))});tB.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tB.classNames={fullWidth:tM,zeroRight:tj};var t_=function(e){var t=e.sideCar,n=tN(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,tP({},n))};t_.isSideCarExport=!0;var tV=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tz=function(){var e=tV();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tG=function(){var e=tz();return function(t){return e(t.styles,t.dynamic),null}},tK={left:0,top:0,right:0,gap:0},t$=function(e){return parseInt(e||"",10)||0},tU=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t$(n),t$(r),t$(o)]},tX=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tK;var t=tU(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tY=tG(),tq="data-scroll-locked",tZ=function(e,t,n,r){var o=e.left,l=e.top,i=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(tq,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tj," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tM," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tj," .").concat(tj," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tM," .").concat(tM," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(tq,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},tJ=function(){var e=parseInt(document.body.getAttribute(tq)||"0",10);return isFinite(e)?e:0},tQ=function(){a.useEffect(function(){return document.body.setAttribute(tq,(tJ()+1).toString()),function(){var e=tJ()-1;e<=0?document.body.removeAttribute(tq):document.body.setAttribute(tq,e.toString())}},[])},t0=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;tQ();var l=a.useMemo(function(){return tX(o)},[o]);return a.createElement(tY,{styles:tZ(l,!t,o,n?"":"!important")})},t1=!1;if("undefined"!=typeof window)try{var t2=Object.defineProperty({},"passive",{get:function(){return t1=!0,!0}});window.addEventListener("test",t2,t2),window.removeEventListener("test",t2,t2)}catch(e){t1=!1}var t5=!!t1&&{passive:!1},t6=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},t7=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),t3(e,r)){var o=t9(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},t3=function(e,t){return"v"===e?t6(t,"overflowY"):t6(t,"overflowX")},t9=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},t4=function(e,t,n,r,o){var l,i=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),a=i*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=t9(e,u),v=h[0],m=h[1]-h[2]-i*v;(v||m)&&t3(e,u)&&(f+=m,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},t8=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ne=function(e){return[e.deltaX,e.deltaY]},nt=function(e){return e&&"current"in e?e.current:e},nn=0,nr=[];let no=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(nn++)[0],l=a.useState(tG)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nt),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=t8(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],c="deltaY"in e?e.deltaY:a[1]-l[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=t7(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t7(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return t4(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(nr.length&&nr[nr.length-1]===l){var n="deltaY"in e?ne(e):t8(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(nt).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),d=a.useCallback(function(e){n.current=t8(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,ne(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,t8(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return nr.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,t5),document.addEventListener("touchmove",c,t5),document.addEventListener("touchstart",d,t5),function(){nr=nr.filter(function(e){return e!==l}),document.removeEventListener("wheel",c,t5),document.removeEventListener("touchmove",c,t5),document.removeEventListener("touchstart",d,t5)}},[]);var h=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(t0,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tF.useMedium(r),t_);var nl=a.forwardRef(function(e,t){return a.createElement(tB,tP({},e,{ref:t,sideCar:no}))});nl.classNames=tB.classNames;var ni=[" ","Enter","ArrowUp","ArrowDown"],na=[" ","Enter"],nu="Select",[nc,ns,nd]=function(e){let t=e+"CollectionProvider",[n,r]=m(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:n}=e,r=a.useRef(null),l=a.useRef(new Map).current;return(0,v.jsx)(o,{scope:t,itemMap:l,collectionRef:r,children:n})};i.displayName=t;let u=e+"CollectionSlot",c=(0,y.TL)(u),s=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(u,n),i=(0,g.s)(t,o.collectionRef);return(0,v.jsx)(c,{ref:i,children:r})});s.displayName=u;let d=e+"CollectionItemSlot",f="data-radix-collection-item",p=(0,y.TL)(d),h=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,i=a.useRef(null),u=(0,g.s)(t,i),c=l(d,n);return a.useEffect(()=>(c.itemMap.set(i,{ref:i,...o}),()=>void c.itemMap.delete(i))),(0,v.jsx)(p,{...{[f]:""},ref:u,children:r})});return h.displayName=d,[{Provider:i,Slot:s,ItemSlot:h},function(t){let n=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(f,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(nu),[nf,np]=m(nu,[nd,tn]),nh=tn(),[nv,nm]=nf(nu),[ng,ny]=nf(nu),nw=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:l,value:i,defaultValue:u,onValueChange:c,dir:s,name:d,autoComplete:f,disabled:p,required:h,form:m}=e,g=nh(t),[y,w]=a.useState(null),[b,x]=a.useState(null),[S,C]=a.useState(!1),R=function(e){let t=a.useContext(E);return e||t||"ltr"}(s),[A,T]=tb({prop:r,defaultProp:null!=o&&o,onChange:l,caller:nu}),[L,k]=tb({prop:i,defaultProp:u,onChange:c,caller:nu}),P=a.useRef(null),N=!y||m||!!y.closest("form"),[j,M]=a.useState(new Set),O=Array.from(j).map(e=>e.props.value).join(";");return(0,v.jsx)(tl,{...g,children:(0,v.jsxs)(nv,{required:h,scope:t,trigger:y,onTriggerChange:w,valueNode:b,onValueNodeChange:x,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:G(),value:L,onValueChange:k,open:A,onOpenChange:T,dir:R,triggerPointerDownPosRef:P,disabled:p,children:[(0,v.jsx)(nc.Provider,{scope:t,children:(0,v.jsx)(ng,{scope:e.__scopeSelect,onNativeOptionAdd:a.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:a.useCallback(e=>{M(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,v.jsxs)(n2,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:f,value:L,onChange:e=>k(e.target.value),disabled:p,form:m,children:[void 0===L?(0,v.jsx)("option",{value:""}):null,Array.from(j)]},O):null]})})};nw.displayName=nu;var nb="SelectTrigger",nx=a.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,l=nh(n),i=nm(nb,n),u=i.disabled||r,c=(0,g.s)(t,i.onTriggerChange),s=ns(n),f=a.useRef("touch"),[p,h,m]=n6(e=>{let t=s().filter(e=>!e.disabled),n=t.find(e=>e.value===i.value),r=n7(t,e,n);void 0!==r&&i.onValueChange(r.value)}),y=e=>{u||(i.onOpenChange(!0),m()),e&&(i.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,v.jsx)(ta,{asChild:!0,...l,children:(0,v.jsx)(S.sG.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":n5(i.value)?"":void 0,...o,ref:c,onClick:d(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:d(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:d(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&ni.includes(e.key)&&(y(),e.preventDefault())})})})});nx.displayName=nb;var nE="SelectValue",nS=a.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,u=nm(nE,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==l,d=(0,g.s)(t,u.onValueNodeChange);return _(()=>{c(s)},[c,s]),(0,v.jsx)(S.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:n5(u.value)?(0,v.jsx)(v.Fragment,{children:i}):l})});nS.displayName=nE;var nC=a.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,v.jsx)(S.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nC.displayName="SelectIcon";var nR=e=>(0,v.jsx)(ty,{asChild:!0,...e});nR.displayName="SelectPortal";var nA="SelectContent",nT=a.forwardRef((e,t)=>{let n=nm(nA,e.__scopeSelect),[r,o]=a.useState();return(_(()=>{o(new DocumentFragment)},[]),n.open)?(0,v.jsx)(nN,{...e,ref:t}):r?c.createPortal((0,v.jsx)(nL,{scope:e.__scopeSelect,children:(0,v.jsx)(nc.Slot,{scope:e.__scopeSelect,children:(0,v.jsx)("div",{children:e.children})})}),r):null});nT.displayName=nA;var[nL,nk]=nf(nA),nP=(0,y.TL)("SelectContent.RemoveScroll"),nN=a.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:l,onPointerDownOutside:i,side:u,sideOffset:c,align:s,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:y,hideWhenDetached:w,avoidCollisions:b,...x}=e,E=nm(nA,n),[S,C]=a.useState(null),[R,A]=a.useState(null),L=(0,g.s)(t,e=>C(e)),[k,j]=a.useState(null),[M,O]=a.useState(null),I=ns(n),[W,F]=a.useState(!1),H=a.useRef(!1);a.useEffect(()=>{if(S)return tk(S)},[S]),a.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:N()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:N()),P++,()=>{1===P&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),P--}},[]);let B=a.useCallback(e=>{let[t,...n]=I().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[I,R]),_=a.useCallback(()=>B([k,S]),[B,k,S]);a.useEffect(()=>{W&&_()},[W,_]);let{onOpenChange:V,triggerPointerDownPosRef:z}=E;a.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=z.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=z.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,V,z]),a.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[G,K]=n6(e=>{let t=I().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=n7(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),$=a.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==E.value&&E.value===t||r)&&(j(e),r&&(H.current=!0))},[E.value]),U=a.useCallback(()=>null==S?void 0:S.focus(),[S]),X=a.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==E.value&&E.value===t||r)&&O(e)},[E.value]),Y="popper"===r?nM:nj,q=Y===nM?{side:u,sideOffset:c,align:s,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:y,hideWhenDetached:w,avoidCollisions:b}:{};return(0,v.jsx)(nL,{scope:n,content:S,viewport:R,onViewportChange:A,itemRefCallback:$,selectedItem:k,onItemLeave:U,itemTextRefCallback:X,focusSelectedItem:_,selectedItemText:M,position:r,isPositioned:W,searchRef:G,children:(0,v.jsx)(nl,{as:nP,allowPinchZoom:!0,children:(0,v.jsx)(D,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:d(o,e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,v.jsx)(T,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:i,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,v.jsx)(Y,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...x,...q,onPlaced:()=>F(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:d(x.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||K(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});nN.displayName="SelectContentImpl";var nj=a.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,l=nm(nA,n),i=nk(nA,n),[u,c]=a.useState(null),[d,f]=a.useState(null),p=(0,g.s)(t,e=>f(e)),h=ns(n),m=a.useRef(!1),y=a.useRef(!0),{viewport:w,selectedItem:b,selectedItemText:x,focusSelectedItem:E}=i,C=a.useCallback(()=>{if(l.trigger&&l.valueNode&&u&&d&&w&&b&&x){let e=l.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),o=x.getBoundingClientRect();if("rtl"!==l.dir){let r=o.left-t.left,l=n.left-r,i=e.left-l,a=e.width+i,c=Math.max(a,t.width),d=s(l,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=a+"px",u.style.left=d+"px"}else{let r=t.right-o.right,l=window.innerWidth-n.right-r,i=window.innerWidth-e.right-l,a=e.width+i,c=Math.max(a,t.width),d=s(l,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=a+"px",u.style.right=d+"px"}let i=h(),a=window.innerHeight-20,c=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),v=parseInt(f.paddingTop,10),g=parseInt(f.borderBottomWidth,10),y=p+v+c+parseInt(f.paddingBottom,10)+g,E=Math.min(5*b.offsetHeight,y),S=window.getComputedStyle(w),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,T=b.offsetHeight/2,L=p+v+(b.offsetTop+T);if(L<=A){let e=i.length>0&&b===i[i.length-1].ref.current;u.style.bottom="0px";let t=Math.max(a-A,T+(e?R:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+g);u.style.height=L+t+"px"}else{let e=i.length>0&&b===i[0].ref.current;u.style.top="0px";let t=Math.max(A,p+w.offsetTop+(e?C:0)+T);u.style.height=t+(y-L)+"px",w.scrollTop=L-A+w.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=E+"px",u.style.maxHeight=a+"px",null==r||r(),requestAnimationFrame(()=>m.current=!0)}},[h,l.trigger,l.valueNode,u,d,w,b,x,l.dir,r]);_(()=>C(),[C]);let[R,A]=a.useState();_(()=>{d&&A(window.getComputedStyle(d).zIndex)},[d]);let T=a.useCallback(e=>{e&&!0===y.current&&(C(),null==E||E(),y.current=!1)},[C,E]);return(0,v.jsx)(nO,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,v.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,v.jsx)(S.sG.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nj.displayName="SelectItemAlignedPosition";var nM=a.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=nh(n);return(0,v.jsx)(td,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nM.displayName="SelectPopperPosition";var[nO,nD]=nf(nA,{}),nI="SelectViewport",nW=a.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,l=nk(nI,n),i=nD(nI,n),u=(0,g.s)(t,l.onViewportChange),c=a.useRef(0);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,v.jsx)(nc.Slot,{scope:n,children:(0,v.jsx)(S.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:d(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=i;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});nW.displayName=nI;var nF="SelectGroup",[nH,nB]=nf(nF);a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=G();return(0,v.jsx)(nH,{scope:n,id:o,children:(0,v.jsx)(S.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=nF;var n_="SelectLabel";a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nB(n_,n);return(0,v.jsx)(S.sG.div,{id:o.id,...r,ref:t})}).displayName=n_;var nV="SelectItem",[nz,nG]=nf(nV),nK=a.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:l,...i}=e,u=nm(nV,n),c=nk(nV,n),s=u.value===r,[f,p]=a.useState(null!=l?l:""),[h,m]=a.useState(!1),y=(0,g.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,r,o)}),w=G(),b=a.useRef("touch"),x=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,v.jsx)(nz,{scope:n,value:r,disabled:o,textId:w,isSelected:s,onItemTextChange:a.useCallback(e=>{p(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,v.jsx)(nc.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:(0,v.jsx)(S.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":h?"":void 0,"aria-selected":s&&h,"data-state":s?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...i,ref:y,onFocus:d(i.onFocus,()=>m(!0)),onBlur:d(i.onBlur,()=>m(!1)),onClick:d(i.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:d(i.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:d(i.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:d(i.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:d(i.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:d(i.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(na.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});nK.displayName=nV;var n$="SelectItemText",nU=a.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...l}=e,i=nm(n$,n),u=nk(n$,n),s=nG(n$,n),d=ny(n$,n),[f,p]=a.useState(null),h=(0,g.s)(t,e=>p(e),s.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,s.value,s.disabled)}),m=null==f?void 0:f.textContent,y=a.useMemo(()=>(0,v.jsx)("option",{value:s.value,disabled:s.disabled,children:m},s.value),[s.disabled,s.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:b}=d;return _(()=>(w(y),()=>b(y)),[w,b,y]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(S.sG.span,{id:s.textId,...l,ref:h}),s.isSelected&&i.valueNode&&!i.valueNodeHasChildren?c.createPortal(l.children,i.valueNode):null]})});nU.displayName=n$;var nX="SelectItemIndicator",nY=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nG(nX,n).isSelected?(0,v.jsx)(S.sG.span,{"aria-hidden":!0,...r,ref:t}):null});nY.displayName=nX;var nq="SelectScrollUpButton",nZ=a.forwardRef((e,t)=>{let n=nk(nq,e.__scopeSelect),r=nD(nq,e.__scopeSelect),[o,l]=a.useState(!1),i=(0,g.s)(t,r.onScrollButtonChange);return _(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(n0,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nZ.displayName=nq;var nJ="SelectScrollDownButton",nQ=a.forwardRef((e,t)=>{let n=nk(nJ,e.__scopeSelect),r=nD(nJ,e.__scopeSelect),[o,l]=a.useState(!1),i=(0,g.s)(t,r.onScrollButtonChange);return _(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(n0,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nQ.displayName=nJ;var n0=a.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,l=nk("SelectScrollButton",n),i=a.useRef(null),u=ns(n),c=a.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return a.useEffect(()=>()=>c(),[c]),_(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,v.jsx)(S.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:d(o.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(r,50))}),onPointerMove:d(o.onPointerMove,()=>{var e;null==(e=l.onItemLeave)||e.call(l),null===i.current&&(i.current=window.setInterval(r,50))}),onPointerLeave:d(o.onPointerLeave,()=>{c()})})});a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,v.jsx)(S.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var n1="SelectArrow";a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nh(n),l=nm(n1,n),i=nk(n1,n);return l.open&&"popper"===i.position?(0,v.jsx)(th,{...o,...r,ref:t}):null}).displayName=n1;var n2=a.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,l=a.useRef(null),i=(0,g.s)(t,l),u=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return a.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,v.jsx)(S.sG.select,{...o,style:{...tx,...o.style},ref:i,defaultValue:r})});function n5(e){return""===e||void 0===e}function n6(e){let t=C(e),n=a.useRef(""),r=a.useRef(0),o=a.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),l=a.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return a.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,l]}function n7(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return u!==n?u:void 0}n2.displayName="SelectBubbleInput";var n3=nw,n9=nx,n4=nS,n8=nC,re=nR,rt=nT,rn=nW,rr=nK,ro=nU,rl=nY,ri=nZ,ra=nQ},2085:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.$,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return l(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:a}=t,u=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let l=o(t)||o(r);return i[e][l]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return l(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...c}[t]):({...a,...c})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),o=n(7650),l=n(9708),i=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,l.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...l,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7565).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6101:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>l});var r=n(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function i(...e){return r.useCallback(l(...e),e)}},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7565).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7565:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:s="",children:d,iconNode:f,...p}=e;return(0,r.createElement)("svg",{ref:t,...c,width:o,height:o,stroke:n,strokeWidth:i?24*Number(l)/Number(o):l,className:a("lucide",s),...!d&&!u(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:u,...c}=n;return(0,r.createElement)(s,{ref:l,iconNode:t,className:a("lucide-".concat(o(i(e))),"lucide-".concat(e),u),...c})});return n.displayName=i(e),n}},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7565).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9708:(e,t,n)=>{n.d(t,{DX:()=>a,TL:()=>i});var r=n(2115),o=n(6101),l=n(5155);function i(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...l}=e;if(r.isValidElement(n)){var i;let e,a,u=(i=n,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props);return n.type!==r.Fragment&&(c.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,a=r.Children.toArray(o),u=a.find(c);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var a=i("Slot"),u=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);