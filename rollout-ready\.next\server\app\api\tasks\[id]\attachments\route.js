(()=>{var e={};e.id=689,e.ids=[689],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3820:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>m,POST:()=>l});var a=s(96559),i=s(48088),n=s(37719),o=s(32190),p=s(5069),u=s(79748),d=s(33873),c=s(29021);async function l(e,{params:t}){try{let s=await t,r=parseInt(s.id);if(isNaN(r))return o.NextResponse.json({message:"Invalid task ID"},{status:400});if(!await p.z.projectTask.findUnique({where:{id:r}}))return o.NextResponse.json({message:"Task not found"},{status:404});let a=await e.formData(),i=a.get("file"),n=a.get("uploadedBy");if(!i)return o.NextResponse.json({message:"No file provided"},{status:400});if(!n)return o.NextResponse.json({message:"Uploader information required"},{status:400});if(i.size>0xa00000)return o.NextResponse.json({message:"File size exceeds 10MB limit"},{status:400});if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-powerpoint","application/vnd.openxmlformats-officedocument.presentationml.presentation","text/plain","image/jpeg","image/jpg","image/png","image/gif"].includes(i.type))return o.NextResponse.json({message:"File type not supported"},{status:400});let l=(0,d.join)(process.cwd(),"uploads","tasks");(0,c.existsSync)(l)||await (0,u.mkdir)(l,{recursive:!0});let m=Date.now(),f=i.name.split(".").pop(),x=`task_${r}_${m}.${f}`,g=(0,d.join)(l,x),h=await i.arrayBuffer(),w=Buffer.from(h);await (0,u.writeFile)(g,w);let j=await p.z.taskAttachment.create({data:{taskId:r,fileName:x,originalName:i.name,fileSize:i.size,mimeType:i.type,uploadedBy:n}});return o.NextResponse.json({message:"File uploaded successfully",attachment:j},{status:201})}catch(e){return console.error("Error uploading file:",e),o.NextResponse.json({message:"Failed to upload file"},{status:500})}}async function m(e,{params:t}){try{let e=await t,s=parseInt(e.id);if(isNaN(s))return o.NextResponse.json({message:"Invalid task ID"},{status:400});let r=await p.z.taskAttachment.findMany({where:{taskId:s},orderBy:{createdAt:"desc"}});return o.NextResponse.json(r)}catch(e){return console.error("Error fetching attachments:",e),o.NextResponse.json({message:"Failed to fetch attachments"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/tasks/[id]/attachments/route",pathname:"/api/tasks/[id]/attachments",filename:"route",bundlePath:"app/api/tasks/[id]/attachments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\tasks\\[id]\\attachments\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:h}=f;function w(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(96330);let a=globalThis.prisma??new r.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79748:e=>{"use strict";e.exports=require("fs/promises")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(3820));module.exports=r})();