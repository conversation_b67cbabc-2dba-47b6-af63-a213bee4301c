{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { prisma } from './db';\nimport { SystemRole } from '@prisma/client';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';\nconst SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days\n\nexport interface AuthUser {\n  id: number;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  systemRole: SystemRole;\n}\n\nexport interface SessionData {\n  userId: number;\n  username: string;\n  systemRole: SystemRole;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token\nexport function generateToken(payload: SessionData): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });\n}\n\n// Verify JWT token\nexport function verifyToken(token: string): SessionData | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as SessionData;\n  } catch {\n    return null;\n  }\n}\n\n// Create user session\nexport async function createSession(userId: number): Promise<string> {\n  const user = await prisma.user.findUnique({\n    where: { id: userId },\n    select: { id: true, username: true, systemRole: true }\n  });\n\n  if (!user) {\n    throw new Error('User not found');\n  }\n\n  const token = generateToken({\n    userId: user.id,\n    username: user.username,\n    systemRole: user.systemRole\n  });\n\n  await prisma.session.create({\n    data: {\n      userId,\n      token,\n      expiresAt: new Date(Date.now() + SESSION_DURATION)\n    }\n  });\n\n  return token;\n}\n\n// Validate session\nexport async function validateSession(token: string): Promise<AuthUser | null> {\n  try {\n    // Check if session exists in database\n    const session = await prisma.session.findUnique({\n      where: { token },\n      include: { user: true }\n    });\n\n    if (!session || session.expiresAt < new Date()) {\n      // Clean up expired session\n      if (session) {\n        await prisma.session.delete({ where: { id: session.id } });\n      }\n      return null;\n    }\n\n    // Verify JWT token\n    const payload = verifyToken(token);\n    if (!payload) {\n      return null;\n    }\n\n    return {\n      id: session.user.id,\n      username: session.user.username,\n      email: session.user.email,\n      firstName: session.user.firstName || undefined,\n      lastName: session.user.lastName || undefined,\n      systemRole: session.user.systemRole\n    };\n  } catch {\n    return null;\n  }\n}\n\n// Logout (delete session)\nexport async function logout(token: string): Promise<void> {\n  await prisma.session.deleteMany({\n    where: { token }\n  });\n}\n\n// Clean up expired sessions\nexport async function cleanupExpiredSessions(): Promise<void> {\n  await prisma.session.deleteMany({\n    where: {\n      expiresAt: {\n        lt: new Date()\n      }\n    }\n  });\n}\n\n// Check if user has required system role\nexport function hasSystemRole(userRole: SystemRole, requiredRole: SystemRole): boolean {\n  const roleHierarchy = {\n    [SystemRole.USER]: 0,\n    [SystemRole.MANAGER]: 1,\n    [SystemRole.ADMIN]: 2\n  };\n\n  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n}\n\n// Get user's project roles\nexport async function getUserProjectRoles(userId: number) {\n  return prisma.projectRole.findMany({\n    where: { userId },\n    include: {\n      project: true,\n      role: true\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,mBAAmB,IAAI,KAAK,KAAK,KAAK,MAAM,SAAS;AAkBpD,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAoB;IAChD,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAe,cAAc,MAAc;IAChD,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE,IAAI;QAAO;QACpB,QAAQ;YAAE,IAAI;YAAM,UAAU;YAAM,YAAY;QAAK;IACvD;IAEA,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,cAAc;QAC1B,QAAQ,KAAK,EAAE;QACf,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;IAC7B;IAEA,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1B,MAAM;YACJ;YACA;YACA,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACnC;IACF;IAEA,OAAO;AACT;AAGO,eAAe,gBAAgB,KAAa;IACjD,IAAI;QACF,sCAAsC;QACtC,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE;YAAM;YACf,SAAS;gBAAE,MAAM;YAAK;QACxB;QAEA,IAAI,CAAC,WAAW,QAAQ,SAAS,GAAG,IAAI,QAAQ;YAC9C,2BAA2B;YAC3B,IAAI,SAAS;gBACX,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBAAE,OAAO;wBAAE,IAAI,QAAQ,EAAE;oBAAC;gBAAE;YAC1D;YACA,OAAO;QACT;QAEA,mBAAmB;QACnB,MAAM,UAAU,YAAY;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,OAAO;YACL,IAAI,QAAQ,IAAI,CAAC,EAAE;YACnB,UAAU,QAAQ,IAAI,CAAC,QAAQ;YAC/B,OAAO,QAAQ,IAAI,CAAC,KAAK;YACzB,WAAW,QAAQ,IAAI,CAAC,SAAS,IAAI;YACrC,UAAU,QAAQ,IAAI,CAAC,QAAQ,IAAI;YACnC,YAAY,QAAQ,IAAI,CAAC,UAAU;QACrC;IACF,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAe,OAAO,KAAa;IACxC,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC9B,OAAO;YAAE;QAAM;IACjB;AACF;AAGO,eAAe;IACpB,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC9B,OAAO;YACL,WAAW;gBACT,IAAI,IAAI;YACV;QACF;IACF;AACF;AAGO,SAAS,cAAc,QAAoB,EAAE,YAAwB;IAC1E,MAAM,gBAAgB;QACpB,CAAC,6HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,EAAE;QACnB,CAAC,6HAAA,CAAA,aAAU,CAAC,OAAO,CAAC,EAAE;QACtB,CAAC,6HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,EAAE;IACtB;IAEA,OAAO,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,aAAa;AAC/D;AAGO,eAAe,oBAAoB,MAAc;IACtD,OAAO,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;QACjC,OAAO;YAAE;QAAO;QAChB,SAAS;YACP,SAAS;YACT,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\nimport { hashPassword } from \"@/lib/auth\";\nimport { SystemRole } from \"@prisma/client\";\n\n// GET /api/users - List all users\nexport async function GET() {\n  try {\n    const users = await prisma.user.findMany({\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstName: true,\n        lastName: true,\n        systemRole: true,\n        jobRoleId: true,\n        isActive: true,\n        createdAt: true,\n        jobRole: {\n          select: {\n            id: true,\n            name: true,\n            description: true,\n          },\n        },\n        _count: {\n          select: {\n            projectRoles: true,\n          },\n        },\n      },\n      orderBy: [\n        { systemRole: 'asc' },\n        { firstName: 'asc' },\n        { username: 'asc' },\n      ],\n    });\n\n    return NextResponse.json(users);\n  } catch (error) {\n    console.error(\"Error fetching users:\", error);\n    return NextResponse.json(\n      { message: \"Failed to fetch users\" },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/users - Create new user\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { username, email, password, firstName, lastName, systemRole, jobRoleId } = body;\n\n    // Validation\n    if (!username || !email || !password) {\n      return NextResponse.json(\n        { message: \"Username, email, and password are required\" },\n        { status: 400 }\n      );\n    }\n\n    if (password.length < 6) {\n      return NextResponse.json(\n        { message: \"Password must be at least 6 characters long\" },\n        { status: 400 }\n      );\n    }\n\n    // Validate job role if provided\n    if (jobRoleId) {\n      const jobRole = await prisma.role.findUnique({\n        where: { id: parseInt(jobRoleId) }\n      });\n\n      if (!jobRole) {\n        return NextResponse.json(\n          { message: \"Invalid job role\" },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Check if username or email already exists\n    const existingUser = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { username: username.toLowerCase() },\n          { email: email.toLowerCase() },\n        ],\n      },\n    });\n\n    if (existingUser) {\n      return NextResponse.json(\n        { message: \"Username or email already exists\" },\n        { status: 400 }\n      );\n    }\n\n    // Hash password\n    const hashedPassword = await hashPassword(password);\n\n    // Create user\n    const user = await prisma.user.create({\n      data: {\n        username: username.toLowerCase(),\n        email: email.toLowerCase(),\n        password: hashedPassword,\n        firstName: firstName?.trim() || null,\n        lastName: lastName?.trim() || null,\n        systemRole: systemRole || SystemRole.USER,\n        jobRoleId: jobRoleId ? parseInt(jobRoleId) : null,\n      },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstName: true,\n        lastName: true,\n        systemRole: true,\n        jobRoleId: true,\n        isActive: true,\n        createdAt: true,\n      },\n    });\n\n    return NextResponse.json(user, { status: 201 });\n  } catch (error) {\n    console.error(\"Error creating user:\", error);\n    return NextResponse.json(\n      { message: \"Failed to create user\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,aAAa;oBACf;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,cAAc;oBAChB;gBACF;YACF;YACA,SAAS;gBACP;oBAAE,YAAY;gBAAM;gBACpB;oBAAE,WAAW;gBAAM;gBACnB;oBAAE,UAAU;gBAAM;aACnB;QACH;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG;QAElF,aAAa;QACb,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAA6C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAA8C,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,IAAI,WAAW;YACb,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC3C,OAAO;oBAAE,IAAI,SAAS;gBAAW;YACnC;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;gBAAmB,GAC9B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,4CAA4C;QAC5C,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL,IAAI;oBACF;wBAAE,UAAU,SAAS,WAAW;oBAAG;oBACnC;wBAAE,OAAO,MAAM,WAAW;oBAAG;iBAC9B;YACH;QACF;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAmC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;QAE1C,cAAc;QACd,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,UAAU,SAAS,WAAW;gBAC9B,OAAO,MAAM,WAAW;gBACxB,UAAU;gBACV,WAAW,WAAW,UAAU;gBAChC,UAAU,UAAU,UAAU;gBAC9B,YAAY,cAAc,6HAAA,CAAA,aAAU,CAAC,IAAI;gBACzC,WAAW,YAAY,SAAS,aAAa;YAC/C;YACA,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}