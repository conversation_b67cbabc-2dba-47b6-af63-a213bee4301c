{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// Public routes that don't require authentication\nconst publicRoutes = [\n  '/',\n  '/login',\n  '/api/auth/login',\n  '/api/auth/logout',\n  '/manifest.json',\n  '/sw.js',\n  '/_next',\n  '/favicon.ico',\n];\n\n// Protected routes that require authentication\nconst protectedRoutes = [\n  '/admin',\n  '/dashboard',\n];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Allow public routes\n  if (publicRoutes.some(route => pathname.startsWith(route))) {\n    return NextResponse.next();\n  }\n\n  // Check if route is protected\n  const isProtectedRoute = protectedRoutes.some(route =>\n    pathname.startsWith(route)\n  );\n\n  if (!isProtectedRoute) {\n    return NextResponse.next();\n  }\n\n  // Get token from cookie\n  const token = request.cookies.get('auth-token')?.value;\n\n  if (!token) {\n    // Redirect to login if no token\n    const loginUrl = new URL('/login', request.url);\n    loginUrl.searchParams.set('redirect', pathname);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  // If token exists, let the page handle validation\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,kDAAkD;AAClD,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,+CAA+C;AAC/C,MAAM,kBAAkB;IACtB;IACA;CACD;AAEM,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,sBAAsB;IACtB,IAAI,aAAa,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;QAC1D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,8BAA8B;IAC9B,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;IAGtB,IAAI,CAAC,kBAAkB;QACrB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,wBAAwB;IACxB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IAEjD,IAAI,CAAC,OAAO;QACV,gCAAgC;QAChC,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,kDAAkD;IAClD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;KAKC,GACD;KACD;AACH"}}]}