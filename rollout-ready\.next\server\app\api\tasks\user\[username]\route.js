(()=>{var e={};e.id=769,e.ids=[769],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(96330);let a=globalThis.prisma??new r.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},58975:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>k,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{GET:()=>c});var a=s(96559),i=s(48088),n=s(37719),o=s(32190),u=s(5069);async function c(e,{params:t}){try{let e=(await t).username;if(!e)return o.NextResponse.json({message:"Username is required"},{status:400});let s=await u.z.projectTask.findMany({where:{projectRole:{user:{username:e}}},include:{project:{select:{id:!0,name:!0,description:!0,startDate:!0}},projectRole:{include:{role:{select:{id:!0,name:!0,description:!0}},user:{select:{id:!0,username:!0,firstName:!0,lastName:!0}}}},templateTask:{select:{id:!0,description:!0,isRecurring:!0,isCritical:!0}}},orderBy:[{dueDate:"asc"},{createdAt:"desc"}]}),r=s.reduce((e,t)=>{let s=t.project.id;return e[s]||(e[s]={project:t.project,role:t.projectRole.role,tasks:[]}),e[s].tasks.push({id:t.id,description:t.description,dueDate:t.dueDate,status:t.status,comments:t.comments,isRecurring:t.templateTask?.isRecurring||!1,isCritical:t.templateTask?.isCritical||!1,createdAt:t.createdAt,updatedAt:t.updatedAt}),e},{}),a=Object.values(r),i={totalTasks:s.length,todoTasks:s.filter(e=>"TODO"===e.status).length,inProgressTasks:s.filter(e=>"IN_PROGRESS"===e.status).length,doneTasks:s.filter(e=>"DONE"===e.status).length,overdueTasks:s.filter(e=>"DONE"!==e.status&&new Date(e.dueDate)<new Date).length,criticalTasks:s.filter(e=>e.templateTask?.isCritical&&"DONE"!==e.status).length,activeProjects:Object.keys(r).length};return o.NextResponse.json({summary:i,projectTasks:a,allTasks:s})}catch(e){return console.error("Error fetching user tasks:",e),o.NextResponse.json({message:"Failed to fetch user tasks"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/tasks/user/[username]/route",pathname:"/api/tasks/user/[username]",filename:"route",bundlePath:"app/api/tasks/user/[username]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\tasks\\user\\[username]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:m}=l;function k(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(58975));module.exports=r})();