1:"$Sreact.fragment"
2:I[6874,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","848","static/chunks/app/admin/roles/page-c00ad37c5a8900de.js"],""]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[2103,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","177","static/chunks/app/layout-83cf57f71ce4342f.js"],"default"]
6:I[9228,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","177","static/chunks/app/layout-83cf57f71ce4342f.js"],"default"]
8:I[9665,[],"MetadataBoundary"]
a:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[6614,[],""]
:HL["/_next/static/css/79cee61387c6348b.css","style"]
0:{"P":null,"b":"89hKFyWTOtN_-5kAJiDJe","p":"","c":["","admin","roles"],"i":false,"f":[[["",{"children":["admin",{"children":["roles",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/79cee61387c6348b.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}],["$","meta",null,{"name":"theme-color","content":"#3b82f6"}],["$","meta",null,{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta",null,{"name":"apple-mobile-web-app-title","content":"Rollout Ready"}],["$","meta",null,{"name":"mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"msapplication-TileColor","content":"#3b82f6"}],["$","meta",null,{"name":"msapplication-tap-highlight","content":"no"}],["$","link",null,{"rel":"icon","href":"/icons/icon-192x192.svg"}],["$","link",null,{"rel":"apple-touch-icon","href":"/icons/icon-192x192.svg"}],["$","link",null,{"rel":"manifest","href":"/manifest.json"}]]}],["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased min-h-screen bg-background","children":[["$","nav",null,{"className":"border-b bg-white","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"flex justify-between h-16","children":[["$","div",null,{"className":"flex items-center","children":["$","h1",null,{"className":"text-xl font-bold text-gray-900","children":"Rollout Ready"}]}],["$","div",null,{"className":"flex items-center space-x-4","children":[["$","$L2",null,{"href":"/","className":"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium","children":"Home"}],["$","$L2",null,{"href":"/admin","className":"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium","children":"Admin"}]]}]]}]}]}],["$","main",null,{"className":"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L5",null,{}],["$","$L6",null,{}]]}]]}]]}],{"children":["admin",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["roles",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":["$L7",["$","$L8",null,{"children":"$L9"}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","zZAgNyHsEbOgPN_W5bdmj",{"children":[["$","$Lf",null,{"children":"$L10"}],null]}],null]}],false]],"m":"$undefined","G":["$11","$undefined"],"s":false,"S":true}
12:"$Sreact.suspense"
13:I[4911,[],"AsyncMetadata"]
9:["$","$12",null,{"fallback":null,"children":["$","$L13",null,{"promise":"$@14"}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
14:{"metadata":[["$","title","0",{"children":"Rollout Ready"}],["$","meta","1",{"name":"description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","link","2",{"rel":"manifest","href":"/manifest.json","crossOrigin":"$undefined"}],["$","meta","3",{"name":"format-detection","content":"telephone=no"}],["$","meta","4",{"name":"mobile-web-app-capable","content":"yes"}],["$","meta","5",{"name":"apple-mobile-web-app-title","content":"Rollout Ready"}],["$","meta","6",{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta","7",{"property":"og:title","content":"Rollout Ready"}],["$","meta","8",{"property":"og:description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","meta","9",{"property":"og:site_name","content":"Rollout Ready"}],["$","meta","10",{"property":"og:type","content":"website"}],["$","meta","11",{"name":"twitter:card","content":"summary"}],["$","meta","12",{"name":"twitter:title","content":"Rollout Ready"}],["$","meta","13",{"name":"twitter:description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","link","14",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
e:{"metadata":"$14:metadata","error":null,"digest":"$undefined"}
15:I[5127,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","848","static/chunks/app/admin/roles/page-c00ad37c5a8900de.js"],"Table"]
16:I[5127,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","848","static/chunks/app/admin/roles/page-c00ad37c5a8900de.js"],"TableHeader"]
17:I[5127,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","848","static/chunks/app/admin/roles/page-c00ad37c5a8900de.js"],"TableRow"]
18:I[5127,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","848","static/chunks/app/admin/roles/page-c00ad37c5a8900de.js"],"TableHead"]
19:I[5127,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","848","static/chunks/app/admin/roles/page-c00ad37c5a8900de.js"],"TableBody"]
1a:I[5127,["874","static/chunks/874-01f110b4d2036f39.js","277","static/chunks/277-9852fd43da8910cd.js","848","static/chunks/app/admin/roles/page-c00ad37c5a8900de.js"],"TableCell"]
7:["$","div",null,{"className":"space-y-8","children":[["$","div",null,{"className":"flex justify-between items-center","children":[["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"Manage Roles"}],["$","p",null,{"className":"text-gray-600 mt-2","children":"Create and manage project roles and their associated templates"}]]}],["$","$L2",null,{"href":"/admin/roles/new","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3","children":"Create New Role"}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"All Roles"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Overview of all project roles in the system"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","$L15",null,{"children":[["$","$L16",null,{"children":["$","$L17",null,{"children":[["$","$L18",null,{"children":"Role Name"}],["$","$L18",null,{"children":"Description"}],["$","$L18",null,{"children":"Templates"}],["$","$L18",null,{"children":"Projects Used"}],["$","$L18",null,{"children":"Actions"}]]}]}],["$","$L19",null,{"children":[["$","$L17","1",{"children":[["$","$L1a",null,{"className":"font-medium","children":"Business Analyst"}],["$","$L1a",null,{"children":"Business requirements and process analysis"}],["$","$L1a",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":[0," template","s"]}]}],["$","$L1a",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":[1," project",""]}]}],["$","$L1a",null,{"children":["$","div",null,{"className":"flex gap-2","children":[["$","$L2",null,{"href":"/admin/roles/1","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}],["$","$L2",null,{"href":"/admin/roles/1/edit","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"Edit"}]}]]}]}]]}],["$","$L17","2",{"children":[["$","$L1a",null,{"className":"font-medium","children":"Infrastructure Lead"}],["$","$L1a",null,{"children":"Technical infrastructure setup and management"}],["$","$L1a",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":[1," template",""]}]}],["$","$L1a",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":[2," project","s"]}]}],["$","$L1a",null,{"children":["$","div",null,{"className":"flex gap-2","children":[["$","$L2",null,{"href":"/admin/roles/2","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}],["$","$L2",null,{"href":"/admin/roles/2/edit","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"Edit"}]}]]}]}]]}],["$","$L17","3",{"children":[["$","$L1a",null,{"className":"font-medium","children":"Project Manager"}],["$","$L1a",null,{"children":"Overall project coordination and management"}],["$","$L1a",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":[1," template",""]}]}],["$","$L1a",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":[2," project","s"]}]}],["$","$L1a",null,{"children":["$","div",null,{"className":"flex gap-2","children":[["$","$L2",null,{"href":"/admin/roles/3","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}],["$","$L2",null,{"href":"/admin/roles/3/edit","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"Edit"}]}]]}]}]]}],["$","$L17","4",{"children":[["$","$L1a",null,{"className":"font-medium","children":"Security Architect"}],["$","$L1a",null,{"children":"Security assessment and implementation"}],["$","$L1a",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":[1," template",""]}]}],["$","$L1a",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":[2," project","s"]}]}],["$","$L1a",null,{"children":["$","div",null,{"className":"flex gap-2","children":[["$","$L2",null,{"href":"/admin/roles/4","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}],["$","$L2",null,{"href":"/admin/roles/4/edit","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"Edit"}]}]]}]}]]}]]}]]}]}]]}],["$","div",null,{"className":"grid md:grid-cols-3 gap-6","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Total Roles"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-3xl font-bold","children":4}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Total Templates"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-3xl font-bold","children":3}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Active in Projects"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-3xl font-bold","children":7}]}]]}]]}]]}]
