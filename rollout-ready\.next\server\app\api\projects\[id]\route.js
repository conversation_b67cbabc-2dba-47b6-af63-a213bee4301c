(()=>{var e={};e.id=514,e.ids=[514],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37185:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>j,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{DELETE:()=>l,GET:()=>u,PUT:()=>c});var a=r(96559),o=r(48088),i=r(37719),n=r(32190),p=r(5069);async function d(e,t,r){for(let s of(await p.z.template.findMany({where:{autoAssign:!0,roleId:{in:t.map(e=>e.roleId)}},include:{templateTasks:!0}}))){let a=t.find(e=>e.roleId===s.roleId);if(a){for(let t of s.templateTasks)if(!await p.z.projectTask.findFirst({where:{projectId:e,templateTaskId:t.id,projectRoleId:a.id}})){let s=new Date(r);s.setDate(s.getDate()+t.offsetDays),await p.z.projectTask.create({data:{projectId:e,templateTaskId:t.id,projectRoleId:a.id,description:t.description,dueDate:s,status:"TODO"}})}}}}async function u(e,{params:t}){try{let e=await t,r=parseInt(e.id);if(isNaN(r))return n.NextResponse.json({message:"Invalid project ID"},{status:400});let s=await p.z.project.findUnique({where:{id:r},include:{projectRoles:{include:{role:!0,user:!0}},_count:{select:{projectTasks:!0}}}});if(!s)return n.NextResponse.json({message:"Project not found"},{status:404});return n.NextResponse.json(s)}catch(e){return console.error("Error fetching project:",e),n.NextResponse.json({message:"Failed to fetch project"},{status:500})}}async function c(e,{params:t}){try{let r=await t,s=parseInt(r.id);if(isNaN(s))return n.NextResponse.json({message:"Invalid project ID"},{status:400});let{name:a,description:o,startDate:i,roleAssignments:u}=await e.json(),c=await p.z.project.findUnique({where:{id:s},include:{projectRoles:!0}});if(!c)return n.NextResponse.json({message:"Project not found"},{status:404});let l=await p.z.project.update({where:{id:s},data:{name:a,description:o,startDate:new Date(i)}});if(u){let e=c.projectRoles,t=[];for(let[r,a]of Object.entries(u)){let o=parseInt(r),i=e.find(e=>e.roleId===o);if(a&&"number"==typeof a){let e;if(!await p.z.user.findUnique({where:{id:a}}))return n.NextResponse.json({message:`User with ID ${a} not found`},{status:400});e=i?await p.z.projectRole.update({where:{id:i.id},data:{userId:a}}):await p.z.projectRole.create({data:{projectId:s,roleId:o,userId:a}}),t.push(e)}else i&&(await p.z.projectRole.delete({where:{id:i.id}}),await p.z.projectTask.deleteMany({where:{projectRoleId:i.id}}))}await d(s,t,l.startDate)}let j=await p.z.project.findUnique({where:{id:s},include:{projectRoles:{include:{role:!0,user:!0}},_count:{select:{projectTasks:!0}}}});return n.NextResponse.json(j)}catch(e){return console.error("Error updating project:",e),n.NextResponse.json({message:"Failed to update project"},{status:500})}}async function l(e,{params:t}){try{let e=await t,r=parseInt(e.id);if(isNaN(r))return n.NextResponse.json({message:"Invalid project ID"},{status:400});if(!await p.z.project.findUnique({where:{id:r}}))return n.NextResponse.json({message:"Project not found"},{status:404});return await p.z.project.delete({where:{id:r}}),n.NextResponse.json({message:"Project deleted successfully"})}catch(e){return console.error("Error deleting project:",e),n.NextResponse.json({message:"Failed to delete project"},{status:500})}}let j=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/projects/[id]/route",pathname:"/api/projects/[id]",filename:"route",bundlePath:"app/api/projects/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\projects\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:x}=j;function m(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(37185));module.exports=s})();