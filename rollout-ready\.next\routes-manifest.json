{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/admin/projects/[id]", "regex": "^/admin/projects/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/projects/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/projects/[id]/edit", "regex": "^/admin/projects/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/projects/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/admin/templates/[id]", "regex": "^/admin/templates/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/templates/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/tasks/[id]", "regex": "^/api/tasks/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tasks/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/[user]", "regex": "^/dashboard/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuser": "nxtPuser"}, "namedRegex": "^/dashboard/(?<nxtPuser>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/projects/new", "regex": "^/admin/projects/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/projects/new(?:/)?$"}, {"page": "/admin/roles", "regex": "^/admin/roles(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/roles(?:/)?$"}, {"page": "/admin/roles/new", "regex": "^/admin/roles/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/roles/new(?:/)?$"}, {"page": "/admin/templates", "regex": "^/admin/templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/templates(?:/)?$"}, {"page": "/admin/templates/new", "regex": "^/admin/templates/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/templates/new(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}