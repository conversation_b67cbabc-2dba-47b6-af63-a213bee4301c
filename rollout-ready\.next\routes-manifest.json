{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/admin/projects/[id]", "regex": "^/admin/projects/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/projects/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/projects/[id]/edit", "regex": "^/admin/projects/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/projects/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/admin/roles/[id]", "regex": "^/admin/roles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/roles/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/roles/[id]/edit", "regex": "^/admin/roles/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/roles/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/admin/templates/[id]", "regex": "^/admin/templates/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/templates/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/users/[id]", "regex": "^/admin/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/users/[id]/edit", "regex": "^/admin/users/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/users/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/api/attachments/[id]", "regex": "^/api/attachments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/attachments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/projects/[id]", "regex": "^/api/projects/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/projects/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/roles/[id]", "regex": "^/api/roles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/roles/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/tasks/user/[username]", "regex": "^/api/tasks/user/([^/]+?)(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername"}, "namedRegex": "^/api/tasks/user/(?<nxtPusername>[^/]+?)(?:/)?$"}, {"page": "/api/tasks/[id]", "regex": "^/api/tasks/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tasks/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/tasks/[id]/attachments", "regex": "^/api/tasks/([^/]+?)/attachments(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tasks/(?<nxtPid>[^/]+?)/attachments(?:/)?$"}, {"page": "/api/templates/[id]", "regex": "^/api/templates/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/templates/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/users/[id]", "regex": "^/api/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/users/[id]/reset-password", "regex": "^/api/users/([^/]+?)/reset\\-password(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)/reset\\-password(?:/)?$"}, {"page": "/dashboard/[user]", "regex": "^/dashboard/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuser": "nxtPuser"}, "namedRegex": "^/dashboard/(?<nxtPuser>[^/]+?)(?:/)?$"}, {"page": "/tasks/[id]", "regex": "^/tasks/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/tasks/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/projects/new", "regex": "^/admin/projects/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/projects/new(?:/)?$"}, {"page": "/admin/roles", "regex": "^/admin/roles(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/roles(?:/)?$"}, {"page": "/admin/roles/new", "regex": "^/admin/roles/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/roles/new(?:/)?$"}, {"page": "/admin/templates", "regex": "^/admin/templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/templates(?:/)?$"}, {"page": "/admin/templates/new", "regex": "^/admin/templates/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/templates/new(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/admin/users/new", "regex": "^/admin/users/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users/new(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}