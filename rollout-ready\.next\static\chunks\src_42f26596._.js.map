{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/admin/templates/new/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface Role {\n  id: number;\n  name: string;\n  description: string;\n}\n\ninterface TemplateTask {\n  description: string;\n  offsetDays: number;\n  isRecurring: boolean;\n  isCritical: boolean;\n}\n\nexport default function NewTemplatePage() {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    description: \"\",\n    roleId: \"\",\n    autoAssign: false,\n  });\n  const [tasks, setTasks] = useState<TemplateTask[]>([]);\n  const [newTask, setNewTask] = useState<TemplateTask>({\n    description: \"\",\n    offsetDays: 0,\n    isRecurring: false,\n    isCritical: false,\n  });\n\n  useEffect(() => {\n    fetchRoles();\n  }, []);\n\n  const fetchRoles = async () => {\n    try {\n      const response = await fetch(\"/api/roles\");\n      if (response.ok) {\n        const rolesData = await response.json();\n        setRoles(rolesData);\n      }\n    } catch (error) {\n      console.error(\"Error fetching roles:\", error);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    try {\n      const templateData = {\n        ...formData,\n        roleId: parseInt(formData.roleId),\n        tasks,\n      };\n\n      const response = await fetch(\"/api/templates\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(templateData),\n      });\n\n      if (response.ok) {\n        router.push(\"/admin/templates\");\n      } else {\n        const error = await response.json();\n        alert(`Error: ${error.message}`);\n      }\n    } catch {\n      alert(\"An error occurred while creating the template\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const value = e.target.type === 'checkbox' ? (e.target as HTMLInputElement).checked : e.target.value;\n    setFormData({\n      ...formData,\n      [e.target.name]: value,\n    });\n  };\n\n  const addTask = () => {\n    if (newTask.description.trim()) {\n      setTasks([...tasks, { ...newTask }]);\n      setNewTask({\n        description: \"\",\n        offsetDays: 0,\n        isRecurring: false,\n        isCritical: false,\n      });\n    }\n  };\n\n  const removeTask = (index: number) => {\n    setTasks(tasks.filter((_, i) => i !== index));\n  };\n\n  const handleNewTaskChange = (field: keyof TemplateTask, value: string | number | boolean) => {\n    setNewTask({\n      ...newTask,\n      [field]: value,\n    });\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Create New Template</h1>\n        <p className=\"text-gray-600 mt-2\">\n          Create a reusable task template for a specific role\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Template Details */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Template Details</CardTitle>\n            <CardDescription>\n              Basic information about the template\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\">Template Name *</Label>\n              <Input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={handleChange}\n                placeholder=\"e.g., Project Manager Checklist\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"roleId\">Role *</Label>\n              <Select value={formData.roleId} onValueChange={(value) => setFormData({...formData, roleId: value})}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select a role\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {roles.map((role) => (\n                    <SelectItem key={role.id} value={role.id.toString()}>\n                      {role.name}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Description</Label>\n              <textarea\n                id=\"description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleChange}\n                placeholder=\"Brief description of this template\"\n                className=\"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <input\n                type=\"checkbox\"\n                id=\"autoAssign\"\n                name=\"autoAssign\"\n                checked={formData.autoAssign}\n                onChange={handleChange}\n                className=\"rounded border-gray-300\"\n              />\n              <Label htmlFor=\"autoAssign\">Auto-assign when role is added to project</Label>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Tasks */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Template Tasks</CardTitle>\n            <CardDescription>\n              Add tasks that will be created when this template is used\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            {/* Add New Task */}\n            <div className=\"border rounded-lg p-4 bg-gray-50\">\n              <h3 className=\"font-semibold mb-4\">Add New Task</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"md:col-span-2\">\n                  <Label>Task Description *</Label>\n                  <Input\n                    value={newTask.description}\n                    onChange={(e) => handleNewTaskChange('description', e.target.value)}\n                    placeholder=\"e.g., Create project charter and scope document\"\n                  />\n                </div>\n                <div>\n                  <Label>Offset Days</Label>\n                  <Input\n                    type=\"number\"\n                    value={newTask.offsetDays}\n                    onChange={(e) => handleNewTaskChange('offsetDays', parseInt(e.target.value) || 0)}\n                    placeholder=\"0\"\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">Days from project start (negative for pre-start)</p>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={newTask.isCritical}\n                      onChange={(e) => handleNewTaskChange('isCritical', e.target.checked)}\n                      className=\"rounded border-gray-300\"\n                    />\n                    <Label>Critical Task</Label>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={newTask.isRecurring}\n                      onChange={(e) => handleNewTaskChange('isRecurring', e.target.checked)}\n                      className=\"rounded border-gray-300\"\n                    />\n                    <Label>Recurring Task</Label>\n                  </div>\n                </div>\n              </div>\n              <Button type=\"button\" onClick={addTask} className=\"mt-4\" disabled={!newTask.description.trim()}>\n                Add Task\n              </Button>\n            </div>\n\n            {/* Task List */}\n            {tasks.length > 0 && (\n              <div>\n                <h3 className=\"font-semibold mb-4\">Template Tasks ({tasks.length})</h3>\n                <div className=\"space-y-2\">\n                  {tasks.map((task, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                      <div className=\"flex-1\">\n                        <p className=\"font-medium\">{task.description}</p>\n                        <div className=\"flex gap-2 mt-1\">\n                          <Badge variant=\"outline\">\n                            {task.offsetDays >= 0 ? `T+${task.offsetDays}` : `T${task.offsetDays}`} days\n                          </Badge>\n                          {task.isCritical && <Badge variant=\"destructive\">Critical</Badge>}\n                          {task.isRecurring && <Badge variant=\"secondary\">Recurring</Badge>}\n                        </div>\n                      </div>\n                      <Button\n                        type=\"button\"\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => removeTask(index)}\n                      >\n                        Remove\n                      </Button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Submit */}\n        <div className=\"flex gap-4\">\n          <Button type=\"submit\" disabled={isLoading || !formData.roleId || tasks.length === 0}>\n            {isLoading ? \"Creating...\" : \"Create Template\"}\n          </Button>\n          <Link href=\"/admin/templates\">\n            <Button type=\"button\" variant=\"outline\">\n              Cancel\n            </Button>\n          </Link>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAyBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,QAAQ;QACR,YAAY;IACd;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACnD,aAAa;QACb,YAAY;QACZ,aAAa;QACb,YAAY;IACd;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,eAAe;gBACnB,GAAG,QAAQ;gBACX,QAAQ,SAAS,SAAS,MAAM;gBAChC;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE;YACjC;QACF,EAAE,OAAM;YACN,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,EAAE,MAAM,CAAC,IAAI,KAAK,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG,EAAE,MAAM,CAAC,KAAK;QACpG,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;QACnB;IACF;IAEA,MAAM,UAAU;QACd,IAAI,QAAQ,WAAW,CAAC,IAAI,IAAI;YAC9B,SAAS;mBAAI;gBAAO;oBAAE,GAAG,OAAO;gBAAC;aAAE;YACnC,WAAW;gBACT,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,YAAY;YACd;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACxC;IAEA,MAAM,sBAAsB,CAAC,OAA2B;QACtD,WAAW;YACT,GAAG,OAAO;YACV,CAAC,MAAM,EAAE;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAS;;;;;;0DACxB,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO,SAAS,MAAM;gDAAE,eAAe,CAAC,QAAU,YAAY;wDAAC,GAAG,QAAQ;wDAAE,QAAQ;oDAAK;;kEAC/F,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;kEACX,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,qIAAA,CAAA,aAAU;gEAAe,OAAO,KAAK,EAAE,CAAC,QAAQ;0EAC9C,KAAK,IAAI;+DADK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;kDAQhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,SAAS,SAAS,UAAU;gDAC5B,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,6LAAC,oIAAA,CAAA,QAAK;gEACJ,OAAO,QAAQ,WAAW;gEAC1B,UAAU,CAAC,IAAM,oBAAoB,eAAe,EAAE,MAAM,CAAC,KAAK;gEAClE,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,6LAAC,oIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,QAAQ,UAAU;gEACzB,UAAU,CAAC,IAAM,oBAAoB,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC/E,aAAY;;;;;;0EAEd,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAE5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,SAAS,QAAQ,UAAU;wEAC3B,UAAU,CAAC,IAAM,oBAAoB,cAAc,EAAE,MAAM,CAAC,OAAO;wEACnE,WAAU;;;;;;kFAEZ,6LAAC,oIAAA,CAAA,QAAK;kFAAC;;;;;;;;;;;;0EAET,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,SAAS,QAAQ,WAAW;wEAC5B,UAAU,CAAC,IAAM,oBAAoB,eAAe,EAAE,MAAM,CAAC,OAAO;wEACpE,WAAU;;;;;;kFAEZ,6LAAC,oIAAA,CAAA,QAAK;kFAAC;;;;;;;;;;;;;;;;;;;;;;;;0DAIb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAS;gDAAS,WAAU;gDAAO,UAAU,CAAC,QAAQ,WAAW,CAAC,IAAI;0DAAI;;;;;;;;;;;;oCAMjG,MAAM,MAAM,GAAG,mBACd,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;oDAAqB;oDAAiB,MAAM,MAAM;oDAAC;;;;;;;0DACjE,6LAAC;gDAAI,WAAU;0DACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAe,KAAK,WAAW;;;;;;kFAC5C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;;oFACZ,KAAK,UAAU,IAAI,IAAI,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,UAAU,EAAE;oFAAC;;;;;;;4EAExE,KAAK,UAAU,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAc;;;;;;4EAChD,KAAK,WAAW,kBAAI,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAY;;;;;;;;;;;;;;;;;;0EAGpD,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,WAAW;0EAC3B;;;;;;;uDAhBO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA4BtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU,aAAa,CAAC,SAAS,MAAM,IAAI,MAAM,MAAM,KAAK;0CAC/E,YAAY,gBAAgB;;;;;;0CAE/B,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;GAnRwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}