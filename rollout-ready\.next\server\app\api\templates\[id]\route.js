(()=>{var e={};e.id=1099,e.ids=[1099],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(96330);let a=globalThis.prisma??new r.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11443:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{DELETE:()=>d,GET:()=>l,PUT:()=>u});var a=s(96559),n=s(48088),i=s(37719),o=s(32190),p=s(5069);async function l(e,{params:t}){try{let e=await t,s=parseInt(e.id);if(isNaN(s))return o.NextResponse.json({message:"Invalid template ID"},{status:400});let r=await p.z.template.findUnique({where:{id:s},include:{role:!0,templateTasks:{orderBy:{offsetDays:"asc"}},_count:{select:{templateTasks:!0}}}});if(!r)return o.NextResponse.json({message:"Template not found"},{status:404});return o.NextResponse.json(r)}catch(e){return console.error("Error fetching template:",e),o.NextResponse.json({message:"Failed to fetch template"},{status:500})}}async function u(e,{params:t}){try{let s=await t,r=parseInt(s.id);if(isNaN(r))return o.NextResponse.json({message:"Invalid template ID"},{status:400});let{name:a,description:n,roleId:i,autoAssign:l,tasks:u}=await e.json();if(!await p.z.template.findUnique({where:{id:r}}))return o.NextResponse.json({message:"Template not found"},{status:404});let d=await p.z.template.update({where:{id:r},data:{name:a,description:n||null,roleId:parseInt(i),autoAssign:l||!1,templateTasks:{deleteMany:{},create:u?.map(e=>({description:e.description,offsetDays:e.offsetDays,isRecurring:e.isRecurring||!1,isCritical:e.isCritical||!1}))||[]}},include:{role:!0,templateTasks:!0}});return o.NextResponse.json(d)}catch(e){return console.error("Error updating template:",e),o.NextResponse.json({message:"Failed to update template"},{status:500})}}async function d(e,{params:t}){try{let e=await t,s=parseInt(e.id);if(isNaN(s))return o.NextResponse.json({message:"Invalid template ID"},{status:400});if(!await p.z.template.findUnique({where:{id:s},include:{_count:{select:{templateTasks:!0}}}}))return o.NextResponse.json({message:"Template not found"},{status:404});if(await p.z.projectTask.findFirst({where:{templateTask:{templateId:s}}}))return o.NextResponse.json({message:"Cannot delete template that is currently used in projects"},{status:400});return await p.z.template.delete({where:{id:s}}),o.NextResponse.json({message:"Template deleted successfully"})}catch(e){return console.error("Error deleting template:",e),o.NextResponse.json({message:"Failed to delete template"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/templates/[id]/route",pathname:"/api/templates/[id]",filename:"route",bundlePath:"app/api/templates/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\templates\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:x}=c;function g(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(11443));module.exports=r})();