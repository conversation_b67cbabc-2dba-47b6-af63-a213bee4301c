(()=>{var e={};e.id=9766,e.ids=[9766],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4011:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var a=t(60687),r=t(85814),i=t.n(r),n=t(32192);let l=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function o({items:e,showHome:s=!0}){return(0,a.jsxs)("nav",{className:"flex items-center space-x-1 text-sm text-gray-500 mb-4",children:[s&&(0,a.jsxs)(a.<PERSON>,{children:[(0,a.jsxs)(i(),{href:"/",className:"flex items-center hover:text-gray-700 transition-colors",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"ml-1",children:"Home"})]}),e.length>0&&(0,a.jsx)(l,{className:"h-4 w-4"})]}),e.map((s,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[s.href?(0,a.jsx)(i(),{href:s.href,className:"hover:text-gray-700 transition-colors",children:s.label}):(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:s.label}),t<e.length-1&&(0,a.jsx)(l,{className:"h-4 w-4 ml-1"})]},t))]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>c});var a=t(60687);t(43210);var r=t(16725),i=t(78272),n=t(13964),l=t(3589),o=t(4780);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:s="default",children:t,...n}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[t,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:s,position:t="popper",...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...i,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(x,{})]})})}function p({className:e,children:s,...t}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...t,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function h({className:e,...s}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(l.A,{className:"size-4"})})}function x({className:e,...s}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21953:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var a=t(60687),r=t(16189),i=t(29523);let n=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var l=t(32192),o=t(85814),d=t.n(o);function c({title:e,description:s,showBackButton:t=!0,backUrl:o,showHomeButton:c=!1,children:u}){let m=(0,r.useRouter)();return(0,a.jsxs)("div",{className:"space-y-4",children:[(t||c)&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t&&(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{o?m.push(o):m.back()},className:"flex items-center gap-2",children:[(0,a.jsx)(n,{className:"h-4 w-4"}),"Back"]}),c&&(0,a.jsx)(d(),{href:"/",children:(0,a.jsxs)(i.$,{variant:"outline",size:"sm",className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"Home"]})})]}),(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:e}),s&&(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:s})]}),u&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:u})]})]})}},28962:(e,s,t)=>{Promise.resolve().then(t.bind(t,66547))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47114:(e,s,t)=>{Promise.resolve().then(t.bind(t,60897))},54300:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var a=t(60687),r=t(43210),i=t(14163),n=r.forwardRef((e,s)=>(0,a.jsx)(i.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=t(4780);function o({className:e,...s}){return(0,a.jsx)(n,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},60897:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(60687),r=t(43210),i=t(16189),n=t(85814),l=t.n(n),o=t(29523),d=t(44493),c=t(89667),u=t(54300),m=t(15079),p=t(4780);let h=r.forwardRef(({className:e,checked:s,onCheckedChange:t,...r},i)=>(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",className:"sr-only peer",ref:i,checked:s,onChange:e=>t?.(e.target.checked),...r}),(0,a.jsx)("div",{className:(0,p.cn)("relative w-11 h-6 bg-gray-200 rounded-full peer peer-checked:bg-blue-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 transition-colors","after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all","peer-checked:after:translate-x-full peer-checked:after:border-white",e)})]}));h.displayName="Switch";var x=t(21953),f=t(4011);function g({params:e}){let s=(0,i.useRouter)(),[t,n]=(0,r.useState)(null),[p,g]=(0,r.useState)(!1),[v,j]=(0,r.useState)(""),[w,b]=(0,r.useState)([]),[y,N]=(0,r.useState)(null),[P,k]=(0,r.useState)({username:"",email:"",firstName:"",lastName:"",systemRole:"USER",jobRoleId:"none",isActive:!0}),[C,R]=(0,r.useState)({newPassword:"",confirmPassword:""}),[A,S]=(0,r.useState)(!1),[$,_]=(0,r.useState)(""),z=e=>{let{name:s,value:t}=e.target;k(e=>({...e,[s]:t}))},U=e=>{let{name:s,value:t}=e.target;R(e=>({...e,[s]:t})),_("")},F=async()=>{if(t){if(!C.newPassword||C.newPassword.length<6)return void _("Password must be at least 6 characters long");if(C.newPassword!==C.confirmPassword)return void _("Passwords do not match");g(!0),_("");try{let e=await fetch(`/api/users/${t}/reset-password`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({newPassword:C.newPassword})}),s=await e.json();e.ok?(R({newPassword:"",confirmPassword:""}),S(!1),alert("Password reset successfully! The user can now login with the new password.")):_(s.message||"Failed to reset password")}catch(e){_("An error occurred. Please try again.")}finally{g(!1)}}},q=async()=>{if(t){g(!0),_("");try{let e=await fetch(`/api/users/${t}/reset-password`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({generateRandom:!0})}),s=await e.json();e.ok?(R({newPassword:"",confirmPassword:""}),S(!1),alert(`Password reset successfully!

Temporary Password: ${s.temporaryPassword}

Please share this password securely with the user. They should change it after first login.`)):_(s.message||"Failed to generate password")}catch(e){_("An error occurred. Please try again.")}finally{g(!1)}}},E=async e=>{if(e.preventDefault(),t){g(!0),j("");try{let e=await fetch(`/api/users/${t}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:P.username,email:P.email,firstName:P.firstName||null,lastName:P.lastName||null,systemRole:P.systemRole,jobRoleId:"none"===P.jobRoleId?null:parseInt(P.jobRoleId),isActive:P.isActive})}),a=await e.json();e.ok?s.push(`/admin/users/${t}`):j(a.message||"Failed to update user")}catch(e){j("An error occurred. Please try again.")}finally{g(!1)}}},J=async()=>{if(confirm(`Are you sure you want to deactivate the user "${y.username}"? This will set their account to inactive.`)){g(!0),j("");try{let e=await fetch(`/api/users/${t}`,{method:"DELETE"});if(e.ok)s.push("/admin/users");else{let s=await e.json();j(s.message||"Failed to deactivate user")}}catch(e){j("An error occurred while deactivating the user")}finally{g(!1)}}};if(!y)return(0,a.jsx)("div",{children:"Loading..."});let T=y.firstName&&y.lastName?`${y.firstName} ${y.lastName}`:y.username;return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(f.default,{items:[{label:"Admin",href:"/admin"},{label:"Users",href:"/admin/users"},{label:T,href:`/admin/users/${t}`},{label:"Edit"}]}),(0,a.jsx)(x.default,{title:`Edit User: ${T}`,description:"Update user information and permissions",backUrl:`/admin/users/${t}`}),(0,a.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"User Information"}),(0,a.jsx)(d.BT,{children:"Update user details and credentials"})]}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[v&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:v}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"firstName",children:"First Name"}),(0,a.jsx)(c.p,{id:"firstName",name:"firstName",type:"text",value:P.firstName,onChange:z,placeholder:"John"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"lastName",children:"Last Name"}),(0,a.jsx)(c.p,{id:"lastName",name:"lastName",type:"text",value:P.lastName,onChange:z,placeholder:"Doe"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"username",children:"Username *"}),(0,a.jsx)(c.p,{id:"username",name:"username",type:"text",required:!0,value:P.username,onChange:z,placeholder:"johndoe"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"email",children:"Email *"}),(0,a.jsx)(c.p,{id:"email",name:"email",type:"email",required:!0,value:P.email,onChange:z,placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"systemRole",children:"System Role *"}),(0,a.jsxs)(m.l6,{value:P.systemRole,onValueChange:e=>{k(s=>({...s,systemRole:e}))},children:[(0,a.jsx)(m.bq,{children:(0,a.jsx)(m.yv,{placeholder:"Select a system role"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"USER",children:"User - Can view and update assigned tasks"}),(0,a.jsx)(m.eb,{value:"MANAGER",children:"Manager - Can create projects and assign users"}),(0,a.jsx)(m.eb,{value:"ADMIN",children:"Admin - Full system access"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"jobRole",children:"Job Role"}),(0,a.jsxs)(m.l6,{value:P.jobRoleId,onValueChange:e=>{k(s=>({...s,jobRoleId:e}))},children:[(0,a.jsx)(m.bq,{children:(0,a.jsx)(m.yv,{placeholder:"Select a job role (optional)"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"none",children:"No specific job role"}),w.map(e=>(0,a.jsxs)(m.eb,{value:e.id.toString(),children:[e.name,e.description&&` - ${e.description}`]},e.id))]})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Job role determines which project roles this user can be assigned to"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h,{id:"isActive",checked:P.isActive,onCheckedChange:e=>{k(s=>({...s,isActive:e}))}}),(0,a.jsx)(u.J,{htmlFor:"isActive",children:"Account is active"})]})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center justify-between",children:["Password Management",(0,a.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>S(!A),children:A?"Cancel":"Reset Password"})]}),(0,a.jsx)(d.BT,{children:"Reset the user's password. The user will need to use the new password to login."})]}),A&&(0,a.jsxs)(d.Wu,{className:"space-y-4",children:[$&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:$}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"newPassword",children:"New Password *"}),(0,a.jsx)(c.p,{id:"newPassword",name:"newPassword",type:"password",value:C.newPassword,onChange:U,placeholder:"Enter new password",minLength:6}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Minimum 6 characters"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"confirmPassword",children:"Confirm New Password *"}),(0,a.jsx)(c.p,{id:"confirmPassword",name:"confirmPassword",type:"password",value:C.confirmPassword,onChange:U,placeholder:"Confirm new password",minLength:6})]})]}),(0,a.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,a.jsx)(o.$,{type:"button",onClick:F,disabled:p||!C.newPassword||!C.confirmPassword,variant:"destructive",children:p?"Resetting...":"Set Password"}),(0,a.jsx)(o.$,{type:"button",onClick:q,disabled:p,variant:"secondary",children:p?"Generating...":"Generate Random Password"}),(0,a.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>{S(!1),R({newPassword:"",confirmPassword:""}),_("")},children:"Cancel"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-sm text-blue-800",children:(0,a.jsx)("strong",{children:"Options:"})}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 mt-1 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Set Password:"})," Enter a specific password for the user"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Generate Random:"})," Create a secure random password automatically"]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(o.$,{type:"submit",disabled:p,children:p?"Updating...":"Update User"}),(0,a.jsx)(l(),{href:`/admin/users/${t}`,children:(0,a.jsx)(o.$,{type:"button",variant:"outline",children:"Cancel"})}),(0,a.jsx)(o.$,{type:"button",variant:"destructive",onClick:J,disabled:p,children:p?"Deactivating...":"Deactivate User"})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66547:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\app\\\\admin\\\\users\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\users\\[id]\\edit\\page.tsx","default")},67155:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["admin",{children:["users",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,66547)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\users\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\users\\[id]\\edit\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/users/[id]/edit/page",pathname:"/admin/users/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(60687);t(43210);var r=t(4780);function i({className:e,type:s,...t}){return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,7648,1658,425,5811],()=>t(67155));module.exports=a})();