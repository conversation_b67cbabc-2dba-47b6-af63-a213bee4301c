import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET() {
  try {
    const templates = await prisma.template.findMany({
      include: {
        role: true,
        templateTasks: true,
        _count: {
          select: {
            templateTasks: true,
          },
        },
      },
      orderBy: [
        { role: { name: 'asc' } },
        { name: 'asc' },
      ],
    });

    return NextResponse.json(templates);
  } catch (error) {
    console.error("Error fetching templates:", error);
    return NextResponse.json(
      { message: "Failed to fetch templates" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, roleId, autoAssign, tasks } = body;

    if (!name || name.trim() === "") {
      return NextResponse.json(
        { message: "Template name is required" },
        { status: 400 }
      );
    }

    if (!roleId) {
      return NextResponse.json(
        { message: "Role is required" },
        { status: 400 }
      );
    }

    if (!tasks || tasks.length === 0) {
      return NextResponse.json(
        { message: "At least one task is required" },
        { status: 400 }
      );
    }

    // Check if role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      return NextResponse.json(
        { message: "Role not found" },
        { status: 400 }
      );
    }

    // Create the template
    const template = await prisma.template.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        roleId: roleId,
        autoAssign: autoAssign || false,
      },
    });

    // Create template tasks
    const templateTasks = await Promise.all(
      tasks.map((task: any) =>
        prisma.templateTask.create({
          data: {
            templateId: template.id,
            description: task.description.trim(),
            offsetDays: task.offsetDays || 0,
            isRecurring: task.isRecurring || false,
            isCritical: task.isCritical || false,
          },
        })
      )
    );

    return NextResponse.json({
      template,
      templateTasks: templateTasks.length,
    }, { status: 201 });

  } catch (error) {
    console.error("Error creating template:", error);
    return NextResponse.json(
      { message: "Failed to create template" },
      { status: 500 }
    );
  }
}
