(()=>{var e={};e.id=698,e.ids=[698],e.modules={84:(e,r,t)=>{"use strict";t.d(r,{E:()=>o});var s=t(7413);t(1120);var a=t(403),n=t(662),i=t(974);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,asChild:t=!1,...n}){let o=t?a.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(d({variant:r}),e),...n})}},440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(5986),a=t(8974);function n(...e){return(0,a.QP)((0,s.$)(e))}},1132:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(7413),a=t(4536),n=t.n(a),i=t(8963),d=t(3469),o=t(84),c=t(5069);async function l(){let[e,r,t,s]=await Promise.all([c.z.role.count(),c.z.template.count(),c.z.project.count(),c.z.projectTask.count()]);return{rolesCount:e,templatesCount:r,projectsCount:t,tasksCount:s}}async function u(){return await c.z.project.findMany({take:5,orderBy:{createdAt:"desc"},include:{projectRoles:{include:{role:!0}},_count:{select:{projectTasks:!0}}}})}async function x(){let e=await l(),r=await u();return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage roles, templates, and projects for Rollout Ready"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Roles"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.rolesCount})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Templates"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.templatesCount})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Active Projects"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.projectsCount})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Tasks"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.tasksCount})})]})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Manage Roles"}),(0,s.jsx)(i.BT,{children:"Create and manage project roles"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)(n(),{href:"/admin/roles",children:(0,s.jsx)(d.$,{className:"w-full",children:"Manage Roles"})})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Manage Templates"}),(0,s.jsx)(i.BT,{children:"Create and edit task templates"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)(n(),{href:"/admin/templates",children:(0,s.jsx)(d.$,{className:"w-full",children:"Manage Templates"})})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Create Project"}),(0,s.jsx)(i.BT,{children:"Start a new implementation project"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)(n(),{href:"/admin/projects/new",children:(0,s.jsx)(d.$,{className:"w-full",children:"Create Project"})})})]})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Recent Projects"}),(0,s.jsx)(i.BT,{children:"Latest projects and their status"})]}),(0,s.jsx)(i.Wu,{children:0===r.length?(0,s.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No projects created yet"}):(0,s.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Start Date: ",new Date(e.startDate).toLocaleDateString()]}),(0,s.jsx)("div",{className:"flex gap-2 mt-2",children:e.projectRoles.map(e=>(0,s.jsxs)(o.E,{variant:"secondary",children:[e.role.name,": ",e.userName]},e.id))})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[e._count.projectTasks," tasks"]}),(0,s.jsx)(n(),{href:`/admin/projects/${e.id}`,children:(0,s.jsx)(d.$,{variant:"outline",size:"sm",className:"mt-2",children:"View Project"})})]})]},e.id))})})]})]})}},1328:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3469:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(7413);t(1120);var a=t(403),n=t(662),i=t(974);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:r,size:t,asChild:n=!1,...o}){let c=n?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:t,className:e})),...o})}},3873:e=>{"use strict";e.exports=require("path")},5069:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var s=t(6330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},6330:e=>{"use strict";e.exports=require("@prisma/client")},7776:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},8115:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>c});var s=t(5239),a=t(8088),n=t(8170),i=t.n(n),d=t(893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8963:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var s=t(7413);t(1120);var a=t(974);function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,982,277,923,287],()=>t(8115));module.exports=s})();