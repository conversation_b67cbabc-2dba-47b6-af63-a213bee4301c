import Link from "next/link";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function Home() {
  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Welcome to Rollout Ready
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          A role-based checklist and task management system designed to ensure that
          no steps are missed during large-scale implementation projects.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Admin Dashboard</CardTitle>
            <CardDescription>
              Manage roles, templates, and create new projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/admin">
              <Button className="w-full">Go to <PERSON>min</Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Dashboards</CardTitle>
            <CardDescription>
              View tasks assigned to specific team members
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Link href="/dashboard/ollie">
                <Button variant="outline" className="w-full">Ollie's Tasks</Button>
              </Link>
              <Link href="/dashboard/sarah">
                <Button variant="outline" className="w-full">Sarah's Tasks</Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Start</CardTitle>
            <CardDescription>
              Get started with your first project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/admin/projects/new">
              <Button className="w-full">Create Project</Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Features Overview */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Key Features</h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Role-Based Management</h3>
            <p className="text-gray-600">Assign specific roles to team members and automatically generate relevant tasks.</p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Template System</h3>
            <p className="text-gray-600">Create reusable templates with predefined tasks and timelines.</p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Task Tracking</h3>
            <p className="text-gray-600">Monitor progress with status updates and due date management.</p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">User Dashboards</h3>
            <p className="text-gray-600">Personalized views showing only relevant tasks for each team member.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
