Write-Host "🚀 LAUNCHING ROLLOUT READY PWA..." -ForegroundColor Green
Write-Host ""

Write-Host "📦 Building application..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "✅ Build complete!" -ForegroundColor Green
Write-Host "🌐 Starting server..." -ForegroundColor Yellow
Write-Host ""
Write-Host "📱 PWA will be available at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "💡 Press Ctrl+C to stop the server" -ForegroundColor Gray
Write-Host ""

npm start
