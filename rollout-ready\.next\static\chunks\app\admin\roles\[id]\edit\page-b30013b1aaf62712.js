(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[467],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var n=r(5155);r(2115);var s=r(9708),a=r(2085),i=r(9434);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:a,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:a,className:t})),...d})}},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(2596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let a=s(t)||s(n);return i[e][a]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(5155);r(2115);var s=r(9434);function a(e){let{className:t,type:r,...a}=e;return(0,n.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...a})}},2535:(e,t,r)=>{Promise.resolve().then(r.bind(r,9945))},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>o,sG:()=>l});var n=r(2115),s=r(7650),a=r(9708),i=r(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),s=n.forwardRef((e,n)=>{let{asChild:s,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?r:t,{...a,ref:n})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function o(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>a});var n=r(2115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>a,aR:()=>i});var n=r(5155);r(2115);var s=r(9434);function a(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},8979:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var n=r(5155),s=r(2115),a=r(3655),i=s.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=r(9434);function o(e){let{className:t,...r}=e;return(0,n.jsx)(i,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(2596),s=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,TL:()=>i});var n=r(2115),s=r(6101),a=r(5155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var i;let e,l,o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let n in t){let s=e[n],a=t[n];/^on[A-Z]/.test(n)?s&&a?r[n]=(...e)=>{let t=a(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...a}:"className"===n&&(r[n]=[s,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,s.t)(t,o):o),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...i}=e,l=n.Children.toArray(s),o=l.find(d);if(o){let e=o.props.children,s=l.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,a.jsx)(t,{...i,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},9945:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(5155),s=r(2115),a=r(5695),i=r(6874),l=r.n(i),o=r(6695),d=r(285),c=r(2523),u=r(8979);function p(e){let{params:t}=e,r=(0,a.useRouter)(),[i,p]=(0,s.useState)(!1),[f,x]=(0,s.useState)(null),[m,h]=(0,s.useState)({name:"",description:""}),[v,g]=(0,s.useState)("");(0,s.useEffect)(()=>{b()},[]);let b=async()=>{try{let e=await t,r=parseInt(e.id);if(isNaN(r))return void g("Invalid role ID");let n=await fetch("/api/roles/".concat(r));if(!n.ok)return void g("Role not found");let s=await n.json();x(s),h({name:s.name,description:s.description||""})}catch(e){g("Failed to load role data")}},j=async e=>{if(e.preventDefault(),f){p(!0),g("");try{let e=await fetch("/api/roles/".concat(f.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)});if(e.ok)r.push("/admin/roles/".concat(f.id));else{let t=await e.json();g(t.message||"Failed to update role")}}catch(e){g("An error occurred while updating the role")}finally{p(!1)}}},y=e=>{h({...m,[e.target.name]:e.target.value})},N=async()=>{if(f){if(f._count.projectRoles>0)return void g("Cannot delete role that is currently used in projects");if(confirm('Are you sure you want to delete the role "'.concat(f.name,'"? This action cannot be undone.'))){p(!0),g("");try{let e=await fetch("/api/roles/".concat(f.id),{method:"DELETE"});if(e.ok)r.push("/admin/roles");else{let t=await e.json();g(t.message||"Failed to delete role")}}catch(e){g("An error occurred while deleting the role")}finally{p(!1)}}}};return v?(0,n.jsx)("div",{className:"space-y-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Error"}),(0,n.jsx)("p",{className:"text-red-600 mt-2",children:v}),(0,n.jsx)(l(),{href:"/admin/roles",children:(0,n.jsx)(d.$,{className:"mt-4",children:"Back to Roles"})})]})}):f?(0,n.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Edit Role: ",f.name]}),(0,n.jsx)("p",{className:"text-gray-600 mt-2",children:"Modify role details and settings"})]}),(0,n.jsxs)("form",{onSubmit:j,className:"space-y-8",children:[(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{children:"Role Details"}),(0,n.jsx)(o.BT,{children:"Update basic role information"})]}),(0,n.jsxs)(o.Wu,{className:"space-y-6",children:[v&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:v}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"name",children:"Role Name *"}),(0,n.jsx)(c.p,{id:"name",name:"name",type:"text",required:!0,value:m.name,onChange:y,placeholder:"e.g., Project Manager, Infrastructure Lead"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"description",children:"Description"}),(0,n.jsx)("textarea",{id:"description",name:"description",value:m.description,onChange:y,placeholder:"Brief description of this role's responsibilities",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{children:"Role Usage"}),(0,n.jsx)(o.BT,{children:"Current usage of this role in the system"})]}),(0,n.jsx)(o.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Templates"}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:f._count.templates}),(0,n.jsx)("p",{className:"text-xs text-gray-600",children:"Associated templates"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:"Project Usage"}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:f._count.projectRoles}),(0,n.jsx)("p",{className:"text-xs text-gray-600",children:"Times used in projects"})]})]})})]}),(0,n.jsxs)("div",{className:"flex gap-4",children:[(0,n.jsx)(d.$,{type:"submit",disabled:i,children:i?"Updating...":"Update Role"}),(0,n.jsx)(l(),{href:"/admin/roles/".concat(f.id),children:(0,n.jsx)(d.$,{type:"button",variant:"outline",children:"Cancel"})}),0===f._count.projectRoles&&(0,n.jsx)(d.$,{type:"button",variant:"destructive",onClick:N,disabled:i,children:"Delete Role"})]})]}),f._count.projectRoles>0&&(0,n.jsxs)(o.Zp,{children:[(0,n.jsx)(o.aR,{children:(0,n.jsx)(o.ZB,{children:"Cannot Delete"})}),(0,n.jsx)(o.Wu,{children:(0,n.jsxs)("p",{className:"text-gray-600",children:["This role cannot be deleted because it is currently used in ",f._count.projectRoles," project",1!==f._count.projectRoles?"s":"",". Remove the role from all projects before deleting it."]})})]})]}):(0,n.jsx)("div",{className:"space-y-8",children:(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Loading..."})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[277,874,441,684,358],()=>t(2535)),_N_E=e.O()}]);