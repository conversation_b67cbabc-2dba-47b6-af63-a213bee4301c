(()=>{var e={};e.id=848,e.ids=[848],e.modules={84:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(7413);r(1120);var o=r(403),n=r(662),a=r(974);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?o.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,a.cn)(i({variant:t}),e),...n})}},401:(e,t,r)=>{"use strict";r.d(t,{Table:()=>o,TableBody:()=>a,TableCell:()=>d,TableHead:()=>i,TableHeader:()=>n,TableRow:()=>l});var s=r(2907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","Table"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHeader"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableBody");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableFooter");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHead"),l=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableRow"),d=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCell");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCaption")},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(5986),o=r(8974);function n(...e){return(0,o.QP)((0,s.$)(e))}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3469:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(7413);r(1120);var o=r(403),n=r(662),a=r(974);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let d=n?o.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,a.cn)(i({variant:t,size:r,className:e})),...l})}},3873:e=>{"use strict";e.exports=require("path")},5069:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var s=r(6330);let o=globalThis.prisma??new s.PrismaClient({log:["query"]})},6211:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>i,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>a,TableRow:()=>l});var s=r(687);r(3210);var o=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm",e),...t})})}function a({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},6330:e=>{"use strict";e.exports=require("@prisma/client")},7538:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(7413),o=r(4536),n=r.n(o),a=r(8963),i=r(3469),l=r(84),d=r(401),c=r(5069);async function u(){return await c.z.role.findMany({include:{templates:!0,_count:{select:{templates:!0,projectRoles:!0}}},orderBy:{name:"asc"}})}async function p(){let e=await u();return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Roles"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Create and manage project roles and their associated templates"})]}),(0,s.jsx)(n(),{href:"/admin/roles/new",children:(0,s.jsx)(i.$,{children:"Create New Role"})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"All Roles"}),(0,s.jsx)(a.BT,{children:"Overview of all project roles in the system"})]}),(0,s.jsx)(a.Wu,{children:0===e.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"No roles created yet"}),(0,s.jsx)(n(),{href:"/admin/roles/new",children:(0,s.jsx)(i.$,{children:"Create Your First Role"})})]}):(0,s.jsxs)(d.Table,{children:[(0,s.jsx)(d.TableHeader,{children:(0,s.jsxs)(d.TableRow,{children:[(0,s.jsx)(d.TableHead,{children:"Role Name"}),(0,s.jsx)(d.TableHead,{children:"Description"}),(0,s.jsx)(d.TableHead,{children:"Templates"}),(0,s.jsx)(d.TableHead,{children:"Projects Used"}),(0,s.jsx)(d.TableHead,{children:"Actions"})]})}),(0,s.jsx)(d.TableBody,{children:e.map(e=>(0,s.jsxs)(d.TableRow,{children:[(0,s.jsx)(d.TableCell,{className:"font-medium",children:e.name}),(0,s.jsx)(d.TableCell,{children:e.description||"No description"}),(0,s.jsx)(d.TableCell,{children:(0,s.jsxs)(l.E,{variant:"secondary",children:[e._count.templates," template",1!==e._count.templates?"s":""]})}),(0,s.jsx)(d.TableCell,{children:(0,s.jsxs)(l.E,{variant:"outline",children:[e._count.projectRoles," project",1!==e._count.projectRoles?"s":""]})}),(0,s.jsx)(d.TableCell,{children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n(),{href:`/admin/roles/${e.id}`,children:(0,s.jsx)(i.$,{variant:"outline",size:"sm",children:"View"})}),(0,s.jsx)(n(),{href:`/admin/roles/${e.id}/edit`,children:(0,s.jsx)(i.$,{variant:"outline",size:"sm",children:"Edit"})})]})})]},e.id))})]})})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{children:(0,s.jsx)(a.ZB,{className:"text-lg",children:"Total Roles"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold",children:e.length})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{children:(0,s.jsx)(a.ZB,{className:"text-lg",children:"Total Templates"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold",children:e.reduce((e,t)=>e+t._count.templates,0)})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{children:(0,s.jsx)(a.ZB,{className:"text-lg",children:"Active in Projects"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold",children:e.reduce((e,t)=>e+t._count.projectRoles,0)})})]})]})]})}},7540:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,401))},8963:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>a});var s=r(7413);r(1120);var o=r(974);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...t})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9531:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(5239),o=r(8088),n=r(8170),a=r.n(n),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["roles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7538)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/roles/page",pathname:"/admin/roles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9551:e=>{"use strict";e.exports=require("url")},9748:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5814,23)),Promise.resolve().then(r.bind(r,6211))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,982,277,923,287],()=>r(9531));module.exports=s})();