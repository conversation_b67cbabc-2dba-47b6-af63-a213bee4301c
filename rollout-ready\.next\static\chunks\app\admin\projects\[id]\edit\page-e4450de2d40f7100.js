(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[26],{2796:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>m});var t=s(5155),r=s(2115),i=s(5695),c=s(6874),n=s.n(c),l=s(6695),d=s(285),o=s(2523),h=s(8979),p=s(4963);function m(e){let{params:a}=e,s=(0,i.useRouter)(),[c,m]=(0,r.useState)(!1),[x,j]=(0,r.useState)(null),[u,f]=(0,r.useState)([]),[N,y]=(0,r.useState)({name:"",description:"",startDate:""}),[v,g]=(0,r.useState)({}),[b,w]=(0,r.useState)("");(0,r.useEffect)(()=>{D()},[]);let D=async()=>{try{let e=await a,s=parseInt(e.id);if(isNaN(s))return void w("Invalid project ID");let t=await fetch("/api/projects/".concat(s));if(!t.ok)return void w("Project not found");let r=await t.json();j(r),y({name:r.name,description:r.description||"",startDate:r.startDate.split("T")[0]});let i={};r.projectRoles.forEach(e=>{i[e.roleId]=e.userId}),g(i);let c=await fetch("/api/roles");if(c.ok){let e=await c.json();f(e)}}catch(e){w("Failed to load project data")}},S=async e=>{if(e.preventDefault(),x){m(!0),w("");try{let e={...N,roleAssignments:v},a=await fetch("/api/projects/".concat(x.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(a.ok)s.push("/admin/projects/".concat(x.id));else{let e=await a.json();w(e.message||"Failed to update project")}}catch(e){w("An error occurred while updating the project")}finally{m(!1)}}},k=e=>{y({...N,[e.target.name]:e.target.value})},C=(e,a)=>{g({...v,[e]:a})};return b?(0,t.jsx)("div",{className:"space-y-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Error"}),(0,t.jsx)("p",{className:"text-red-600 mt-2",children:b}),(0,t.jsx)(n(),{href:"/admin",children:(0,t.jsx)(d.$,{className:"mt-4",children:"Back to Admin"})})]})}):x?(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Edit Project: ",x.name]}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Modify project details and team assignments"})]}),(0,t.jsxs)("form",{onSubmit:S,className:"space-y-8",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Project Details"}),(0,t.jsx)(l.BT,{children:"Update basic project information"})]}),(0,t.jsxs)(l.Wu,{className:"space-y-6",children:[b&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:b}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"name",children:"Project Name *"}),(0,t.jsx)(o.p,{id:"name",name:"name",type:"text",required:!0,value:N.name,onChange:k,placeholder:"e.g., Deploy MES at Avonmouth"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"description",children:"Description"}),(0,t.jsx)("textarea",{id:"description",name:"description",value:N.description,onChange:k,placeholder:"Brief description of the project",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"startDate",children:"Start Date *"}),(0,t.jsx)(o.p,{id:"startDate",name:"startDate",type:"date",required:!0,value:N.startDate,onChange:k})]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Team Assignments"}),(0,t.jsx)(l.BT,{children:"Assign team members to project roles"})]}),(0,t.jsx)(l.Wu,{children:0===u.length?(0,t.jsx)("p",{className:"text-gray-500",children:"Loading roles..."}):(0,t.jsx)("div",{className:"space-y-4",children:u.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,t.jsx)("div",{className:"w-64",children:(0,t.jsx)(p.A,{value:v[e.id],onValueChange:a=>C(e.id,a),placeholder:"Select user for ".concat(e.name),excludeUserIds:Object.values(v).filter(a=>void 0!==a&&a!==v[e.id]),projectRoleName:e.name})})]},e.id))})})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(d.$,{type:"submit",disabled:c,children:c?"Updating...":"Update Project"}),(0,t.jsx)(n(),{href:"/admin/projects/".concat(x.id),children:(0,t.jsx)(d.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})]}):(0,t.jsx)("div",{className:"space-y-8",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Loading..."})})})}},5200:(e,a,s)=>{Promise.resolve().then(s.bind(s,2796))}},e=>{var a=a=>e(e.s=a);e.O(0,[277,874,537,667,441,684,358],()=>a(5200)),_N_E=e.O()}]);