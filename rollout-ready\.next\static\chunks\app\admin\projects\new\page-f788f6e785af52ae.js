(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[912],{6082:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var t=a(5155),r=a(2115),l=a(5695),i=a(6874),n=a.n(i),c=a(6695),d=a(285),o=a(2523),m=a(8979),h=a(4963);function p(){let e=(0,l.useRouter)(),[s,a]=(0,r.useState)(!1),[i,p]=(0,r.useState)([]),[x,j]=(0,r.useState)({name:"",description:"",startDate:""}),[u,f]=(0,r.useState)({});(0,r.useEffect)(()=>{b()},[]);let b=async()=>{try{let e=await fetch("/api/roles");if(e.ok){let s=await e.json();p(s)}}catch(e){console.error("Error fetching roles:",e)}},g=async s=>{s.preventDefault(),a(!0);try{let s={...x,roleAssignments:u},a=await fetch("/api/projects",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(a.ok)e.push("/admin");else{let e=await a.json();alert("Error: ".concat(e.message))}}catch(e){alert("An error occurred while creating the project")}finally{a(!1)}},y=e=>{j({...x,[e.target.name]:e.target.value})},N=(e,s)=>{f({...u,[e]:s})};return(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Project"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Set up a new implementation project with templates and team assignments"}),(0,t.jsxs)("div",{className:"mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"Project Creation Workflow:"}),(0,t.jsxs)("ol",{className:"list-decimal list-inside text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"Define project details (name, description, start date)"}),(0,t.jsx)("li",{children:"Assign team members to project roles"}),(0,t.jsx)("li",{children:"System automatically applies relevant task templates"}),(0,t.jsx)("li",{children:"Tasks are generated based on role assignments and templates"})]})]})]}),(0,t.jsxs)("form",{onSubmit:g,className:"space-y-8",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Project Details"}),(0,t.jsx)(c.BT,{children:"Basic information about the project"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"name",children:"Project Name *"}),(0,t.jsx)(o.p,{id:"name",name:"name",type:"text",required:!0,value:x.name,onChange:y,placeholder:"e.g., Deploy MES at Avonmouth"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"description",children:"Description"}),(0,t.jsx)("textarea",{id:"description",name:"description",value:x.description,onChange:y,placeholder:"Brief description of the project",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"startDate",children:"Start Date *"}),(0,t.jsx)(o.p,{id:"startDate",name:"startDate",type:"date",required:!0,value:x.startDate,onChange:y})]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Team Assignments"}),(0,t.jsx)(c.BT,{children:"Assign team members to project roles. Task templates will be automatically applied based on these assignments."})]}),(0,t.jsx)(c.Wu,{children:0===i.length?(0,t.jsx)("p",{className:"text-gray-500",children:"No roles available. Please create roles first."}):(0,t.jsx)("div",{className:"space-y-4",children:i.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,t.jsx)("div",{className:"w-64",children:(0,t.jsx)(h.A,{value:u[e.id],onValueChange:s=>N(e.id,s),placeholder:"Select user for ".concat(e.name),excludeUserIds:Object.values(u).filter(s=>void 0!==s&&s!==u[e.id]),projectRoleName:e.name})})]},e.id))})})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(d.$,{type:"submit",disabled:s,children:s?"Creating...":"Create Project"}),(0,t.jsx)(n(),{href:"/admin",children:(0,t.jsx)(d.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})]})}},7997:(e,s,a)=>{Promise.resolve().then(a.bind(a,6082))}},e=>{var s=s=>e(e.s=s);e.O(0,[277,874,537,667,441,684,358],()=>s(7997)),_N_E=e.O()}]);