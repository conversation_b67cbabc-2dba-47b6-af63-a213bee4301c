(()=>{var e={};e.id=597,e.ids=[597],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>o});var r=s(96330);let o=globalThis.prisma??new r.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},81108:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>R,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var r={};s.r(r),s.d(r,{DELETE:()=>p,GET:()=>l,PUT:()=>d});var o=s(96559),n=s(48088),a=s(37719),i=s(32190),u=s(5069);async function l(e,{params:t}){try{let e=await t,s=parseInt(e.id);if(isNaN(s))return i.NextResponse.json({message:"Invalid role ID"},{status:400});let r=await u.z.role.findUnique({where:{id:s},include:{templates:{include:{_count:{select:{templateTasks:!0}}},orderBy:{name:"asc"}},_count:{select:{templates:!0,projectRoles:!0}}}});if(!r)return i.NextResponse.json({message:"Role not found"},{status:404});return i.NextResponse.json(r)}catch(e){return console.error("Error fetching role:",e),i.NextResponse.json({message:"Failed to fetch role"},{status:500})}}async function d(e,{params:t}){try{let s=await t,r=parseInt(s.id);if(isNaN(r))return i.NextResponse.json({message:"Invalid role ID"},{status:400});let{name:o,description:n}=await e.json();if(!o||""===o.trim())return i.NextResponse.json({message:"Role name is required"},{status:400});if(!await u.z.role.findUnique({where:{id:r}}))return i.NextResponse.json({message:"Role not found"},{status:404});if(await u.z.role.findFirst({where:{name:o.trim(),id:{not:r}}}))return i.NextResponse.json({message:"A role with this name already exists"},{status:400});let a=await u.z.role.update({where:{id:r},data:{name:o.trim(),description:n?.trim()||null},include:{_count:{select:{templates:!0,projectRoles:!0}}}});return i.NextResponse.json(a)}catch(e){return console.error("Error updating role:",e),i.NextResponse.json({message:"Failed to update role"},{status:500})}}async function p(e,{params:t}){try{let e=await t,s=parseInt(e.id);if(isNaN(s))return i.NextResponse.json({message:"Invalid role ID"},{status:400});let r=await u.z.role.findUnique({where:{id:s},include:{_count:{select:{projectRoles:!0,templates:!0}}}});if(!r)return i.NextResponse.json({message:"Role not found"},{status:404});if(r._count.projectRoles>0)return i.NextResponse.json({message:"Cannot delete role that is currently used in projects"},{status:400});return await u.z.role.delete({where:{id:s}}),i.NextResponse.json({message:"Role deleted successfully"})}catch(e){return console.error("Error deleting role:",e),i.NextResponse.json({message:"Failed to delete role"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/roles/[id]/route",pathname:"/api/roles/[id]",filename:"route",bundlePath:"app/api/roles/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\roles\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:R}=c;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(81108));module.exports=r})();