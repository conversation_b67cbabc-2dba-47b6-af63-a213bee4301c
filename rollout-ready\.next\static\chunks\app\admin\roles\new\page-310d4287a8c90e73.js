(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[885],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var n=t(5155);t(2115);var i=t(9708),a=t(2085),s=t(9434);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:a,asChild:o=!1,...d}=e,c=o?i.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,s.cn)(l({variant:t,size:a,className:r})),...d})}},2085:(e,r,t)=>{"use strict";t.d(r,{F:()=>s});var n=t(2596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,s=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:l}=r,o=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],n=null==l?void 0:l[e];if(null===r)return null;let a=i(r)||i(n);return s[e][a]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return a(e,o,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...i}=r;return Object.entries(i).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...d}[r]):({...l,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var n=t(5155);t(2115);var i=t(9434);function a(e){let{className:r,type:t,...a}=e;return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}},3655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>o,sG:()=>l});var n=t(2115),i=t(7650),a=t(9708),s=t(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,a.TL)(`Primitive.${r}`),i=n.forwardRef((e,n)=>{let{asChild:i,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(i?t:r,{...a,ref:n})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{});function o(e,r){e&&i.flushSync(()=>e.dispatchEvent(r))}},4300:(e,r,t)=>{Promise.resolve().then(t.bind(t,8811))},5695:(e,r,t)=>{"use strict";var n=t(8999);t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>s,t:()=>a});var n=t(2115);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}function s(...e){return n.useCallback(a(...e),e)}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>a,aR:()=>s});var n=t(5155);t(2115);var i=t(9434);function a(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function s(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function l(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",r),...t})}},8811:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var n=t(5155),i=t(2115),a=t(5695),s=t(6874),l=t.n(s),o=t(6695),d=t(285),c=t(2523),u=t(8979);function p(){let e=(0,a.useRouter)(),[r,t]=(0,i.useState)(!1),[s,p]=(0,i.useState)({name:"",description:""}),f=async r=>{r.preventDefault(),t(!0);try{let r=await fetch("/api/roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(r.ok)e.push("/admin/roles");else{let e=await r.json();alert("Error: ".concat(e.message))}}catch(e){alert("An error occurred while creating the role")}finally{t(!1)}},m=e=>{p({...s,[e.target.name]:e.target.value})};return(0,n.jsxs)("div",{className:"max-w-2xl mx-auto space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Role"}),(0,n.jsx)("p",{className:"text-gray-600 mt-2",children:"Add a new project role to the system"})]}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{children:"Role Details"}),(0,n.jsx)(o.BT,{children:"Enter the basic information for the new role"})]}),(0,n.jsx)(o.Wu,{children:(0,n.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"name",children:"Role Name *"}),(0,n.jsx)(c.p,{id:"name",name:"name",type:"text",required:!0,value:s.name,onChange:m,placeholder:"e.g., Project Manager, Infrastructure Lead"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"description",children:"Description"}),(0,n.jsx)("textarea",{id:"description",name:"description",value:s.description,onChange:m,placeholder:"Brief description of this role's responsibilities",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,n.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,n.jsx)(d.$,{type:"submit",disabled:r,children:r?"Creating...":"Create Role"}),(0,n.jsx)(l(),{href:"/admin/roles",children:(0,n.jsx)(d.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})})]}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsx)(o.aR,{children:(0,n.jsx)(o.ZB,{children:"Next Steps"})}),(0,n.jsxs)(o.Wu,{children:[(0,n.jsx)("p",{className:"text-gray-600",children:"After creating this role, you can:"}),(0,n.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1 text-gray-600",children:[(0,n.jsx)("li",{children:"Create templates associated with this role"}),(0,n.jsx)("li",{children:"Assign this role to team members in projects"}),(0,n.jsx)("li",{children:"Set up auto-assignment rules for templates"})]})]})]})]})}},8979:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var n=t(5155),i=t(2115),a=t(3655),s=i.forwardRef((e,r)=>(0,n.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));s.displayName="Label";var l=t(9434);function o(e){let{className:r,...t}=e;return(0,n.jsx)(s,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var n=t(2596),i=t(9688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,i.QP)((0,n.$)(r))}},9708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,TL:()=>s});var n=t(2115),i=t(6101),a=t(5155);function s(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...a}=e;if(n.isValidElement(t)){var s;let e,l,o=(s=t,(l=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(l=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),d=function(e,r){let t={...r};for(let n in r){let i=e[n],a=r[n];/^on[A-Z]/.test(n)?i&&a?t[n]=(...e)=>{let r=a(...e);return i(...e),r}:i&&(t[n]=i):"style"===n?t[n]={...i,...a}:"className"===n&&(t[n]=[i,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==n.Fragment&&(d.ref=r?(0,i.t)(r,o):o),n.cloneElement(t,d)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:i,...s}=e,l=n.Children.toArray(i),o=l.find(d);if(o){let e=o.props.children,i=l.map(r=>r!==o?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...s,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,a.jsx)(r,{...s,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}var l=s("Slot"),o=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var r=r=>e(e.s=r);e.O(0,[277,874,441,684,358],()=>r(4300)),_N_E=e.O()}]);