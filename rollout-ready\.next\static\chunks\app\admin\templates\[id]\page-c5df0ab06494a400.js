(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[88],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155);r(2115);var s=r(9708),n=r(2085),i=r(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...c}=e,o=d?s.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:n,className:t})),...c})}},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var a=r(2596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=a.$,i=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,d=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],a=null==l?void 0:l[e];if(null===t)return null;let n=s(t)||s(a);return i[e][n]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return n(e,d,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},5127:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>l,TableCell:()=>o,TableHead:()=>c,TableHeader:()=>i,TableRow:()=>d});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...r})})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}},5380:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var a=r(5155),s=r(2115),n=r(5695),i=r(6874),l=r.n(i),d=r(6695),c=r(285),o=r(6126),u=r(5127);function x(e){let{template:t}=e,r=(0,n.useRouter)(),[i,x]=(0,s.useState)(!1),[h,f]=(0,s.useState)(""),m=async()=>{if(confirm('Are you sure you want to delete the template "'.concat(t.name,'"? This action cannot be undone.'))){x(!0),f("");try{let e=await fetch("/api/templates/".concat(t.id),{method:"DELETE"});if(e.ok)r.push("/admin/templates");else{let t=await e.json();f(t.message||"Failed to delete template")}}catch(e){f("An error occurred while deleting the template")}finally{x(!1)}}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:t.name}),(0,a.jsxs)("p",{className:"text-gray-600 mt-2",children:["Template for ",t.role.name]}),t.description&&(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:t.description})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l(),{href:"/admin/templates/".concat(t.id,"/edit"),children:(0,a.jsx)(c.$,{variant:"outline",children:"Edit Template"})}),(0,a.jsx)(c.$,{variant:"destructive",onClick:m,disabled:i,children:i?"Deleting...":"Delete Template"}),(0,a.jsx)(l(),{href:"/admin/templates",children:(0,a.jsx)(c.$,{variant:"outline",children:"Back to Templates"})})]})]}),h&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:h}),(0,a.jsxs)("div",{className:"grid md:grid-cols-4 gap-6",children:[(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"text-lg",children:"Role"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)(o.E,{variant:"outline",className:"text-base px-3 py-1",children:t.role.name})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"text-lg",children:"Total Tasks"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-3xl font-bold",children:t._count.templateTasks})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"text-lg",children:"Auto-Assign"})}),(0,a.jsx)(d.Wu,{children:t.autoAssign?(0,a.jsx)(o.E,{variant:"default",className:"bg-green-100 text-green-800 border-green-200",children:"Enabled"}):(0,a.jsx)(o.E,{variant:"outline",children:"Disabled"})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"text-lg",children:"Critical Tasks"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-3xl font-bold text-red-600",children:t.templateTasks.filter(e=>e.isCritical).length})})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"Template Tasks"}),(0,a.jsx)(d.BT,{children:"Tasks that will be created when this template is applied to a project"})]}),(0,a.jsx)(d.Wu,{children:0===t.templateTasks.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"No tasks defined for this template"}),(0,a.jsx)(l(),{href:"/admin/templates/".concat(t.id,"/edit"),children:(0,a.jsx)(c.$,{children:"Add Tasks"})})]}):(0,a.jsxs)(u.Table,{children:[(0,a.jsx)(u.TableHeader,{children:(0,a.jsxs)(u.TableRow,{children:[(0,a.jsx)(u.TableHead,{children:"Task Description"}),(0,a.jsx)(u.TableHead,{children:"Timeline"}),(0,a.jsx)(u.TableHead,{children:"Due Date Offset"}),(0,a.jsx)(u.TableHead,{children:"Properties"})]})}),(0,a.jsx)(u.TableBody,{children:t.templateTasks.map(e=>(0,a.jsxs)(u.TableRow,{children:[(0,a.jsx)(u.TableCell,{className:"font-medium",children:e.description}),(0,a.jsx)(u.TableCell,{children:(0,a.jsxs)(o.E,{variant:"outline",children:[e.offsetDays>=0?"T+".concat(e.offsetDays):"T".concat(e.offsetDays)," days"]})}),(0,a.jsx)(u.TableCell,{children:0===e.offsetDays?(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:"Project Start"}):e.offsetDays>0?(0,a.jsxs)("span",{className:"text-green-600",children:[e.offsetDays," days after start"]}):(0,a.jsxs)("span",{className:"text-orange-600",children:[Math.abs(e.offsetDays)," days before start"]})}),(0,a.jsx)(u.TableCell,{children:(0,a.jsxs)("div",{className:"flex gap-1",children:[e.isCritical&&(0,a.jsx)(o.E,{variant:"destructive",className:"text-xs",children:"Critical"}),e.isRecurring&&(0,a.jsx)(o.E,{variant:"secondary",className:"text-xs",children:"Recurring"}),!e.isCritical&&!e.isRecurring&&(0,a.jsx)(o.E,{variant:"outline",className:"text-xs",children:"Standard"})]})})]},e.id))})]})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"Task Timeline"}),(0,a.jsx)(d.BT,{children:"Visual representation of when tasks are due relative to project start"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:t.templateTasks.sort((e,t)=>e.offsetDays-t.offsetDays).map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"w-20 text-right",children:(0,a.jsx)(o.E,{variant:"outline",className:"text-xs",children:e.offsetDays>=0?"T+".concat(e.offsetDays):"T".concat(e.offsetDays)})}),(0,a.jsxs)("div",{className:"flex-1 p-3 border rounded-lg bg-gray-50",children:[(0,a.jsx)("p",{className:"font-medium",children:e.description}),(0,a.jsxs)("div",{className:"flex gap-1 mt-1",children:[e.isCritical&&(0,a.jsx)(o.E,{variant:"destructive",className:"text-xs",children:"Critical"}),e.isRecurring&&(0,a.jsx)(o.E,{variant:"secondary",className:"text-xs",children:"Recurring"})]})]})]},e.id))})})]})]})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"notFound")&&r.d(t,{notFound:function(){return a.notFound}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>n});var a=r(2115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}function i(...e){return a.useCallback(n(...e),e)}},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(5155);r(2115);var s=r(9708),n=r(2085),i=r(9434);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,asChild:n=!1,...d}=e,c=n?s.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:r}),t),...d})}},6349:(e,t,r)=>{Promise.resolve().then(r.bind(r,5380))},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,TL:()=>i});var a=r(2115),s=r(6101),n=r(5155);function i(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var i;let e,l,d=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=(...e)=>{let t=n(...e);return s(...e),t}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==a.Fragment&&(c.ref=t?(0,s.t)(t,d):d),a.cloneElement(r,c)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...i}=e,l=a.Children.toArray(s),d=l.find(c);if(d){let e=d.props.children,s=l.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...i,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),d=Symbol("radix.slottable");function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,8441,1684,7358],()=>t(6349)),_N_E=e.O()}]);