(()=>{var e={};e.id=250,e.ids=[250],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,s)=>{"use strict";s.d(t,{Table:()=>n,TableBody:()=>l,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>o,TableRow:()=>i});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20975:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),o=s.n(n),l=s(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let d={children:["",{children:["admin",{children:["roles",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57614)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\[id]\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/roles/[id]/page",pathname:"/admin/roles/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},57614:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,generateMetadata:()=>m});var r=s(37413),a=s(4536),n=s.n(a),o=s(78963),l=s(23469),i=s(30084),d=s(80401),c=s(5069),p=s(39916);async function h(e){return await c.z.role.findUnique({where:{id:e},include:{templates:{include:{_count:{select:{templateTasks:!0}}},orderBy:{name:"asc"}},projectRoles:{include:{project:!0,user:!0},orderBy:{createdAt:"desc"}},_count:{select:{templates:!0,projectRoles:!0}}}})}async function m({params:e}){let t=parseInt((await e).id);if(isNaN(t))return{title:"Role Not Found - Rollout Ready"};let s=await h(t);return s?{title:`${s.name} - Rollout Ready`,description:s.description||`Role details for ${s.name}`}:{title:"Role Not Found - Rollout Ready"}}async function u({params:e}){let t=parseInt((await e).id);isNaN(t)&&(0,p.notFound)();let s=await h(t);return s||(0,p.notFound)(),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s.name}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Role details and associated templates"}),s.description&&(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:s.description})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(n(),{href:`/admin/roles/${s.id}/edit`,children:(0,r.jsx)(l.$,{variant:"outline",children:"Edit Role"})}),(0,r.jsx)(n(),{href:"/admin/roles",children:(0,r.jsx)(l.$,{variant:"outline",children:"Back to Roles"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"pb-2",children:(0,r.jsx)(o.ZB,{className:"text-sm font-medium",children:"Templates"})}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:s._count.templates}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"Associated templates"})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"pb-2",children:(0,r.jsx)(o.ZB,{className:"text-sm font-medium",children:"Project Usage"})}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:s._count.projectRoles}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"Times used in projects"})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"pb-2",children:(0,r.jsx)(o.ZB,{className:"text-sm font-medium",children:"Total Tasks"})}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:s.templates.reduce((e,t)=>e+t._count.templateTasks,0)}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"Tasks across all templates"})]})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Associated Templates"}),(0,r.jsx)(o.BT,{children:"Templates that use this role"})]}),(0,r.jsx)(o.Wu,{children:0===s.templates.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"No templates created for this role yet"}),(0,r.jsx)(n(),{href:"/admin/templates/new",children:(0,r.jsx)(l.$,{children:"Create Template"})})]}):(0,r.jsxs)(d.Table,{children:[(0,r.jsx)(d.TableHeader,{children:(0,r.jsxs)(d.TableRow,{children:[(0,r.jsx)(d.TableHead,{children:"Template Name"}),(0,r.jsx)(d.TableHead,{children:"Description"}),(0,r.jsx)(d.TableHead,{children:"Tasks"}),(0,r.jsx)(d.TableHead,{children:"Auto-Assign"}),(0,r.jsx)(d.TableHead,{children:"Actions"})]})}),(0,r.jsx)(d.TableBody,{children:s.templates.map(e=>(0,r.jsxs)(d.TableRow,{children:[(0,r.jsx)(d.TableCell,{className:"font-medium",children:e.name}),(0,r.jsx)(d.TableCell,{children:e.description||"No description"}),(0,r.jsx)(d.TableCell,{children:(0,r.jsxs)(i.E,{variant:"secondary",children:[e._count.templateTasks," task",1!==e._count.templateTasks?"s":""]})}),(0,r.jsx)(d.TableCell,{children:e.autoAssign?(0,r.jsx)(i.E,{variant:"default",children:"Auto-assign"}):(0,r.jsx)(i.E,{variant:"outline",children:"Manual"})}),(0,r.jsx)(d.TableCell,{children:(0,r.jsx)(n(),{href:`/admin/templates/${e.id}`,children:(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"View"})})})]},e.id))})]})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Recent Project Usage"}),(0,r.jsx)(o.BT,{children:"Recent projects where this role has been assigned"})]}),(0,r.jsx)(o.Wu,{children:0===s.projectRoles.length?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"This role hasn't been used in any projects yet"})}):(0,r.jsxs)(d.Table,{children:[(0,r.jsx)(d.TableHeader,{children:(0,r.jsxs)(d.TableRow,{children:[(0,r.jsx)(d.TableHead,{children:"Project"}),(0,r.jsx)(d.TableHead,{children:"Assigned To"}),(0,r.jsx)(d.TableHead,{children:"Assigned Date"}),(0,r.jsx)(d.TableHead,{children:"Actions"})]})}),(0,r.jsx)(d.TableBody,{children:s.projectRoles.slice(0,10).map(e=>(0,r.jsxs)(d.TableRow,{children:[(0,r.jsx)(d.TableCell,{className:"font-medium",children:e.project.name}),(0,r.jsxs)(d.TableCell,{children:[e.user.firstName," ",e.user.lastName," (",e.user.username,")"]}),(0,r.jsx)(d.TableCell,{children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)(d.TableCell,{children:(0,r.jsx)(n(),{href:`/admin/projects/${e.project.id}`,children:(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"View Project"})})})]},e.id))})]})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67540:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,80401))},69748:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,6211))},79551:e=>{"use strict";e.exports=require("url")},80401:(e,t,s)=>{"use strict";s.d(t,{Table:()=>a,TableBody:()=>o,TableCell:()=>d,TableHead:()=>l,TableHeader:()=>n,TableRow:()=>i});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","Table"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHeader"),o=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableBody");(0,r.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableFooter");let l=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHead"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableRow"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCell");(0,r.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCaption")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,205,277,923,811,113],()=>s(20975));module.exports=r})();