(()=>{var e={};e.id=489,e.ids=[489],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{"use strict";t.d(r,{BE:()=>a,dk:()=>l,jw:()=>c,ri:()=>p});var s=t(85663),n=t(43205),u=t.n(n),i=t(5069);t(96330);let o=process.env.JWT_SECRET||"your-secret-key-change-in-production";async function a(e,r){return s.Ay.compare(e,r)}async function c(e){var r;let t=await i.z.user.findUnique({where:{id:e},select:{id:!0,username:!0,systemRole:!0}});if(!t)throw Error("User not found");let s=(r={userId:t.id,username:t.username,systemRole:t.systemRole},u().sign(r,o,{expiresIn:"7d"}));return await i.z.session.create({data:{userId:e,token:s,expiresAt:new Date(Date.now()+6048e5)}}),s}async function l(e){try{let r=await i.z.session.findUnique({where:{token:e},include:{user:!0}});if(!r||r.expiresAt<new Date)return r&&await i.z.session.delete({where:{id:r.id}}),null;if(!function(e){try{return u().verify(e,o)}catch{return null}}(e))return null;return{id:r.user.id,username:r.user.username,email:r.user.email,firstName:r.user.firstName||void 0,lastName:r.user.lastName||void 0,systemRole:r.user.systemRole}}catch{return null}}async function p(e){await i.z.session.deleteMany({where:{token:e}})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49728:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>c});var n=t(96559),u=t(48088),i=t(37719),o=t(32190),a=t(12909);async function c(e){try{let r=e.cookies.get("auth-token")?.value;r&&await (0,a.ri)(r);let t=o.NextResponse.json({message:"Logout successful"});return t.cookies.delete("auth-token"),t}catch(e){return console.error("Logout error:",e),o.NextResponse.json({message:"Internal server error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:"app/api/auth/logout/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\auth\\logout\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:m}=l;function x(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,315],()=>t(49728));module.exports=s})();