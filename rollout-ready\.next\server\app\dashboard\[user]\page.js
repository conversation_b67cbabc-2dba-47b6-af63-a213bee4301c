(()=>{var e={};e.id=419,e.ids=[419],e.modules={84:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(7413);r(1120);var a=r(403),n=r(662),o=r(974);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}},401:(e,t,r)=>{"use strict";r.d(t,{Table:()=>a,TableBody:()=>o,TableCell:()=>d,TableHead:()=>i,TableHeader:()=>n,TableRow:()=>l});var s=r(2907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","Table"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHeader"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableBody");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableFooter");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHead"),l=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableRow"),d=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCell");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCaption")},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(5986),a=r(8974);function n(...e){return(0,a.QP)((0,s.$)(e))}},1678:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient({log:["query"]})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3469:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(7413);r(1120);var a=r(403),n=r(662),o=r(974);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let d=n?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...l})}},3795:(e,t,r)=>{Promise.resolve().then(r.bind(r,6211))},3873:e=>{"use strict";e.exports=require("path")},6211:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>i,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>o,TableRow:()=>l});var s=r(687);r(3210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},7627:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(5239),a=r(8088),n=r(8170),o=r.n(n),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["[user]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9203)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\dashboard\\[user]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\dashboard\\[user]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/[user]/page",pathname:"/dashboard/[user]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7771:(e,t,r)=>{Promise.resolve().then(r.bind(r,401))},8963:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o});var s=r(7413);r(1120);var a=r(974);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9203:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(7413),a=r(8963),n=r(84),o=r(3469),i=r(401),l=r(1678);async function d(e){return await l.z.projectTask.findMany({where:{projectRole:{userName:{equals:e,mode:"insensitive"}}},include:{project:!0,projectRole:{include:{role:!0}}},orderBy:[{dueDate:"asc"},{createdAt:"desc"}]})}async function c(e){let t=await d(e),r={total:t.length,todo:t.filter(e=>"TODO"===e.status).length,inProgress:t.filter(e=>"IN_PROGRESS"===e.status).length,done:t.filter(e=>"DONE"===e.status).length,overdue:t.filter(e=>"DONE"!==e.status&&new Date(e.dueDate)<new Date).length};return{tasks:t,stats:r}}function u(e,t){return"DONE"!==t&&new Date(e)<new Date}async function p({params:e}){let t=decodeURIComponent((await e).user),{tasks:r,stats:l}=await c(t);return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 capitalize",children:[t,"'s Dashboard"]}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Your assigned tasks across all projects"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Total Tasks"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:l.total})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"To Do"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:l.todo})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"In Progress"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:l.inProgress})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Completed"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:l.done})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Overdue"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:l.overdue})})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"Your Tasks"}),(0,s.jsx)(a.BT,{children:"All tasks assigned to you across projects"})]}),(0,s.jsx)(a.Wu,{children:0===r.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No tasks assigned yet"})}):(0,s.jsxs)(i.Table,{children:[(0,s.jsx)(i.TableHeader,{children:(0,s.jsxs)(i.TableRow,{children:[(0,s.jsx)(i.TableHead,{children:"Task"}),(0,s.jsx)(i.TableHead,{children:"Project"}),(0,s.jsx)(i.TableHead,{children:"Role"}),(0,s.jsx)(i.TableHead,{children:"Due Date"}),(0,s.jsx)(i.TableHead,{children:"Status"}),(0,s.jsx)(i.TableHead,{children:"Actions"})]})}),(0,s.jsx)(i.TableBody,{children:r.map(e=>(0,s.jsxs)(i.TableRow,{className:u(e.dueDate,e.status)?"bg-red-50":"",children:[(0,s.jsxs)(i.TableCell,{className:"font-medium",children:[e.description,u(e.dueDate,e.status)&&(0,s.jsx)(n.E,{variant:"destructive",className:"ml-2 text-xs",children:"Overdue"})]}),(0,s.jsx)(i.TableCell,{children:e.project.name}),(0,s.jsx)(i.TableCell,{children:e.projectRole.role.name}),(0,s.jsx)(i.TableCell,{children:new Date(e.dueDate).toLocaleDateString()}),(0,s.jsx)(i.TableCell,{children:function(e){switch(e){case"TODO":return(0,s.jsx)(n.E,{variant:"secondary",children:"To Do"});case"IN_PROGRESS":return(0,s.jsx)(n.E,{variant:"default",children:"In Progress"});case"DONE":return(0,s.jsx)(n.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"Done"});default:return(0,s.jsx)(n.E,{variant:"secondary",children:e})}}(e.status)}),(0,s.jsx)(i.TableCell,{children:(0,s.jsx)(o.$,{variant:"outline",size:"sm",children:"Update"})})]},e.id))})]})})]})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,982,658,923,287],()=>r(7627));module.exports=s})();