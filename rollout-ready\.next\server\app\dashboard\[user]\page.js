(()=>{var e={};e.id=419,e.ids=[419],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},6211:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>l,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>o,TableRow:()=>i});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function i({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(75986),a=r(8974);function n(...e){return(0,a.QP)((0,s.$)(e))}},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>h,gC:()=>p,l6:()=>d,yv:()=>c});var s=r(60687);r(43210);var a=r(16725),n=r(78272),o=r(13964),l=r(3589),i=r(4780);function d({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...o}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...n}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,s.jsx)(x,{}),(0,s.jsx)(a.LM,{className:(0,i.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(m,{})]})})}function h({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(o.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function m({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23358:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\components\\\\TaskUpdateButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\TaskUpdateButton.tsx","default")},24219:(e,t,r)=>{Promise.resolve().then(r.bind(r,41028)),Promise.resolve().then(r.bind(r,6211))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(37413);r(61120);var a=r(70403),n=r(50662),o=r(10974);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,asChild:r=!1,...n}){let i=r?a.DX:"span";return(0,s.jsx)(i,{"data-slot":"badge",className:(0,o.cn)(l({variant:t}),e),...n})}},33873:e=>{"use strict";e.exports=require("path")},41028:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(60687),a=r(43210),n=r(29523),o=r(15079);function l({taskId:e,currentStatus:t,onUpdate:r}){let[l,i]=(0,a.useState)(!1),[d,c]=(0,a.useState)(!1),u=async t=>{i(!0);try{let s=await fetch(`/api/tasks/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t})});if(s.ok)c(!1),r?r():window.location.reload();else{let e=await s.json();alert(`Error: ${e.message}`)}}catch{alert("An error occurred while updating the task")}finally{i(!1)}};return d?(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,s.jsxs)(o.l6,{onValueChange:u,disabled:l,children:[(0,s.jsx)(o.bq,{className:"w-32",children:(0,s.jsx)(o.yv,{placeholder:"Select status"})}),(0,s.jsxs)(o.gC,{children:[(0,s.jsx)(o.eb,{value:"TODO",children:"To Do"}),(0,s.jsx)(o.eb,{value:"IN_PROGRESS",children:"In Progress"}),(0,s.jsx)(o.eb,{value:"DONE",children:"Done"})]})]}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>c(!1),disabled:l,children:"Cancel"})]}):(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>c(!0),disabled:l,children:l?"Updating...":"Update"})}},47627:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["dashboard",{children:["[user]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69203)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\dashboard\\[user]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\dashboard\\[user]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/[user]/page",pathname:"/dashboard/[user]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},58563:(e,t,r)=>{Promise.resolve().then(r.bind(r,23358)),Promise.resolve().then(r.bind(r,80401))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69203:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(37413),a=r(78963),n=r(30084),o=r(80401),l=r(5069),i=r(23358);async function d(e){return await l.z.projectTask.findMany({where:{projectRole:{user:{username:e}}},include:{project:!0,projectRole:{include:{role:!0,user:!0}}},orderBy:[{dueDate:"asc"},{createdAt:"desc"}]})}async function c(e){let t=await d(e),r={total:t.length,todo:t.filter(e=>"TODO"===e.status).length,inProgress:t.filter(e=>"IN_PROGRESS"===e.status).length,done:t.filter(e=>"DONE"===e.status).length,overdue:t.filter(e=>"DONE"!==e.status&&new Date(e.dueDate)<new Date).length};return{tasks:t,stats:r}}function u(e,t){return"DONE"!==t&&new Date(e)<new Date}async function p({params:e}){let t=decodeURIComponent((await e).user),{tasks:r,stats:l}=await c(t);return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 capitalize",children:[t,"'s Dashboard"]}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Your assigned tasks across all projects"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Total Tasks"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:l.total})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"To Do"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:l.todo})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"In Progress"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:l.inProgress})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Completed"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:l.done})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Overdue"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:l.overdue})})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"Your Tasks"}),(0,s.jsx)(a.BT,{children:"All tasks assigned to you across projects"})]}),(0,s.jsx)(a.Wu,{children:0===r.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No tasks assigned yet"})}):(0,s.jsxs)(o.Table,{children:[(0,s.jsx)(o.TableHeader,{children:(0,s.jsxs)(o.TableRow,{children:[(0,s.jsx)(o.TableHead,{children:"Task"}),(0,s.jsx)(o.TableHead,{children:"Project"}),(0,s.jsx)(o.TableHead,{children:"Role"}),(0,s.jsx)(o.TableHead,{children:"Due Date"}),(0,s.jsx)(o.TableHead,{children:"Status"}),(0,s.jsx)(o.TableHead,{children:"Actions"})]})}),(0,s.jsx)(o.TableBody,{children:r.map(e=>(0,s.jsxs)(o.TableRow,{className:u(e.dueDate,e.status)?"bg-red-50":"",children:[(0,s.jsxs)(o.TableCell,{className:"font-medium",children:[e.description,u(e.dueDate,e.status)&&(0,s.jsx)(n.E,{variant:"destructive",className:"ml-2 text-xs",children:"Overdue"})]}),(0,s.jsx)(o.TableCell,{children:e.project.name}),(0,s.jsx)(o.TableCell,{children:e.projectRole.role.name}),(0,s.jsx)(o.TableCell,{children:new Date(e.dueDate).toLocaleDateString()}),(0,s.jsx)(o.TableCell,{children:function(e){switch(e){case"TODO":return(0,s.jsx)(n.E,{variant:"secondary",children:"To Do"});case"IN_PROGRESS":return(0,s.jsx)(n.E,{variant:"default",children:"In Progress"});case"DONE":return(0,s.jsx)(n.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"Done"});default:return(0,s.jsx)(n.E,{variant:"secondary",children:e})}}(e.status)}),(0,s.jsx)(o.TableCell,{children:(0,s.jsx)(i.default,{taskId:e.id,currentStatus:e.status})})]},e.id))})]})})]})]})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78963:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>o});var s=r(37413);r(61120);var a=r(10974);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},79551:e=>{"use strict";e.exports=require("url")},80401:(e,t,r)=>{"use strict";r.d(t,{Table:()=>a,TableBody:()=>o,TableCell:()=>d,TableHead:()=>l,TableHeader:()=>n,TableRow:()=>i});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","Table"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHeader"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableBody");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableFooter");let l=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHead"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableRow"),d=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCell");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCaption")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,205,277,923,425,811],()=>r(47627));module.exports=s})();