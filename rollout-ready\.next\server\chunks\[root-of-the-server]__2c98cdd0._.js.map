{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/projects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { name, description, startDate, roleAssignments } = body;\n\n    if (!name || name.trim() === \"\") {\n      return NextResponse.json(\n        { message: \"Project name is required\" },\n        { status: 400 }\n      );\n    }\n\n    if (!startDate) {\n      return NextResponse.json(\n        { message: \"Start date is required\" },\n        { status: 400 }\n      );\n    }\n\n    // Create the project\n    const project = await prisma.project.create({\n      data: {\n        name: name.trim(),\n        description: description?.trim() || null,\n        startDate: new Date(startDate),\n      },\n    });\n\n    // Create project roles for assigned users\n    const projectRoles = [];\n    for (const [roleIdStr, userName] of Object.entries(roleAssignments)) {\n      if (userName && userName.trim() !== \"\") {\n        const roleId = parseInt(roleIdStr);\n        const projectRole = await prisma.projectRole.create({\n          data: {\n            projectId: project.id,\n            roleId: roleId,\n            userName: userName.trim(),\n          },\n        });\n        projectRoles.push(projectRole);\n      }\n    }\n\n    // Get templates that should be auto-assigned\n    const autoAssignTemplates = await prisma.template.findMany({\n      where: {\n        autoAssign: true,\n        roleId: {\n          in: projectRoles.map(pr => pr.roleId),\n        },\n      },\n      include: {\n        templateTasks: true,\n      },\n    });\n\n    // Generate project tasks from auto-assigned templates\n    const projectTasks = [];\n    for (const template of autoAssignTemplates) {\n      const projectRole = projectRoles.find(pr => pr.roleId === template.roleId);\n      if (projectRole) {\n        for (const templateTask of template.templateTasks) {\n          const dueDate = new Date(project.startDate);\n          dueDate.setDate(dueDate.getDate() + templateTask.offsetDays);\n\n          const projectTask = await prisma.projectTask.create({\n            data: {\n              projectId: project.id,\n              templateTaskId: templateTask.id,\n              projectRoleId: projectRole.id,\n              description: templateTask.description,\n              dueDate: dueDate,\n              status: 'TODO',\n            },\n          });\n          projectTasks.push(projectTask);\n        }\n      }\n    }\n\n    return NextResponse.json({\n      project,\n      projectRoles: projectRoles.length,\n      projectTasks: projectTasks.length,\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error(\"Error creating project:\", error);\n    return NextResponse.json(\n      { message: \"Failed to create project\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG;QAE1D,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,IAAI;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAA2B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAyB,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,aAAa,aAAa,UAAU;gBACpC,WAAW,IAAI,KAAK;YACtB;QACF;QAEA,0CAA0C;QAC1C,MAAM,eAAe,EAAE;QACvB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAO,OAAO,CAAC,iBAAkB;YACnE,IAAI,YAAY,SAAS,IAAI,OAAO,IAAI;gBACtC,MAAM,SAAS,SAAS;gBACxB,MAAM,cAAc,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;oBAClD,MAAM;wBACJ,WAAW,QAAQ,EAAE;wBACrB,QAAQ;wBACR,UAAU,SAAS,IAAI;oBACzB;gBACF;gBACA,aAAa,IAAI,CAAC;YACpB;QACF;QAEA,6CAA6C;QAC7C,MAAM,sBAAsB,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACzD,OAAO;gBACL,YAAY;gBACZ,QAAQ;oBACN,IAAI,aAAa,GAAG,CAAC,CAAA,KAAM,GAAG,MAAM;gBACtC;YACF;YACA,SAAS;gBACP,eAAe;YACjB;QACF;QAEA,sDAAsD;QACtD,MAAM,eAAe,EAAE;QACvB,KAAK,MAAM,YAAY,oBAAqB;YAC1C,MAAM,cAAc,aAAa,IAAI,CAAC,CAAA,KAAM,GAAG,MAAM,KAAK,SAAS,MAAM;YACzE,IAAI,aAAa;gBACf,KAAK,MAAM,gBAAgB,SAAS,aAAa,CAAE;oBACjD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS;oBAC1C,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK,aAAa,UAAU;oBAE3D,MAAM,cAAc,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;wBAClD,MAAM;4BACJ,WAAW,QAAQ,EAAE;4BACrB,gBAAgB,aAAa,EAAE;4BAC/B,eAAe,YAAY,EAAE;4BAC7B,aAAa,aAAa,WAAW;4BACrC,SAAS;4BACT,QAAQ;wBACV;oBACF;oBACA,aAAa,IAAI,CAAC;gBACpB;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,cAAc,aAAa,MAAM;YACjC,cAAc,aAAa,MAAM;QACnC,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAA2B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}