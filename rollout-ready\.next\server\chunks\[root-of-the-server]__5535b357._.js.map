{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/templates/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\n\nexport async function GET() {\n  try {\n    const templates = await prisma.template.findMany({\n      include: {\n        role: true,\n        templateTasks: true,\n        _count: {\n          select: {\n            templateTasks: true,\n          },\n        },\n      },\n      orderBy: [\n        { role: { name: 'asc' } },\n        { name: 'asc' },\n      ],\n    });\n\n    return NextResponse.json(templates);\n  } catch (error) {\n    console.error(\"Error fetching templates:\", error);\n    return NextResponse.json(\n      { message: \"Failed to fetch templates\" },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { name, description, roleId, autoAssign, tasks } = body;\n\n    if (!name || name.trim() === \"\") {\n      return NextResponse.json(\n        { message: \"Template name is required\" },\n        { status: 400 }\n      );\n    }\n\n    if (!roleId) {\n      return NextResponse.json(\n        { message: \"Role is required\" },\n        { status: 400 }\n      );\n    }\n\n    if (!tasks || tasks.length === 0) {\n      return NextResponse.json(\n        { message: \"At least one task is required\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if role exists\n    const role = await prisma.role.findUnique({\n      where: { id: roleId },\n    });\n\n    if (!role) {\n      return NextResponse.json(\n        { message: \"Role not found\" },\n        { status: 400 }\n      );\n    }\n\n    // Create the template\n    const template = await prisma.template.create({\n      data: {\n        name: name.trim(),\n        description: description?.trim() || null,\n        roleId: roleId,\n        autoAssign: autoAssign || false,\n      },\n    });\n\n    // Create template tasks\n    const templateTasks = await Promise.all(\n      tasks.map((task: { description: string; offsetDays: number; isRecurring: boolean; isCritical: boolean }) =>\n        prisma.templateTask.create({\n          data: {\n            templateId: template.id,\n            description: task.description.trim(),\n            offsetDays: task.offsetDays || 0,\n            isRecurring: task.isRecurring || false,\n            isCritical: task.isCritical || false,\n          },\n        })\n      )\n    );\n\n    return NextResponse.json({\n      template,\n      templateTasks: templateTasks.length,\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error(\"Error creating template:\", error);\n    return NextResponse.json(\n      { message: \"Failed to create template\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,YAAY,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C,SAAS;gBACP,MAAM;gBACN,eAAe;gBACf,QAAQ;oBACN,QAAQ;wBACN,eAAe;oBACjB;gBACF;YACF;YACA,SAAS;gBACP;oBAAE,MAAM;wBAAE,MAAM;oBAAM;gBAAE;gBACxB;oBAAE,MAAM;gBAAM;aACf;QACH;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAA4B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG;QAEzD,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,IAAI;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAA4B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAmB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAgC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAiB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,aAAa,aAAa,UAAU;gBACpC,QAAQ;gBACR,YAAY,cAAc;YAC5B;QACF;QAEA,wBAAwB;QACxB,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,MAAM,GAAG,CAAC,CAAC,OACT,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBACzB,MAAM;oBACJ,YAAY,SAAS,EAAE;oBACvB,aAAa,KAAK,WAAW,CAAC,IAAI;oBAClC,YAAY,KAAK,UAAU,IAAI;oBAC/B,aAAa,KAAK,WAAW,IAAI;oBACjC,YAAY,KAAK,UAAU,IAAI;gBACjC;YACF;QAIJ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,eAAe,cAAc,MAAM;QACrC,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAA4B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}