{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/projects/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\n\n// GET /api/projects/[id] - Get project by ID\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const resolvedParams = await params;\n    const projectId = parseInt(resolvedParams.id);\n    \n    if (isNaN(projectId)) {\n      return NextResponse.json(\n        { message: \"Invalid project ID\" },\n        { status: 400 }\n      );\n    }\n\n    const project = await prisma.project.findUnique({\n      where: { id: projectId },\n      include: {\n        projectRoles: {\n          include: {\n            role: true,\n            user: true,\n          },\n        },\n        _count: {\n          select: {\n            projectTasks: true,\n          },\n        },\n      },\n    });\n\n    if (!project) {\n      return NextResponse.json(\n        { message: \"Project not found\" },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json(project);\n  } catch (error) {\n    console.error(\"Error fetching project:\", error);\n    return NextResponse.json(\n      { message: \"Failed to fetch project\" },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/projects/[id] - Update project\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const resolvedParams = await params;\n    const projectId = parseInt(resolvedParams.id);\n    \n    if (isNaN(projectId)) {\n      return NextResponse.json(\n        { message: \"Invalid project ID\" },\n        { status: 400 }\n      );\n    }\n\n    const body = await request.json();\n    const { name, description, startDate, roleAssignments } = body;\n\n    // Check if project exists\n    const existingProject = await prisma.project.findUnique({\n      where: { id: projectId },\n      include: {\n        projectRoles: true,\n      },\n    });\n\n    if (!existingProject) {\n      return NextResponse.json(\n        { message: \"Project not found\" },\n        { status: 404 }\n      );\n    }\n\n    // Update project details\n    const updatedProject = await prisma.project.update({\n      where: { id: projectId },\n      data: {\n        name,\n        description,\n        startDate: new Date(startDate),\n      },\n    });\n\n    // Handle role assignments if provided\n    if (roleAssignments) {\n      // Get current role assignments\n      const currentRoles = existingProject.projectRoles;\n      \n      // Process role assignments\n      for (const [roleIdStr, userId] of Object.entries(roleAssignments)) {\n        const roleId = parseInt(roleIdStr);\n        \n        // Find existing assignment for this role\n        const existingAssignment = currentRoles.find(pr => pr.roleId === roleId);\n        \n        if (userId && typeof userId === 'number') {\n          // Verify the user exists\n          const user = await prisma.user.findUnique({\n            where: { id: userId },\n          });\n          \n          if (!user) {\n            return NextResponse.json(\n              { message: `User with ID ${userId} not found` },\n              { status: 400 }\n            );\n          }\n          \n          if (existingAssignment) {\n            // Update existing assignment\n            await prisma.projectRole.update({\n              where: { id: existingAssignment.id },\n              data: { userId },\n            });\n          } else {\n            // Create new assignment\n            await prisma.projectRole.create({\n              data: {\n                projectId,\n                roleId,\n                userId,\n              },\n            });\n          }\n        } else if (existingAssignment) {\n          // Remove assignment if userId is undefined/null\n          await prisma.projectRole.delete({\n            where: { id: existingAssignment.id },\n          });\n        }\n      }\n    }\n\n    // Get updated project with relations\n    const finalProject = await prisma.project.findUnique({\n      where: { id: projectId },\n      include: {\n        projectRoles: {\n          include: {\n            role: true,\n            user: true,\n          },\n        },\n        _count: {\n          select: {\n            projectTasks: true,\n          },\n        },\n      },\n    });\n\n    return NextResponse.json(finalProject);\n  } catch (error) {\n    console.error(\"Error updating project:\", error);\n    return NextResponse.json(\n      { message: \"Failed to update project\" },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE /api/projects/[id] - Delete project\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const resolvedParams = await params;\n    const projectId = parseInt(resolvedParams.id);\n    \n    if (isNaN(projectId)) {\n      return NextResponse.json(\n        { message: \"Invalid project ID\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if project exists\n    const existingProject = await prisma.project.findUnique({\n      where: { id: projectId },\n    });\n\n    if (!existingProject) {\n      return NextResponse.json(\n        { message: \"Project not found\" },\n        { status: 404 }\n      );\n    }\n\n    // Delete project (cascade will handle related records)\n    await prisma.project.delete({\n      where: { id: projectId },\n    });\n\n    return NextResponse.json({ message: \"Project deleted successfully\" });\n  } catch (error) {\n    console.error(\"Error deleting project:\", error);\n    return NextResponse.json(\n      { message: \"Failed to delete project\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,YAAY,SAAS,eAAe,EAAE;QAE5C,IAAI,MAAM,YAAY;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAqB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,cAAc;oBACZ,SAAS;wBACP,MAAM;wBACN,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,cAAc;oBAChB;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAoB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAA0B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,YAAY,SAAS,eAAe,EAAE;QAE5C,IAAI,MAAM,YAAY;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAqB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG;QAE1D,0BAA0B;QAC1B,MAAM,kBAAkB,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAoB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,iBAAiB,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACjD,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ;gBACA;gBACA,WAAW,IAAI,KAAK;YACtB;QACF;QAEA,sCAAsC;QACtC,IAAI,iBAAiB;YACnB,+BAA+B;YAC/B,MAAM,eAAe,gBAAgB,YAAY;YAEjD,2BAA2B;YAC3B,KAAK,MAAM,CAAC,WAAW,OAAO,IAAI,OAAO,OAAO,CAAC,iBAAkB;gBACjE,MAAM,SAAS,SAAS;gBAExB,yCAAyC;gBACzC,MAAM,qBAAqB,aAAa,IAAI,CAAC,CAAA,KAAM,GAAG,MAAM,KAAK;gBAEjE,IAAI,UAAU,OAAO,WAAW,UAAU;oBACxC,yBAAyB;oBACzB,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BAAE,IAAI;wBAAO;oBACtB;oBAEA,IAAI,CAAC,MAAM;wBACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;4BAAE,SAAS,CAAC,aAAa,EAAE,OAAO,UAAU,CAAC;wBAAC,GAC9C;4BAAE,QAAQ;wBAAI;oBAElB;oBAEA,IAAI,oBAAoB;wBACtB,6BAA6B;wBAC7B,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;4BAC9B,OAAO;gCAAE,IAAI,mBAAmB,EAAE;4BAAC;4BACnC,MAAM;gCAAE;4BAAO;wBACjB;oBACF,OAAO;wBACL,wBAAwB;wBACxB,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;4BAC9B,MAAM;gCACJ;gCACA;gCACA;4BACF;wBACF;oBACF;gBACF,OAAO,IAAI,oBAAoB;oBAC7B,gDAAgD;oBAChD,MAAM,kHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;wBAC9B,OAAO;4BAAE,IAAI,mBAAmB,EAAE;wBAAC;oBACrC;gBACF;YACF;QACF;QAEA,qCAAqC;QACrC,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,cAAc;oBACZ,SAAS;wBACP,MAAM;wBACN,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,cAAc;oBAChB;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAA2B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,YAAY,SAAS,eAAe,EAAE;QAE5C,IAAI,MAAM,YAAY;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAqB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,MAAM,kBAAkB,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE,IAAI;YAAU;QACzB;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAoB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,uDAAuD;QACvD,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,OAAO;gBAAE,IAAI;YAAU;QACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAA+B;IACrE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAA2B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}