(()=>{var e={};e.id=861,e.ids=[861],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3403:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>l});var o=t(6559),a=t(8088),n=t(7719),i=t(2190),u=t(5069);async function p(){try{let e=await u.z.role.findMany({include:{templates:!0,_count:{select:{templates:!0,projectRoles:!0}}},orderBy:{name:"asc"}});return i.NextResponse.json(e)}catch(e){return console.error("Error fetching roles:",e),i.NextResponse.json({message:"Failed to fetch roles"},{status:500})}}async function l(e){try{let{name:r,description:t}=await e.json();if(!r||""===r.trim())return i.NextResponse.json({message:"Role name is required"},{status:400});if(await u.z.role.findUnique({where:{name:r.trim()}}))return i.NextResponse.json({message:"A role with this name already exists"},{status:400});let s=await u.z.role.create({data:{name:r.trim(),description:t?.trim()||null}});return i.NextResponse.json(s,{status:201})}catch(e){return console.error("Error creating role:",e),i.NextResponse.json({message:"Failed to create role"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/roles/route",pathname:"/api/roles",filename:"route",bundlePath:"app/api/roles/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\roles\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:x}=c;function g(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(6330);let o=globalThis.prisma??new s.PrismaClient({log:["query"]})},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(3403));module.exports=s})();