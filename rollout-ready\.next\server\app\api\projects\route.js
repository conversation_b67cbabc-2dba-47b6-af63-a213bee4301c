(()=>{var e={};e.id=64,e.ids=[64],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(6330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9670:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{POST:()=>u});var a=r(6559),o=r(8088),i=r(7719),n=r(2190),p=r(5069);async function u(e){try{let{name:t,description:r,startDate:s,roleAssignments:a}=await e.json();if(!t||""===t.trim())return n.NextResponse.json({message:"Project name is required"},{status:400});if(!s)return n.NextResponse.json({message:"Start date is required"},{status:400});let o=await p.z.project.create({data:{name:t.trim(),description:r?.trim()||null,startDate:new Date(s)}}),i=[];for(let[e,t]of Object.entries(a))if(t&&""!==t.trim()){let r=parseInt(e),s=await p.z.projectRole.create({data:{projectId:o.id,roleId:r,userName:t.trim()}});i.push(s)}let u=await p.z.template.findMany({where:{autoAssign:!0,roleId:{in:i.map(e=>e.roleId)}},include:{templateTasks:!0}}),d=[];for(let e of u){let t=i.find(t=>t.roleId===e.roleId);if(t)for(let r of e.templateTasks){let e=new Date(o.startDate);e.setDate(e.getDate()+r.offsetDays);let s=await p.z.projectTask.create({data:{projectId:o.id,templateTaskId:r.id,projectRoleId:t.id,description:r.description,dueDate:e,status:"TODO"}});d.push(s)}}return n.NextResponse.json({project:o,projectRoles:i.length,projectTasks:d.length},{status:201})}catch(e){return console.error("Error creating project:",e),n.NextResponse.json({message:"Failed to create project"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\projects\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:m}=d;function j(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(9670));module.exports=s})();