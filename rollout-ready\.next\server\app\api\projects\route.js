(()=>{var e={};e.id=64,e.ids=[64],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79670:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>j});var s={};r.r(s),r.d(s,{POST:()=>d});var a=r(96559),o=r(48088),i=r(37719),n=r(32190),p=r(5069);async function u(e,t,r){for(let s of(await p.z.template.findMany({where:{autoAssign:!0,roleId:{in:t.map(e=>e.roleId)}},include:{templateTasks:!0}}))){let a=t.find(e=>e.roleId===s.roleId);if(a)for(let t of s.templateTasks){let s=new Date(r);s.setDate(s.getDate()+t.offsetDays),await p.z.projectTask.create({data:{projectId:e,templateTaskId:t.id,projectRoleId:a.id,description:t.description,dueDate:s,status:"TODO"}})}}}async function d(e){try{let{name:t,description:r,startDate:s,roleAssignments:a}=await e.json();if(!t||""===t.trim())return n.NextResponse.json({message:"Project name is required"},{status:400});if(!s)return n.NextResponse.json({message:"Start date is required"},{status:400});let o=await p.z.project.create({data:{name:t.trim(),description:r?.trim()||null,startDate:new Date(s)}}),i=[];for(let[e,t]of Object.entries(a))if(t&&"number"==typeof t){let r=parseInt(e);if(!await p.z.user.findUnique({where:{id:t}}))return n.NextResponse.json({message:`User with ID ${t} not found`},{status:400});let s=await p.z.projectRole.create({data:{projectId:o.id,roleId:r,userId:t}});i.push(s)}await u(o.id,i,o.startDate);let d=await p.z.projectTask.count({where:{projectId:o.id}});return n.NextResponse.json({project:o,projectRoles:i.length,projectTasks:d},{status:201})}catch(e){return console.error("Error creating project:",e),n.NextResponse.json({message:"Failed to create project"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\projects\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:j,serverHooks:m}=c;function f(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:j})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(79670));module.exports=s})();