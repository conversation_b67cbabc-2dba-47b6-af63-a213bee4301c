(()=>{var e={};e.id=88,e.ids=[88],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,s)=>{"use strict";s.d(t,{Table:()=>n,TableBody:()=>o,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>l,TableRow:()=>i});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function l({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},7055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),o=s(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(t,i);let d={children:["",{children:["admin",{children:["templates",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,59736)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\[id]\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/templates/[id]/page",pathname:"/admin/templates/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},59736:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(37413),a=s(4536),n=s.n(a),l=s(78963),o=s(23469),i=s(30084),d=s(80401),c=s(5069),p=s(39916);async function h(e){return await c.z.template.findUnique({where:{id:e},include:{role:!0,templateTasks:{orderBy:{offsetDays:"asc"}},_count:{select:{templateTasks:!0}}}})}async function m({params:e}){let t=parseInt((await e).id);isNaN(t)&&(0,p.notFound)();let s=await h(t);return s||(0,p.notFound)(),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s.name}),(0,r.jsxs)("p",{className:"text-gray-600 mt-2",children:["Template for ",s.role.name]}),s.description&&(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:s.description})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(n(),{href:`/admin/templates/${s.id}/edit`,children:(0,r.jsx)(o.$,{variant:"outline",children:"Edit Template"})}),(0,r.jsx)(n(),{href:"/admin/templates",children:(0,r.jsx)(o.$,{variant:"outline",children:"Back to Templates"})})]})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-4 gap-6",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{className:"text-lg",children:"Role"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)(i.E,{variant:"outline",className:"text-base px-3 py-1",children:s.role.name})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{className:"text-lg",children:"Total Tasks"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"text-3xl font-bold",children:s._count.templateTasks})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{className:"text-lg",children:"Auto-Assign"})}),(0,r.jsx)(l.Wu,{children:s.autoAssign?(0,r.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800 border-green-200",children:"Enabled"}):(0,r.jsx)(i.E,{variant:"outline",children:"Disabled"})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{className:"text-lg",children:"Critical Tasks"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"text-3xl font-bold text-red-600",children:s.templateTasks.filter(e=>e.isCritical).length})})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Template Tasks"}),(0,r.jsx)(l.BT,{children:"Tasks that will be created when this template is applied to a project"})]}),(0,r.jsx)(l.Wu,{children:0===s.templateTasks.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"No tasks defined for this template"}),(0,r.jsx)(n(),{href:`/admin/templates/${s.id}/edit`,children:(0,r.jsx)(o.$,{children:"Add Tasks"})})]}):(0,r.jsxs)(d.Table,{children:[(0,r.jsx)(d.TableHeader,{children:(0,r.jsxs)(d.TableRow,{children:[(0,r.jsx)(d.TableHead,{children:"Task Description"}),(0,r.jsx)(d.TableHead,{children:"Timeline"}),(0,r.jsx)(d.TableHead,{children:"Due Date Offset"}),(0,r.jsx)(d.TableHead,{children:"Properties"})]})}),(0,r.jsx)(d.TableBody,{children:s.templateTasks.map(e=>(0,r.jsxs)(d.TableRow,{children:[(0,r.jsx)(d.TableCell,{className:"font-medium",children:e.description}),(0,r.jsx)(d.TableCell,{children:(0,r.jsxs)(i.E,{variant:"outline",children:[e.offsetDays>=0?`T+${e.offsetDays}`:`T${e.offsetDays}`," days"]})}),(0,r.jsx)(d.TableCell,{children:0===e.offsetDays?(0,r.jsx)("span",{className:"text-blue-600 font-medium",children:"Project Start"}):e.offsetDays>0?(0,r.jsxs)("span",{className:"text-green-600",children:[e.offsetDays," days after start"]}):(0,r.jsxs)("span",{className:"text-orange-600",children:[Math.abs(e.offsetDays)," days before start"]})}),(0,r.jsx)(d.TableCell,{children:(0,r.jsxs)("div",{className:"flex gap-1",children:[e.isCritical&&(0,r.jsx)(i.E,{variant:"destructive",className:"text-xs",children:"Critical"}),e.isRecurring&&(0,r.jsx)(i.E,{variant:"secondary",className:"text-xs",children:"Recurring"}),!e.isCritical&&!e.isRecurring&&(0,r.jsx)(i.E,{variant:"outline",className:"text-xs",children:"Standard"})]})})]},e.id))})]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Task Timeline"}),(0,r.jsx)(l.BT,{children:"Visual representation of when tasks are due relative to project start"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:s.templateTasks.sort((e,t)=>e.offsetDays-t.offsetDays).map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"w-20 text-right",children:(0,r.jsx)(i.E,{variant:"outline",className:"text-xs",children:e.offsetDays>=0?`T+${e.offsetDays}`:`T${e.offsetDays}`})}),(0,r.jsxs)("div",{className:"flex-1 p-3 border rounded-lg bg-gray-50",children:[(0,r.jsx)("p",{className:"font-medium",children:e.description}),(0,r.jsxs)("div",{className:"flex gap-1 mt-1",children:[e.isCritical&&(0,r.jsx)(i.E,{variant:"destructive",className:"text-xs",children:"Critical"}),e.isRecurring&&(0,r.jsx)(i.E,{variant:"secondary",className:"text-xs",children:"Recurring"})]})]})]},e.id))})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67540:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,80401))},69748:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,6211))},79551:e=>{"use strict";e.exports=require("url")},80401:(e,t,s)=>{"use strict";s.d(t,{Table:()=>a,TableBody:()=>l,TableCell:()=>d,TableHead:()=>o,TableHeader:()=>n,TableRow:()=>i});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","Table"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHeader"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableBody");(0,r.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableFooter");let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHead"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableRow"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCell");(0,r.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCaption")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,205,277,923,811,113],()=>s(7055));module.exports=r})();