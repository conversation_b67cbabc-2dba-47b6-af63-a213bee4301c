(()=>{var e={};e.id=88,e.ids=[88],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>i,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>o,TableRow:()=>l});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},7055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["templates",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59736)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\[id]\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/templates/[id]/page",pathname:"/admin/templates/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(37413);r(61120);var a=r(70403),n=r(50662),o=r(10974);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}},33873:e=>{"use strict";e.exports=require("path")},59736:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(37413),a=r(4536),n=r.n(a),o=r(78963),i=r(23469),l=r(30084),d=r(80401),c=r(5069),p=r(39916);async function h(e){return await c.z.template.findUnique({where:{id:e},include:{role:!0,templateTasks:{orderBy:{offsetDays:"asc"}},_count:{select:{templateTasks:!0}}}})}async function x({params:e}){let t=parseInt((await e).id);isNaN(t)&&(0,p.notFound)();let r=await h(t);return r||(0,p.notFound)(),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:r.name}),(0,s.jsxs)("p",{className:"text-gray-600 mt-2",children:["Template for ",r.role.name]}),r.description&&(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:r.description})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n(),{href:`/admin/templates/${r.id}/edit`,children:(0,s.jsx)(i.$,{variant:"outline",children:"Edit Template"})}),(0,s.jsx)(n(),{href:"/admin/templates",children:(0,s.jsx)(i.$,{variant:"outline",children:"Back to Templates"})})]})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-4 gap-6",children:[(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{className:"text-lg",children:"Role"})}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)(l.E,{variant:"outline",className:"text-base px-3 py-1",children:r.role.name})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{className:"text-lg",children:"Total Tasks"})}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold",children:r._count.templateTasks})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{className:"text-lg",children:"Auto-Assign"})}),(0,s.jsx)(o.Wu,{children:r.autoAssign?(0,s.jsx)(l.E,{variant:"default",className:"bg-green-100 text-green-800 border-green-200",children:"Enabled"}):(0,s.jsx)(l.E,{variant:"outline",children:"Disabled"})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{className:"text-lg",children:"Critical Tasks"})}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)("div",{className:"text-3xl font-bold text-red-600",children:r.templateTasks.filter(e=>e.isCritical).length})})]})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"Template Tasks"}),(0,s.jsx)(o.BT,{children:"Tasks that will be created when this template is applied to a project"})]}),(0,s.jsx)(o.Wu,{children:0===r.templateTasks.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"No tasks defined for this template"}),(0,s.jsx)(n(),{href:`/admin/templates/${r.id}/edit`,children:(0,s.jsx)(i.$,{children:"Add Tasks"})})]}):(0,s.jsxs)(d.Table,{children:[(0,s.jsx)(d.TableHeader,{children:(0,s.jsxs)(d.TableRow,{children:[(0,s.jsx)(d.TableHead,{children:"Task Description"}),(0,s.jsx)(d.TableHead,{children:"Timeline"}),(0,s.jsx)(d.TableHead,{children:"Due Date Offset"}),(0,s.jsx)(d.TableHead,{children:"Properties"})]})}),(0,s.jsx)(d.TableBody,{children:r.templateTasks.map(e=>(0,s.jsxs)(d.TableRow,{children:[(0,s.jsx)(d.TableCell,{className:"font-medium",children:e.description}),(0,s.jsx)(d.TableCell,{children:(0,s.jsxs)(l.E,{variant:"outline",children:[e.offsetDays>=0?`T+${e.offsetDays}`:`T${e.offsetDays}`," days"]})}),(0,s.jsx)(d.TableCell,{children:0===e.offsetDays?(0,s.jsx)("span",{className:"text-blue-600 font-medium",children:"Project Start"}):e.offsetDays>0?(0,s.jsxs)("span",{className:"text-green-600",children:[e.offsetDays," days after start"]}):(0,s.jsxs)("span",{className:"text-orange-600",children:[Math.abs(e.offsetDays)," days before start"]})}),(0,s.jsx)(d.TableCell,{children:(0,s.jsxs)("div",{className:"flex gap-1",children:[e.isCritical&&(0,s.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"Critical"}),e.isRecurring&&(0,s.jsx)(l.E,{variant:"secondary",className:"text-xs",children:"Recurring"}),!e.isCritical&&!e.isRecurring&&(0,s.jsx)(l.E,{variant:"outline",className:"text-xs",children:"Standard"})]})})]},e.id))})]})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"Task Timeline"}),(0,s.jsx)(o.BT,{children:"Visual representation of when tasks are due relative to project start"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:r.templateTasks.sort((e,t)=>e.offsetDays-t.offsetDays).map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-20 text-right",children:(0,s.jsx)(l.E,{variant:"outline",className:"text-xs",children:e.offsetDays>=0?`T+${e.offsetDays}`:`T${e.offsetDays}`})}),(0,s.jsxs)("div",{className:"flex-1 p-3 border rounded-lg bg-gray-50",children:[(0,s.jsx)("p",{className:"font-medium",children:e.description}),(0,s.jsxs)("div",{className:"flex gap-1 mt-1",children:[e.isCritical&&(0,s.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"Critical"}),e.isRecurring&&(0,s.jsx)(l.E,{variant:"secondary",className:"text-xs",children:"Recurring"})]})]})]},e.id))})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67540:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,80401))},69748:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,6211))},79551:e=>{"use strict";e.exports=require("url")},80401:(e,t,r)=>{"use strict";r.d(t,{Table:()=>a,TableBody:()=>o,TableCell:()=>d,TableHead:()=>i,TableHeader:()=>n,TableRow:()=>l});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","Table"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHeader"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableBody");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableFooter");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHead"),l=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableRow"),d=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCell");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCaption")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,205,277,923,811,624],()=>r(7055));module.exports=s})();