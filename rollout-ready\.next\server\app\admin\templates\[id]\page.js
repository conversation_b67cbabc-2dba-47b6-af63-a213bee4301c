"use strict";(()=>{var e={};e.id=88,e.ids=[88],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},6330:e=>{e.exports=require("@prisma/client")},7055:(e,s,a)=>{a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c});var t=a(5239),r=a(8088),i=a(8170),l=a.n(i),n=a(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c={children:["",{children:["admin",{children:["templates",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,9736)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\[id]\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/templates/[id]/page",pathname:"/admin/templates/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{e.exports=require("url")},9736:(e,s,a)=>{a.r(s),a.d(s,{default:()=>h});var t=a(7413),r=a(4536),i=a.n(r),l=a(8963),n=a(3469),d=a(84),c=a(401),o=a(5069),x=a(9916);async function p(e){return await o.z.template.findUnique({where:{id:e},include:{role:!0,templateTasks:{orderBy:{offsetDays:"asc"}},_count:{select:{templateTasks:!0}}}})}async function h({params:e}){let s=parseInt((await e).id);isNaN(s)&&(0,x.notFound)();let a=await p(s);return a||(0,x.notFound)(),(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:a.name}),(0,t.jsxs)("p",{className:"text-gray-600 mt-2",children:["Template for ",a.role.name]}),a.description&&(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:a.description})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i(),{href:`/admin/templates/${a.id}/edit`,children:(0,t.jsx)(n.$,{variant:"outline",children:"Edit Template"})}),(0,t.jsx)(i(),{href:"/admin/templates",children:(0,t.jsx)(n.$,{variant:"outline",children:"Back to Templates"})})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-4 gap-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{className:"text-lg",children:"Role"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)(d.E,{variant:"outline",className:"text-base px-3 py-1",children:a.role.name})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{className:"text-lg",children:"Total Tasks"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"text-3xl font-bold",children:a._count.templateTasks})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{className:"text-lg",children:"Auto-Assign"})}),(0,t.jsx)(l.Wu,{children:a.autoAssign?(0,t.jsx)(d.E,{variant:"default",className:"bg-green-100 text-green-800 border-green-200",children:"Enabled"}):(0,t.jsx)(d.E,{variant:"outline",children:"Disabled"})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{className:"text-lg",children:"Critical Tasks"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"text-3xl font-bold text-red-600",children:a.templateTasks.filter(e=>e.isCritical).length})})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Template Tasks"}),(0,t.jsx)(l.BT,{children:"Tasks that will be created when this template is applied to a project"})]}),(0,t.jsx)(l.Wu,{children:0===a.templateTasks.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"No tasks defined for this template"}),(0,t.jsx)(i(),{href:`/admin/templates/${a.id}/edit`,children:(0,t.jsx)(n.$,{children:"Add Tasks"})})]}):(0,t.jsxs)(c.Table,{children:[(0,t.jsx)(c.TableHeader,{children:(0,t.jsxs)(c.TableRow,{children:[(0,t.jsx)(c.TableHead,{children:"Task Description"}),(0,t.jsx)(c.TableHead,{children:"Timeline"}),(0,t.jsx)(c.TableHead,{children:"Due Date Offset"}),(0,t.jsx)(c.TableHead,{children:"Properties"})]})}),(0,t.jsx)(c.TableBody,{children:a.templateTasks.map(e=>(0,t.jsxs)(c.TableRow,{children:[(0,t.jsx)(c.TableCell,{className:"font-medium",children:e.description}),(0,t.jsx)(c.TableCell,{children:(0,t.jsxs)(d.E,{variant:"outline",children:[e.offsetDays>=0?`T+${e.offsetDays}`:`T${e.offsetDays}`," days"]})}),(0,t.jsx)(c.TableCell,{children:0===e.offsetDays?(0,t.jsx)("span",{className:"text-blue-600 font-medium",children:"Project Start"}):e.offsetDays>0?(0,t.jsxs)("span",{className:"text-green-600",children:[e.offsetDays," days after start"]}):(0,t.jsxs)("span",{className:"text-orange-600",children:[Math.abs(e.offsetDays)," days before start"]})}),(0,t.jsx)(c.TableCell,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[e.isCritical&&(0,t.jsx)(d.E,{variant:"destructive",className:"text-xs",children:"Critical"}),e.isRecurring&&(0,t.jsx)(d.E,{variant:"secondary",className:"text-xs",children:"Recurring"}),!e.isCritical&&!e.isRecurring&&(0,t.jsx)(d.E,{variant:"outline",className:"text-xs",children:"Standard"})]})})]},e.id))})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Task Timeline"}),(0,t.jsx)(l.BT,{children:"Visual representation of when tasks are due relative to project start"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:a.templateTasks.sort((e,s)=>e.offsetDays-s.offsetDays).map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-20 text-right",children:(0,t.jsx)(d.E,{variant:"outline",className:"text-xs",children:e.offsetDays>=0?`T+${e.offsetDays}`:`T${e.offsetDays}`})}),(0,t.jsxs)("div",{className:"flex-1 p-3 border rounded-lg bg-gray-50",children:[(0,t.jsx)("p",{className:"font-medium",children:e.description}),(0,t.jsxs)("div",{className:"flex gap-1 mt-1",children:[e.isCritical&&(0,t.jsx)(d.E,{variant:"destructive",className:"text-xs",children:"Critical"}),e.isRecurring&&(0,t.jsx)(d.E,{variant:"secondary",className:"text-xs",children:"Recurring"})]})]})]},e.id))})})]})]})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,982,277,923,287,174],()=>a(7055));module.exports=t})();