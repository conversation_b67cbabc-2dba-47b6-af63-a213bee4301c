{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/api/roles/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/db\";\n\nexport async function GET() {\n  try {\n    const roles = await prisma.role.findMany({\n      include: {\n        templates: true,\n        _count: {\n          select: {\n            templates: true,\n            projectRoles: true,\n          },\n        },\n      },\n      orderBy: { name: 'asc' },\n    });\n\n    return NextResponse.json(roles);\n  } catch (error) {\n    console.error(\"Error fetching roles:\", error);\n    return NextResponse.json(\n      { message: \"Failed to fetch roles\" },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { name, description } = body;\n\n    if (!name || name.trim() === \"\") {\n      return NextResponse.json(\n        { message: \"Role name is required\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if role with this name already exists\n    const existingRole = await prisma.role.findUnique({\n      where: { name: name.trim() },\n    });\n\n    if (existingRole) {\n      return NextResponse.json(\n        { message: \"A role with this name already exists\" },\n        { status: 400 }\n      );\n    }\n\n    const role = await prisma.role.create({\n      data: {\n        name: name.trim(),\n        description: description?.trim() || null,\n      },\n    });\n\n    return NextResponse.json(role, { status: 201 });\n  } catch (error) {\n    console.error(\"Error creating role:\", error);\n    return NextResponse.json(\n      { message: \"Failed to create role\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,SAAS;gBACP,WAAW;gBACX,QAAQ;oBACN,QAAQ;wBACN,WAAW;wBACX,cAAc;oBAChB;gBACF;YACF;YACA,SAAS;gBAAE,MAAM;YAAM;QACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG;QAE9B,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,IAAI;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAwB,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,8CAA8C;QAC9C,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,MAAM,KAAK,IAAI;YAAG;QAC7B;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAuC,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,aAAa,aAAa,UAAU;YACtC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}