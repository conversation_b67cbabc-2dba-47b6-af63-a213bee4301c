[{"name": "hot-reloader", "duration": 196, "timestamp": 540046996002, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1750172726584, "traceId": "1f895d88ec9c0cba"}, {"name": "setup-dev-bundler", "duration": 724667, "timestamp": 540046718154, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750172726306, "traceId": "1f895d88ec9c0cba"}, {"name": "run-instrumentation-hook", "duration": 33, "timestamp": 540047532022, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750172727120, "traceId": "1f895d88ec9c0cba"}, {"name": "start-dev-server", "duration": 1679089, "timestamp": 540045870939, "id": 1, "tags": {"cpus": "12", "platform": "win32", "memory.freeMem": "2444132352", "memory.totalMem": "16929980416", "memory.heapSizeLimit": "8514437120", "memory.rss": "177717248", "memory.heapTotal": "98484224", "memory.heapUsed": "74894216"}, "startTime": 1750172725459, "traceId": "1f895d88ec9c0cba"}, {"name": "compile-path", "duration": 2981625, "timestamp": 540076233688, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750172755822, "traceId": "1f895d88ec9c0cba"}, {"name": "ensure-page", "duration": 2983155, "timestamp": 540076232927, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750172755821, "traceId": "1f895d88ec9c0cba"}]