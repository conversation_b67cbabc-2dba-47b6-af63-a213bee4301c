"use strict";(()=>{var e={};e.id=477,e.ids=[477],e.modules={771:(e,s,r)=>{r.r(s),r.d(s,{default:()=>j});var t=r(7413),a=r(4536),n=r.n(a),l=r(8963),i=r(3469),d=r(84),o=r(401),c=r(5069),x=r(9916);async function h(e){return await c.z.project.findUnique({where:{id:e},include:{projectRoles:{include:{role:!0}},projectTasks:{include:{projectRole:{include:{role:!0}},templateTask:!0},orderBy:[{dueDate:"asc"},{createdAt:"desc"}]},_count:{select:{projectTasks:!0}}}})}function p(e,s){return"DONE"!==s&&new Date(e)<new Date}async function j({params:e}){let s=parseInt((await e).id);isNaN(s)&&(0,x.notFound)();let r=await h(s);r||(0,x.notFound)();let a={total:r.projectTasks.length,todo:r.projectTasks.filter(e=>"TODO"===e.status).length,inProgress:r.projectTasks.filter(e=>"IN_PROGRESS"===e.status).length,done:r.projectTasks.filter(e=>"DONE"===e.status).length,overdue:r.projectTasks.filter(e=>"DONE"!==e.status&&new Date(e.dueDate)<new Date).length};return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:r.name}),(0,t.jsxs)("p",{className:"text-gray-600 mt-2",children:["Started: ",new Date(r.startDate).toLocaleDateString()]}),r.description&&(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:r.description})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n(),{href:`/admin/projects/${r.id}/edit`,children:(0,t.jsx)(i.$,{variant:"outline",children:"Edit Project"})}),(0,t.jsx)(n(),{href:"/admin",children:(0,t.jsx)(i.$,{variant:"outline",children:"Back to Admin"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{className:"pb-2",children:(0,t.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Tasks"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:a.total})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{className:"pb-2",children:(0,t.jsx)(l.ZB,{className:"text-sm font-medium",children:"To Do"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:a.todo})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{className:"pb-2",children:(0,t.jsx)(l.ZB,{className:"text-sm font-medium",children:"In Progress"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:a.inProgress})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{className:"pb-2",children:(0,t.jsx)(l.ZB,{className:"text-sm font-medium",children:"Completed"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.done})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{className:"pb-2",children:(0,t.jsx)(l.ZB,{className:"text-sm font-medium",children:"Overdue"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:a.overdue})})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Team Assignments"}),(0,t.jsx)(l.BT,{children:"Role assignments for this project"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:r.projectRoles.map(e=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.role.name}),(0,t.jsx)("p",{className:"text-gray-600",children:e.userName}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)(n(),{href:`/dashboard/${e.userName}`,children:(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"View Tasks"})})})]},e.id))})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Project Tasks"}),(0,t.jsx)(l.BT,{children:"All tasks for this project organized by due date"})]}),(0,t.jsx)(l.Wu,{children:0===r.projectTasks.length?(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No tasks generated for this project yet"})}):(0,t.jsxs)(o.Table,{children:[(0,t.jsx)(o.TableHeader,{children:(0,t.jsxs)(o.TableRow,{children:[(0,t.jsx)(o.TableHead,{children:"Task"}),(0,t.jsx)(o.TableHead,{children:"Assigned To"}),(0,t.jsx)(o.TableHead,{children:"Role"}),(0,t.jsx)(o.TableHead,{children:"Due Date"}),(0,t.jsx)(o.TableHead,{children:"Status"})]})}),(0,t.jsx)(o.TableBody,{children:r.projectTasks.map(e=>(0,t.jsxs)(o.TableRow,{className:p(e.dueDate,e.status)?"bg-red-50":"",children:[(0,t.jsxs)(o.TableCell,{className:"font-medium",children:[e.description,p(e.dueDate,e.status)&&(0,t.jsx)(d.E,{variant:"destructive",className:"ml-2 text-xs",children:"Overdue"})]}),(0,t.jsx)(o.TableCell,{children:e.projectRole.userName}),(0,t.jsx)(o.TableCell,{children:(0,t.jsx)(d.E,{variant:"outline",children:e.projectRole.role.name})}),(0,t.jsx)(o.TableCell,{children:new Date(e.dueDate).toLocaleDateString()}),(0,t.jsx)(o.TableCell,{children:function(e){switch(e){case"TODO":return(0,t.jsx)(d.E,{variant:"secondary",children:"To Do"});case"IN_PROGRESS":return(0,t.jsx)(d.E,{variant:"default",children:"In Progress"});case"DONE":return(0,t.jsx)(d.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"Done"});default:return(0,t.jsx)(d.E,{variant:"secondary",children:e})}}(e.status)})]},e.id))})]})})]})]})}},846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},4227:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>o});var t=r(5239),a=r(8088),n=r(8170),l=r.n(n),i=r(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let o={children:["",{children:["admin",{children:["projects",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,771)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/projects/[id]/page",pathname:"/admin/projects/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6330:e=>{e.exports=require("@prisma/client")},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{e.exports=require("url")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,982,277,923,287,174],()=>r(4227));module.exports=t})();