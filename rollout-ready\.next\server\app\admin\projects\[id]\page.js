(()=>{var e={};e.id=477,e.ids=[477],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>l,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>a,TableRow:()=>i});var s=r(60687);r(43210);var o=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm",e),...t})})}function a({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",e),...t})}function i({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(37413);r(61120);var o=r(70403),n=r(50662),a=r(10974);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,asChild:r=!1,...n}){let i=r?o.DX:"span";return(0,s.jsx)(i,{"data-slot":"badge",className:(0,a.cn)(l({variant:t}),e),...n})}},33873:e=>{"use strict";e.exports=require("path")},54227:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),o=r(48088),n=r(88170),a=r.n(n),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["admin",{children:["projects",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,70771)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\projects\\[id]\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/projects/[id]/page",pathname:"/admin/projects/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67540:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,80401))},69748:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,6211))},70771:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x,generateMetadata:()=>m});var s=r(37413),o=r(4536),n=r.n(o),a=r(78963),l=r(23469),i=r(30084),d=r(80401),c=r(5069),p=r(39916);async function u(e){return await c.z.project.findUnique({where:{id:e},include:{projectRoles:{include:{role:!0}},projectTasks:{include:{projectRole:{include:{role:!0}},templateTask:!0},orderBy:[{dueDate:"asc"},{createdAt:"desc"}]},_count:{select:{projectTasks:!0}}}})}function h(e,t){return"DONE"!==t&&new Date(e)<new Date}async function m({params:e}){let t=parseInt((await e).id);if(isNaN(t))return{title:"Project Not Found - Rollout Ready"};let r=await u(t);return r?{title:`${r.name} - Rollout Ready`,description:r.description||`Project details for ${r.name}`}:{title:"Project Not Found - Rollout Ready"}}async function x({params:e}){let t=parseInt((await e).id);isNaN(t)&&(0,p.notFound)();let r=await u(t);r||(0,p.notFound)();let o={total:r.projectTasks.length,todo:r.projectTasks.filter(e=>"TODO"===e.status).length,inProgress:r.projectTasks.filter(e=>"IN_PROGRESS"===e.status).length,done:r.projectTasks.filter(e=>"DONE"===e.status).length,overdue:r.projectTasks.filter(e=>"DONE"!==e.status&&new Date(e.dueDate)<new Date).length};return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:r.name}),(0,s.jsxs)("p",{className:"text-gray-600 mt-2",children:["Started: ",new Date(r.startDate).toLocaleDateString()]}),r.description&&(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:r.description})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n(),{href:`/admin/projects/${r.id}/edit`,children:(0,s.jsx)(l.$,{variant:"outline",children:"Edit Project"})}),(0,s.jsx)(n(),{href:"/admin",children:(0,s.jsx)(l.$,{variant:"outline",children:"Back to Admin"})})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Total Tasks"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:o.total})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"To Do"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:o.todo})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"In Progress"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:o.inProgress})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Completed"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:o.done})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{className:"pb-2",children:(0,s.jsx)(a.ZB,{className:"text-sm font-medium",children:"Overdue"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:o.overdue})})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"Team Assignments"}),(0,s.jsx)(a.BT,{children:"Role assignments for this project"})]}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:r.projectRoles.map(e=>(0,s.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:e.role.name}),(0,s.jsx)("p",{className:"text-gray-600",children:e.userName}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)(n(),{href:`/dashboard/${e.userName}`,children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",children:"View Tasks"})})})]},e.id))})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"Project Tasks"}),(0,s.jsx)(a.BT,{children:"All tasks for this project organized by due date"})]}),(0,s.jsx)(a.Wu,{children:0===r.projectTasks.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No tasks generated for this project yet"})}):(0,s.jsxs)(d.Table,{children:[(0,s.jsx)(d.TableHeader,{children:(0,s.jsxs)(d.TableRow,{children:[(0,s.jsx)(d.TableHead,{children:"Task"}),(0,s.jsx)(d.TableHead,{children:"Assigned To"}),(0,s.jsx)(d.TableHead,{children:"Role"}),(0,s.jsx)(d.TableHead,{children:"Due Date"}),(0,s.jsx)(d.TableHead,{children:"Status"})]})}),(0,s.jsx)(d.TableBody,{children:r.projectTasks.map(e=>(0,s.jsxs)(d.TableRow,{className:h(e.dueDate,e.status)?"bg-red-50":"",children:[(0,s.jsxs)(d.TableCell,{className:"font-medium",children:[e.description,h(e.dueDate,e.status)&&(0,s.jsx)(i.E,{variant:"destructive",className:"ml-2 text-xs",children:"Overdue"})]}),(0,s.jsx)(d.TableCell,{children:e.projectRole.userName}),(0,s.jsx)(d.TableCell,{children:(0,s.jsx)(i.E,{variant:"outline",children:e.projectRole.role.name})}),(0,s.jsx)(d.TableCell,{children:new Date(e.dueDate).toLocaleDateString()}),(0,s.jsx)(d.TableCell,{children:function(e){switch(e){case"TODO":return(0,s.jsx)(i.E,{variant:"secondary",children:"To Do"});case"IN_PROGRESS":return(0,s.jsx)(i.E,{variant:"default",children:"In Progress"});case"DONE":return(0,s.jsx)(i.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"Done"});default:return(0,s.jsx)(i.E,{variant:"secondary",children:e})}}(e.status)})]},e.id))})]})})]})]})}},79551:e=>{"use strict";e.exports=require("url")},80401:(e,t,r)=>{"use strict";r.d(t,{Table:()=>o,TableBody:()=>a,TableCell:()=>d,TableHead:()=>l,TableHeader:()=>n,TableRow:()=>i});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","Table"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHeader"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableBody");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableFooter");let l=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableHead"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableRow"),d=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCell");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ui\\table.tsx","TableCaption")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,205,277,923,811,624],()=>r(54227));module.exports=s})();