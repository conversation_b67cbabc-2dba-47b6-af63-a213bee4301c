exports.id=811,exports.ids=[811],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(49384),n=r(82348);function o(...e){return(0,n.QP)((0,s.$)(e))}},6528:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\components\\\\ConnectionStatus.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\ConnectionStatus.tsx","default")},27895:(e,t,r)=>{Promise.resolve().then(r.bind(r,82935)),Promise.resolve().then(r.bind(r,6528)),Promise.resolve().then(r.bind(r,88928)),Promise.resolve().then(r.bind(r,93709))},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(60687);r(43210);var n=r(8730),o=r(24224),a=r(4780);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:o=!1,...l}){let d=o?n.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,a.cn)(i({variant:t,size:r,className:e})),...l})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>o,aR:()=>a});var s=r(60687);r(43210);var n=r(4780);function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},54382:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(60687),n=r(43210),o=r(96834);function a(){let[e,t]=(0,n.useState)(!0),[r,a]=(0,n.useState)(!1);return!r&&e?null:(0,s.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,s.jsx)(o.E,{variant:e?"default":"destructive",className:`transition-all duration-300 ${e?"bg-green-100 text-green-800 border-green-200":"bg-red-100 text-red-800 border-red-200"}`,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-2 h-2 rounded-full ${e?"bg-green-500":"bg-red-500"}`}),e?"Back Online":"Offline"]})})})}},58297:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>i});var s=r(60687),n=r(43210),o=r(16189);let a=(0,n.createContext)(void 0);function i({children:e}){let[t,r]=(0,n.useState)(null),[i,l]=(0,n.useState)(!0),d=(0,o.useRouter)(),c=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let t=await e.json();r(t.user)}else r(null)}catch(e){console.error("Auth check failed:",e),r(null)}finally{l(!1)}},u=async(e,t)=>{try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t})});if(s.ok){let e=await s.json();return r(e.user),!0}return!1}catch(e){return console.error("Login failed:",e),!1}},m=async()=>{try{await fetch("/api/auth/logout",{method:"POST"})}catch(e){console.error("Logout failed:",e)}finally{r(null),d.push("/login")}};return(0,s.jsx)(a.Provider,{value:{user:t,isLoading:i,login:u,logout:m,checkAuth:c},children:e})}function l(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},61135:()=>{},65027:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687),n=r(43210),o=r(29523),a=r(44493);function i(){let[e,t]=(0,n.useState)(null),[r,i]=(0,n.useState)(!1),[l,d]=(0,n.useState)(!1),c=async()=>{if(!e)return;e.prompt();let{outcome:r}=await e.userChoice;"accepted"===r&&i(!1),t(null)};return l||!r||!e||sessionStorage.getItem("pwa-install-dismissed")?null:(0,s.jsxs)(a.Zp,{className:"fixed bottom-4 left-4 right-4 z-50 shadow-lg border-blue-200 bg-blue-50 md:left-auto md:right-4 md:w-96",children:[(0,s.jsxs)(a.aR,{className:"pb-3",children:[(0,s.jsxs)(a.ZB,{className:"text-lg flex items-center gap-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Install Rollout Ready"]}),(0,s.jsx)(a.BT,{children:"Install this app on your device for quick access and offline use"})]}),(0,s.jsxs)(a.Wu,{className:"pt-0",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(o.$,{onClick:c,className:"flex-1",children:"Install App"}),(0,s.jsx)(o.$,{variant:"outline",onClick:()=>{i(!1),sessionStorage.setItem("pwa-install-dismissed","true")},children:"Not Now"})]}),(0,s.jsxs)("div",{className:"mt-3 text-xs text-gray-600",children:[(0,s.jsx)("p",{children:"✓ Works offline"}),(0,s.jsx)("p",{children:"✓ Quick access from home screen"}),(0,s.jsx)("p",{children:"✓ Native app experience"})]})]})]})}},70313:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},80041:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},81039:(e,t,r)=>{Promise.resolve().then(r.bind(r,58297)),Promise.resolve().then(r.bind(r,54382)),Promise.resolve().then(r.bind(r,86246)),Promise.resolve().then(r.bind(r,65027))},82935:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\AuthProvider.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\AuthProvider.tsx","useAuth")},86246:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(60687),n=r(85814),o=r.n(n),a=r(29523),i=r(58297),l=r(16189);function d(){let{user:e,logout:t}=(0,i.A)(),r=(0,l.useRouter)(),n=async()=>{await t(),r.push("/login")};return e?(0,s.jsx)("nav",{className:"border-b bg-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Rollout Ready"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(o(),{href:"/",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"Home"}),("ADMIN"===e.systemRole||"MANAGER"===e.systemRole)&&(0,s.jsx)(o(),{href:"/admin",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,s.jsx)(o(),{href:`/dashboard/${e.username}`,className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"My Tasks"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:e.firstName||e.username}),(0,s.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded",children:e.systemRole})]}),(0,s.jsx)(a.$,{variant:"outline",onClick:n,children:"Logout"})]})]})})}):(0,s.jsx)("nav",{className:"border-b bg-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Rollout Ready"})}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsx)(o(),{href:"/login",children:(0,s.jsx)(a.$,{variant:"outline",children:"Login"})})})]})})})}},88928:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\Navigation.tsx","default")},93709:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\components\\\\PWAInstallPrompt.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\PWAInstallPrompt.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>m});var s=r(37413),n=r(22376),o=r.n(n),a=r(68726),i=r.n(a);r(61135);var l=r(93709),d=r(6528),c=r(82935),u=r(88928);let m={title:"Rollout Ready",description:"Role-based checklist and task management system for large-scale implementation projects",manifest:"/manifest.json",themeColor:"#3b82f6",appleWebApp:{capable:!0,statusBarStyle:"default",title:"Rollout Ready"},formatDetection:{telephone:!1},openGraph:{type:"website",siteName:"Rollout Ready",title:"Rollout Ready",description:"Role-based checklist and task management system for large-scale implementation projects"},twitter:{card:"summary",title:"Rollout Ready",description:"Role-based checklist and task management system for large-scale implementation projects"}};function h({children:e}){return(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}),(0,s.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Rollout Ready"}),(0,s.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,s.jsx)("meta",{name:"msapplication-TileColor",content:"#3b82f6"}),(0,s.jsx)("meta",{name:"msapplication-tap-highlight",content:"no"}),(0,s.jsx)("link",{rel:"icon",href:"/icons/icon-192x192.svg"}),(0,s.jsx)("link",{rel:"apple-touch-icon",href:"/icons/icon-192x192.svg"}),(0,s.jsx)("link",{rel:"manifest",href:"/manifest.json"})]}),(0,s.jsx)("body",{className:`${o().variable} ${i().variable} antialiased min-h-screen bg-background`,children:(0,s.jsxs)(c.AuthProvider,{children:[(0,s.jsx)(u.default,{}),(0,s.jsx)("main",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:e}),(0,s.jsx)(l.default,{}),(0,s.jsx)(d.default,{})]})})]})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var n=r(8730),o=r(24224),a=r(4780);let i=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...o}){let l=r?n.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,a.cn)(i({variant:t}),e),...o})}}};