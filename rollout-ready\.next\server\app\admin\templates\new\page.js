(()=>{var e={};e.id=843,e.ids=[843],e.modules={161:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\app\\\\admin\\\\templates\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\new\\page.tsx","default")},440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var r=n(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},932:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>rx});var r,o,i,a=n(687),l=n(3210),s=n.t(l,2),c=n(6189),u=n(5814),d=n.n(u),f=n(4493),p=n(9523),h=n(9667),m=n(4300),v=n(1215);function g(e,[t,n]){return Math.min(n,Math.max(t,e))}function y(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function x(e,t=[]){let n=[],r=()=>{let t=n.map(e=>l.createContext(e));return function(n){let r=n?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=l.createContext(r),i=n.length;n=[...n,r];let s=t=>{let{scope:n,children:r,...s}=t,c=n?.[e]?.[i]||o,u=l.useMemo(()=>s,Object.values(s));return(0,a.jsx)(c.Provider,{value:u,children:r})};return s.displayName=t+"Provider",[s,function(n,a){let s=a?.[e]?.[i]||o,c=l.useContext(s);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var w=n(8599),b=n(8730),E=new WeakMap;function C(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=S(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function S(e){return e!=e||0===e?0:Math.trunc(e)}var j=l.createContext(void 0),R=n(4163);function k(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var N="dismissableLayer.update",T=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),A=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:s,onInteractOutside:c,onDismiss:u,...d}=e,f=l.useContext(T),[p,h]=l.useState(null),m=p?.ownerDocument??globalThis?.document,[,v]=l.useState({}),g=(0,w.s)(t,e=>h(e)),x=Array.from(f.layers),[b]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),E=x.indexOf(b),C=p?x.indexOf(p):-1,S=f.layersWithOutsidePointerEventsDisabled.size>0,j=C>=E,A=function(e,t=globalThis?.document){let n=k(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){L("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...f.branches].some(e=>e.contains(t));j&&!n&&(i?.(e),c?.(e),e.defaultPrevented||u?.())},m),D=function(e,t=globalThis?.document){let n=k(e),r=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!r.current&&L("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...f.branches].some(e=>e.contains(t))&&(s?.(e),c?.(e),e.defaultPrevented||u?.())},m);return!function(e,t=globalThis?.document){let n=k(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{C===f.layers.size-1&&(r?.(e),!e.defaultPrevented&&u&&(e.preventDefault(),u()))},m),l.useEffect(()=>{if(p)return n&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(o=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(p)),f.layers.add(p),P(),()=>{n&&1===f.layersWithOutsidePointerEventsDisabled.size&&(m.body.style.pointerEvents=o)}},[p,m,n,f]),l.useEffect(()=>()=>{p&&(f.layers.delete(p),f.layersWithOutsidePointerEventsDisabled.delete(p),P())},[p,f]),l.useEffect(()=>{let e=()=>v({});return document.addEventListener(N,e),()=>document.removeEventListener(N,e)},[]),(0,a.jsx)(R.sG.div,{...d,ref:g,style:{pointerEvents:S?j?"auto":"none":void 0,...e.style},onFocusCapture:y(e.onFocusCapture,D.onFocusCapture),onBlurCapture:y(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:y(e.onPointerDownCapture,A.onPointerDownCapture)})});function P(){let e=new CustomEvent(N);document.dispatchEvent(e)}function L(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,R.hO)(o,i):o.dispatchEvent(i)}A.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(T),r=l.useRef(null),o=(0,w.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(R.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var D=0;function M(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var O="focusScope.autoFocusOnMount",I="focusScope.autoFocusOnUnmount",_={bubbles:!1,cancelable:!0},W=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[c,u]=l.useState(null),d=k(o),f=k(i),p=l.useRef(null),h=(0,w.s)(t,e=>u(e)),m=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(m.paused||!c)return;let t=e.target;c.contains(t)?p.current=t:H(p.current,{select:!0})},t=function(e){if(m.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||H(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&H(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,m.paused]),l.useEffect(()=>{if(c){z.add(m);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(O,_);c.addEventListener(O,d),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(H(r,{select:t}),document.activeElement!==n)return}(F(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&H(c))}return()=>{c.removeEventListener(O,d),setTimeout(()=>{let t=new CustomEvent(I,_);c.addEventListener(I,f),c.dispatchEvent(t),t.defaultPrevented||H(e??document.body,{select:!0}),c.removeEventListener(I,f),z.remove(m)},0)}}},[c,d,f,m]);let v=l.useCallback(e=>{if(!n&&!r||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=F(e);return[B(t,e),B(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&H(i,{select:!0})):(e.preventDefault(),n&&H(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,m.paused]);return(0,a.jsx)(R.sG.div,{tabIndex:-1,...s,ref:h,onKeyDown:v})});function F(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function B(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function H(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}W.displayName="FocusScope";var z=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=$(e,t)).unshift(t)},remove(t){e=$(e,t),e[0]?.resume()}}}();function $(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var G=globalThis?.document?l.useLayoutEffect:()=>{},V=s[" useId ".trim().toString()]||(()=>void 0),K=0;function q(e){let[t,n]=l.useState(V());return G(()=>{e||n(e=>e??String(K++))},[e]),e||(t?`radix-${t}`:"")}let U=["top","right","bottom","left"],X=Math.min,Y=Math.max,Z=Math.round,J=Math.floor,Q=e=>({x:e,y:e}),ee={left:"right",right:"left",bottom:"top",top:"bottom"},et={start:"end",end:"start"};function en(e,t){return"function"==typeof e?e(t):e}function er(e){return e.split("-")[0]}function eo(e){return e.split("-")[1]}function ei(e){return"x"===e?"y":"x"}function ea(e){return"y"===e?"height":"width"}function el(e){return["top","bottom"].includes(er(e))?"y":"x"}function es(e){return e.replace(/start|end/g,e=>et[e])}function ec(e){return e.replace(/left|right|bottom|top/g,e=>ee[e])}function eu(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ed(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ef(e,t,n){let r,{reference:o,floating:i}=e,a=el(t),l=ei(el(t)),s=ea(l),c=er(t),u="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(eo(t)){case"start":r[l]-=p*(n&&u?-1:1);break;case"end":r[l]+=p*(n&&u?-1:1)}return r}let ep=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=ef(c,r,s),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:x}=await m({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(f=x.placement),x.rects&&(c=!0===x.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):x.rects),{x:u,y:d}=ef(c,f,s)),n=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function eh(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=en(t,e),h=eu(p),m=l[f?"floating"===d?"reference":"floating":d],v=ed(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:s})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),x=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},w=ed(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-w.top+h.top)/x.y,bottom:(w.bottom-v.bottom+h.bottom)/x.y,left:(v.left-w.left+h.left)/x.x,right:(w.right-v.right+h.right)/x.x}}function em(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ev(e){return U.some(t=>e[t]>=0)}async function eg(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=er(n),l=eo(n),s="y"===el(n),c=["left","top"].includes(a)?-1:1,u=i&&s?-1:1,d=en(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),s?{x:p*u,y:f*c}:{x:f*c,y:p*u}}function ey(){return"undefined"!=typeof window}function ex(e){return eE(e)?(e.nodeName||"").toLowerCase():"#document"}function ew(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eb(e){var t;return null==(t=(eE(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eE(e){return!!ey()&&(e instanceof Node||e instanceof ew(e).Node)}function eC(e){return!!ey()&&(e instanceof Element||e instanceof ew(e).Element)}function eS(e){return!!ey()&&(e instanceof HTMLElement||e instanceof ew(e).HTMLElement)}function ej(e){return!!ey()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ew(e).ShadowRoot)}function eR(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eP(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ek(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eN(e){let t=eT(),n=eC(e)?eP(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function eT(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eA(e){return["html","body","#document"].includes(ex(e))}function eP(e){return ew(e).getComputedStyle(e)}function eL(e){return eC(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eD(e){if("html"===ex(e))return e;let t=e.assignedSlot||e.parentNode||ej(e)&&e.host||eb(e);return ej(t)?t.host:t}function eM(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eD(t);return eA(n)?t.ownerDocument?t.ownerDocument.body:t.body:eS(n)&&eR(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=ew(o);if(i){let e=eO(a);return t.concat(a,a.visualViewport||[],eR(o)?o:[],e&&n?eM(e):[])}return t.concat(o,eM(o,[],n))}function eO(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eI(e){let t=eP(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eS(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=Z(n)!==i||Z(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function e_(e){return eC(e)?e:e.contextElement}function eW(e){let t=e_(e);if(!eS(t))return Q(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eI(t),a=(i?Z(n.width):n.width)/r,l=(i?Z(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eF=Q(0);function eB(e){let t=ew(e);return eT()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eF}function eH(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=e_(e),l=Q(1);t&&(r?eC(r)&&(l=eW(r)):l=eW(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===ew(a))&&o)?eB(a):Q(0),c=(i.left+s.x)/l.x,u=(i.top+s.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=ew(a),t=r&&eC(r)?ew(r):r,n=e,o=eO(n);for(;o&&r&&t!==n;){let e=eW(o),t=o.getBoundingClientRect(),r=eP(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,f*=e.y,c+=i,u+=a,o=eO(n=ew(o))}}return ed({width:d,height:f,x:c,y:u})}function ez(e,t){let n=eL(e).scrollLeft;return t?t.left+n:eH(eb(e)).left+n}function e$(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ez(e,r)),y:r.top+t.scrollTop}}function eG(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ew(e),r=eb(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=eT();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(e,n);else if("document"===t)r=function(e){let t=eb(e),n=eL(e),r=e.ownerDocument.body,o=Y(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Y(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ez(e),l=-n.scrollTop;return"rtl"===eP(r).direction&&(a+=Y(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(eb(e));else if(eC(t))r=function(e,t){let n=eH(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eS(e)?eW(e):Q(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=eB(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ed(r)}function eV(e){return"static"===eP(e).position}function eK(e,t){if(!eS(e)||"fixed"===eP(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eb(e)===n&&(n=n.ownerDocument.body),n}function eq(e,t){let n=ew(e);if(ek(e))return n;if(!eS(e)){let t=eD(e);for(;t&&!eA(t);){if(eC(t)&&!eV(t))return t;t=eD(t)}return n}let r=eK(e,t);for(;r&&["table","td","th"].includes(ex(r))&&eV(r);)r=eK(r,t);return r&&eA(r)&&eV(r)&&!eN(r)?n:r||function(e){let t=eD(e);for(;eS(t)&&!eA(t);){if(eN(t))return t;if(ek(t))break;t=eD(t)}return null}(e)||n}let eU=async function(e){let t=this.getOffsetParent||eq,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eS(t),o=eb(t),i="fixed"===n,a=eH(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=Q(0);if(r||!r&&!i)if(("body"!==ex(t)||eR(o))&&(l=eL(t)),r){let e=eH(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=ez(o));i&&!r&&o&&(s.x=ez(o));let c=!o||r||i?Q(0):e$(o,l);return{x:a.left+l.scrollLeft-s.x-c.x,y:a.top+l.scrollTop-s.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eX={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=eb(r),l=!!t&&ek(t.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},c=Q(1),u=Q(0),d=eS(r);if((d||!d&&!i)&&(("body"!==ex(r)||eR(a))&&(s=eL(r)),eS(r))){let e=eH(r);c=eW(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}let f=!a||d||i?Q(0):e$(a,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+u.x+f.x,y:n.y*c.y-s.scrollTop*c.y+u.y+f.y}},getDocumentElement:eb,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ek(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eM(e,[],!1).filter(e=>eC(e)&&"body"!==ex(e)),o=null,i="fixed"===eP(e).position,a=i?eD(e):e;for(;eC(a)&&!eA(a);){let t=eP(a),n=eN(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||eR(a)&&!n&&function e(t,n){let r=eD(t);return!(r===n||!eC(r)||eA(r))&&("fixed"===eP(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=eD(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eG(t,n,o);return e.top=Y(r.top,e.top),e.right=X(r.right,e.right),e.bottom=X(r.bottom,e.bottom),e.left=Y(r.left,e.left),e},eG(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eq,getElementRects:eU,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eI(e);return{width:t,height:n}},getScale:eW,isElement:eC,isRTL:function(e){return"rtl"===eP(e).direction}};function eY(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eZ=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:s}=t,{element:c,padding:u=0}=en(e,t)||{};if(null==c)return{};let d=eu(u),f={x:n,y:r},p=ei(el(o)),h=ea(p),m=await a.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],x=f[p]-i.reference[p],w=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),b=w?w[g]:0;b&&await (null==a.isElement?void 0:a.isElement(w))||(b=l.floating[g]||i.floating[h]);let E=b/2-m[h]/2-1,C=X(d[v?"top":"left"],E),S=X(d[v?"bottom":"right"],E),j=b-m[h]-S,R=b/2-m[h]/2+(y/2-x/2),k=Y(C,X(R,j)),N=!s.arrow&&null!=eo(o)&&R!==k&&i.reference[h]/2-(R<C?C:S)-m[h]/2<0,T=N?R<C?R-C:R-j:0;return{[p]:f[p]+T,data:{[p]:k,centerOffset:R-k-T,...N&&{alignmentOffset:T}},reset:N}}}),eJ=(e,t,n)=>{let r=new Map,o={platform:eX,...n},i={...o.platform,_c:r};return ep(e,t,{...o,platform:i})};var eQ="undefined"!=typeof document?l.useLayoutEffect:function(){};function e0(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e0(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e0(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e1(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e2(e,t){let n=e1(e);return Math.round(t*n)/n}function e3(e){let t=l.useRef(e);return eQ(()=>{t.current=e}),t}let e4=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eZ({element:n.current,padding:r}).fn(t):{}:n?eZ({element:n,padding:r}).fn(t):{}}}),e5=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await eg(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}),e6=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=en(e,t),c={x:n,y:r},u=await eh(t,s),d=el(er(o)),f=ei(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+u[e],r=p-u[t];p=Y(n,X(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+u[e],r=h-u[t];h=Y(n,X(h,r))}let m=l.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}),e8=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:c=!0}=en(e,t),u={x:n,y:r},d=el(o),f=ei(d),p=u[f],h=u[d],m=en(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(er(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),e9=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:s,rects:c,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...x}=en(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let w=er(l),b=el(u),E=er(u)===u,C=await (null==d.isRTL?void 0:d.isRTL(f.floating)),S=m||(E||!y?[ec(u)]:function(e){let t=ec(e);return[es(e),t,es(t)]}(u)),j="none"!==g;!m&&j&&S.push(...function(e,t,n,r){let o=eo(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(er(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(es)))),i}(u,y,g,C));let R=[u,...S],k=await eh(t,x),N=[],T=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&N.push(k[w]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=eo(e),o=ei(el(e)),i=ea(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=ec(a)),[a,ec(a)]}(l,c,C);N.push(k[e[0]],k[e[1]])}if(T=[...T,{placement:l,overflows:N}],!N.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=R[e];if(t&&("alignment"!==h||b===el(t)||T.every(e=>e.overflows[0]>0&&el(e.placement)===b)))return{data:{index:e,overflows:T},reset:{placement:t}};let n=null==(i=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=T.filter(e=>{if(j){let t=el(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=u}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),e7=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:s,elements:c}=t,{apply:u=()=>{},...d}=en(e,t),f=await eh(t,d),p=er(a),h=eo(a),m="y"===el(a),{width:v,height:g}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,x=v-f.left-f.right,w=X(g-f[o],y),b=X(v-f[i],x),E=!t.middlewareData.shift,C=w,S=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(S=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=y),E&&!h){let e=Y(f.left,0),t=Y(f.right,0),n=Y(f.top,0),r=Y(f.bottom,0);m?S=v-2*(0!==e||0!==t?e+t:Y(f.left,f.right)):C=g-2*(0!==n||0!==r?n+r:Y(f.top,f.bottom))}await u({...t,availableWidth:S,availableHeight:C});let j=await s.getDimensions(c.floating);return v!==j.width||g!==j.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),te=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=en(e,t);switch(r){case"referenceHidden":{let e=em(await eh(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ev(e)}}}case"escaped":{let e=em(await eh(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ev(e)}}}default:return{}}}}}(e),options:[e,t]}),tt=(e,t)=>({...e4(e),options:[e,t]});var tn=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,a.jsx)(R.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tn.displayName="Arrow";var tr="Popper",[to,ti]=x(tr),[ta,tl]=to(tr),ts=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,a.jsx)(ta,{scope:t,anchor:r,onAnchorChange:o,children:n})};ts.displayName=tr;var tc="PopperAnchor",tu=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=tl(tc,n),s=l.useRef(null),c=(0,w.s)(t,s);return l.useEffect(()=>{i.onAnchorChange(r?.current||s.current)}),r?null:(0,a.jsx)(R.sG.div,{...o,ref:c})});tu.displayName=tc;var td="PopperContent",[tf,tp]=to(td),th=l.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:h=!1,updatePositionStrategy:m="optimized",onPlaced:g,...y}=e,x=tl(td,n),[b,E]=l.useState(null),C=(0,w.s)(t,e=>E(e)),[S,j]=l.useState(null),N=function(e){let[t,n]=l.useState(void 0);return G(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(S),T=N?.width??0,A=N?.height??0,P="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},L=Array.isArray(d)?d:[d],D=L.length>0,M={padding:P,boundary:L.filter(ty),altBoundary:D},{refs:O,floatingStyles:I,placement:_,isPositioned:W,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:c,open:u}=e,[d,f]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=l.useState(r);e0(p,r)||h(r);let[m,g]=l.useState(null),[y,x]=l.useState(null),w=l.useCallback(e=>{e!==S.current&&(S.current=e,g(e))},[]),b=l.useCallback(e=>{e!==j.current&&(j.current=e,x(e))},[]),E=i||m,C=a||y,S=l.useRef(null),j=l.useRef(null),R=l.useRef(d),k=null!=c,N=e3(c),T=e3(o),A=e3(u),P=l.useCallback(()=>{if(!S.current||!j.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),eJ(S.current,j.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};L.current&&!e0(R.current,t)&&(R.current=t,v.flushSync(()=>{f(t)}))})},[p,t,n,T,A]);eQ(()=>{!1===u&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[u]);let L=l.useRef(!1);eQ(()=>(L.current=!0,()=>{L.current=!1}),[]),eQ(()=>{if(E&&(S.current=E),C&&(j.current=C),E&&C){if(N.current)return N.current(E,C,P);P()}},[E,C,P,N,k]);let D=l.useMemo(()=>({reference:S,floating:j,setReference:w,setFloating:b}),[w,b]),M=l.useMemo(()=>({reference:E,floating:C}),[E,C]),O=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=e2(M.floating,d.x),r=e2(M.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...e1(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,M.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:P,refs:D,elements:M,floatingStyles:O}),[d,P,D,M,O])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,u=e_(e),d=i||a?[...u?eM(u):[],...eM(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=u&&s?function(e,t){let n,r=null,o=eb(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),i();let c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let h=J(d),m=J(o.clientWidth-(u+f)),v={rootMargin:-h+"px "+-m+"px "+-J(o.clientHeight-(d+p))+"px "+-J(u)+"px",threshold:Y(0,X(1,s))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==s){if(!g)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eY(c,e.getBoundingClientRect())||a(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(u,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),u&&!c&&h.observe(u),h.observe(t));let m=c?eH(e):null;return c&&function t(){let r=eH(e);m&&!eY(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===m}),elements:{reference:x.anchor},middleware:[e5({mainAxis:o+A,alignmentAxis:s}),u&&e6({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?e8():void 0,...M}),u&&e9({...M}),e7({...M,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),S&&tt({element:S,padding:c}),tx({arrowWidth:T,arrowHeight:A}),h&&te({strategy:"referenceHidden",...M})]}),[B,H]=tw(_),z=k(g);G(()=>{W&&z?.()},[W,z]);let $=F.arrow?.x,V=F.arrow?.y,K=F.arrow?.centerOffset!==0,[q,U]=l.useState();return G(()=>{b&&U(window.getComputedStyle(b).zIndex)},[b]),(0,a.jsx)("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:W?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(tf,{scope:n,placedSide:B,onArrowChange:j,arrowX:$,arrowY:V,shouldHideArrow:K,children:(0,a.jsx)(R.sG.div,{"data-side":B,"data-align":H,...y,ref:C,style:{...y.style,animation:W?void 0:"none"}})})})});th.displayName=td;var tm="PopperArrow",tv={top:"bottom",right:"left",bottom:"top",left:"right"},tg=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tp(tm,n),i=tv[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(tn,{...r,ref:t,style:{...r.style,display:"block"}})})});function ty(e){return null!==e}tg.displayName=tm;var tx=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[s,c]=tw(n),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===s?(p=i?u:`${d}px`,h=`${-l}px`):"top"===s?(p=i?u:`${d}px`,h=`${r.floating.height+l}px`):"right"===s?(p=`${-l}px`,h=i?u:`${f}px`):"left"===s&&(p=`${r.floating.width+l}px`,h=i?u:`${f}px`),{data:{x:p,y:h}}}});function tw(e){let[t,n="center"]=e.split("-");return[t,n]}var tb=l.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=l.useState(!1);G(()=>i(!0),[]);let s=n||o&&globalThis?.document?.body;return s?v.createPortal((0,a.jsx)(R.sG.div,{...r,ref:t}),s):null});tb.displayName="Portal";var tE=s[" useInsertionEffect ".trim().toString()]||G;function tC({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return tE(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,l.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[s,e,i,a])]}Symbol("RADIX:SYNC_STATE");var tS=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,a.jsx)(R.sG.span,{...e,ref:t,style:{...tS,...e.style}})).displayName="VisuallyHidden";var tj=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tR=new WeakMap,tk=new WeakMap,tN={},tT=0,tA=function(e){return e&&(e.host||tA(e.parentNode))},tP=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tA(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tN[n]||(tN[n]=new WeakMap);var i=tN[n],a=[],l=new Set,s=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,s=(tR.get(e)||0)+1,c=(i.get(e)||0)+1;tR.set(e,s),i.set(e,c),a.push(e),1===s&&o&&tk.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return u(t),l.clear(),tT++,function(){a.forEach(function(e){var t=tR.get(e)-1,o=i.get(e)-1;tR.set(e,t),i.set(e,o),t||(tk.has(e)||e.removeAttribute(r),tk.delete(e)),o||e.removeAttribute(n)}),--tT||(tR=new WeakMap,tR=new WeakMap,tk=new WeakMap,tN={})}},tL=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||tj(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tP(r,o,n,"aria-hidden")):function(){return null}},tD=function(){return(tD=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tM(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tO=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tI="width-before-scroll-bar";function t_(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tW="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,tF=new WeakMap;function tB(e){return e}var tH=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=tB),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return i.options=tD({async:!0,ssr:!1},e),i}(),tz=function(){},t$=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),s=l.useState({onScrollCapture:tz,onWheelCapture:tz,onTouchMoveCapture:tz}),c=s[0],u=s[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,x=e.noIsolation,w=e.inert,b=e.allowPinchZoom,E=e.as,C=e.gapMode,S=tM(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=(n=[a,t],r=function(e){return n.forEach(function(t){return t_(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tW(function(){var e=tF.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||t_(e,null)}),r.forEach(function(e){t.has(e)||t_(e,o)})}tF.set(i,n)},[n]),i),R=tD(tD({},S),c);return l.createElement(l.Fragment,null,m&&l.createElement(g,{sideCar:tH,removeScrollBar:h,shards:v,noRelative:y,noIsolation:x,inert:w,setCallbacks:u,allowPinchZoom:!!b,lockRef:a,gapMode:C}),d?l.cloneElement(l.Children.only(f),tD(tD({},R),{ref:j})):l.createElement(void 0===E?"div":E,tD({},R,{className:p,ref:j}),f))});t$.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t$.classNames={fullWidth:tI,zeroRight:tO};var tG=function(e){var t=e.sideCar,n=tM(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,tD({},n))};tG.isSideCarExport=!0;var tV=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tK=function(){var e=tV();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tq=function(){var e=tK();return function(t){return e(t.styles,t.dynamic),null}},tU={left:0,top:0,right:0,gap:0},tX=function(e){return parseInt(e||"",10)||0},tY=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tX(n),tX(r),tX(o)]},tZ=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tU;var t=tY(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tJ=tq(),tQ="data-scroll-locked",t0=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(tQ,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tO," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tI," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tO," .").concat(tO," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tI," .").concat(tI," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(tQ,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},t1=function(){var e=parseInt(document.body.getAttribute(tQ)||"0",10);return isFinite(e)?e:0},t2=function(){l.useEffect(function(){return document.body.setAttribute(tQ,(t1()+1).toString()),function(){var e=t1()-1;e<=0?document.body.removeAttribute(tQ):document.body.setAttribute(tQ,e.toString())}},[])},t3=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t2();var i=l.useMemo(function(){return tZ(o)},[o]);return l.createElement(tJ,{styles:t0(i,!t,o,n?"":"!important")})},t4=!1;if("undefined"!=typeof window)try{var t5=Object.defineProperty({},"passive",{get:function(){return t4=!0,!0}});window.addEventListener("test",t5,t5),window.removeEventListener("test",t5,t5)}catch(e){t4=!1}var t6=!!t4&&{passive:!1},t8=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},t9=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),t7(e,r)){var o=ne(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},t7=function(e,t){return"v"===e?t8(t,"overflowY"):t8(t,"overflowX")},ne=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nt=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,s=n.target,c=t.contains(s),u=!1,d=l>0,f=0,p=0;do{if(!s)break;var h=ne(e,s),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&t7(e,s)&&(f+=v,p+=m);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&l>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(u=!0),u},nn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},nr=function(e){return[e.deltaX,e.deltaY]},no=function(e){return e&&"current"in e?e.current:e},ni=0,na=[];let nl=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(ni++)[0],i=l.useState(tq)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(no),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=nn(e),l=n.current,s="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=t9(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t9(d,u)),!f)return!1;if(!r.current&&"changedTouches"in e&&(s||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nt(p,t,e,"h"===p?s:c,!0)},[]),c=l.useCallback(function(e){if(na.length&&na[na.length-1]===i){var n="deltaY"in e?nr(e):nn(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(no).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=nn(e),r.current=void 0},[]),f=l.useCallback(function(t){u(t.type,nr(t),t.target,s(t,e.lockRef.current))},[]),p=l.useCallback(function(t){u(t.type,nn(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return na.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,t6),document.addEventListener("touchmove",c,t6),document.addEventListener("touchstart",d,t6),function(){na=na.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,t6),document.removeEventListener("touchmove",c,t6),document.removeEventListener("touchstart",d,t6)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(t3,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tH.useMedium(r),tG);var ns=l.forwardRef(function(e,t){return l.createElement(t$,tD({},e,{ref:t,sideCar:nl}))});ns.classNames=t$.classNames;var nc=[" ","Enter","ArrowUp","ArrowDown"],nu=[" ","Enter"],nd="Select",[nf,np,nh]=function(e){let t=e+"CollectionProvider",[n,r]=x(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=l.useRef(null),i=l.useRef(new Map).current;return(0,a.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};s.displayName=t;let c=e+"CollectionSlot",u=(0,b.TL)(c),d=l.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(c,n),l=(0,w.s)(t,o.collectionRef);return(0,a.jsx)(u,{ref:l,children:r})});d.displayName=c;let f=e+"CollectionItemSlot",p="data-radix-collection-item",h=(0,b.TL)(f),m=l.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,s=l.useRef(null),c=(0,w.s)(t,s),u=i(f,n);return l.useEffect(()=>(u.itemMap.set(s,{ref:s,...o}),()=>void u.itemMap.delete(s))),(0,a.jsx)(h,{...{[p]:""},ref:c,children:r})});return m.displayName=f,[{Provider:s,Slot:d,ItemSlot:m},function(t){let n=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(nd),[nm,nv]=x(nd,[nh,ti]),ng=ti(),[ny,nx]=nm(nd),[nw,nb]=nm(nd),nE=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:s,defaultValue:c,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:h,required:m,form:v}=e,g=ng(t),[y,x]=l.useState(null),[w,b]=l.useState(null),[E,C]=l.useState(!1),S=function(e){let t=l.useContext(j);return e||t||"ltr"}(d),[R,k]=tC({prop:r,defaultProp:o??!1,onChange:i,caller:nd}),[N,T]=tC({prop:s,defaultProp:c,onChange:u,caller:nd}),A=l.useRef(null),P=!y||v||!!y.closest("form"),[L,D]=l.useState(new Set),M=Array.from(L).map(e=>e.props.value).join(";");return(0,a.jsx)(ts,{...g,children:(0,a.jsxs)(ny,{required:m,scope:t,trigger:y,onTriggerChange:x,valueNode:w,onValueNodeChange:b,valueNodeHasChildren:E,onValueNodeHasChildrenChange:C,contentId:q(),value:N,onValueChange:T,open:R,onOpenChange:k,dir:S,triggerPointerDownPosRef:A,disabled:h,children:[(0,a.jsx)(nf.Provider,{scope:t,children:(0,a.jsx)(nw,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),P?(0,a.jsxs)(n5,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>T(e.target.value),disabled:h,form:v,children:[void 0===N?(0,a.jsx)("option",{value:""}):null,Array.from(L)]},M):null]})})};nE.displayName=nd;var nC="SelectTrigger",nS=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=ng(n),s=nx(nC,n),c=s.disabled||r,u=(0,w.s)(t,s.onTriggerChange),d=np(n),f=l.useRef("touch"),[p,h,m]=n8(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=n9(t,e,n);void 0!==r&&s.onValueChange(r.value)}),v=e=>{c||(s.onOpenChange(!0),m()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,a.jsx)(tu,{asChild:!0,...i,children:(0,a.jsx)(R.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":n6(s.value)?"":void 0,...o,ref:u,onClick:y(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&v(e)}),onPointerDown:y(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(v(e),e.preventDefault())}),onKeyDown:y(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&nc.includes(e.key)&&(v(),e.preventDefault())})})})});nS.displayName=nC;var nj="SelectValue",nR=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...s}=e,c=nx(nj,n),{onValueNodeHasChildrenChange:u}=c,d=void 0!==i,f=(0,w.s)(t,c.onValueNodeChange);return G(()=>{u(d)},[u,d]),(0,a.jsx)(R.sG.span,{...s,ref:f,style:{pointerEvents:"none"},children:n6(c.value)?(0,a.jsx)(a.Fragment,{children:l}):i})});nR.displayName=nj;var nk=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,a.jsx)(R.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nk.displayName="SelectIcon";var nN=e=>(0,a.jsx)(tb,{asChild:!0,...e});nN.displayName="SelectPortal";var nT="SelectContent",nA=l.forwardRef((e,t)=>{let n=nx(nT,e.__scopeSelect),[r,o]=l.useState();return(G(()=>{o(new DocumentFragment)},[]),n.open)?(0,a.jsx)(nM,{...e,ref:t}):r?v.createPortal((0,a.jsx)(nP,{scope:e.__scopeSelect,children:(0,a.jsx)(nf.Slot,{scope:e.__scopeSelect,children:(0,a.jsx)("div",{children:e.children})})}),r):null});nA.displayName=nT;var[nP,nL]=nm(nT),nD=(0,b.TL)("SelectContent.RemoveScroll"),nM=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:c,sideOffset:u,align:d,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:v,hideWhenDetached:g,avoidCollisions:x,...b}=e,E=nx(nT,n),[C,S]=l.useState(null),[j,R]=l.useState(null),k=(0,w.s)(t,e=>S(e)),[N,T]=l.useState(null),[P,L]=l.useState(null),O=np(n),[I,_]=l.useState(!1),F=l.useRef(!1);l.useEffect(()=>{if(C)return tL(C)},[C]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??M()),document.body.insertAdjacentElement("beforeend",e[1]??M()),D++,()=>{1===D&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),D--}},[]);let B=l.useCallback(e=>{let[t,...n]=O().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&j&&(j.scrollTop=0),n===r&&j&&(j.scrollTop=j.scrollHeight),n?.focus(),document.activeElement!==o))return},[O,j]),H=l.useCallback(()=>B([N,C]),[B,N,C]);l.useEffect(()=>{I&&H()},[I,H]);let{onOpenChange:z,triggerPointerDownPosRef:$}=E;l.useEffect(()=>{if(C){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-($.current?.x??0)),y:Math.abs(Math.round(t.pageY)-($.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():C.contains(n.target)||z(!1),document.removeEventListener("pointermove",t),$.current=null};return null!==$.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[C,z,$]),l.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[G,V]=n8(e=>{let t=O().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=n9(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=l.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==E.value&&E.value===t||r)&&(T(e),r&&(F.current=!0))},[E.value]),q=l.useCallback(()=>C?.focus(),[C]),U=l.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==E.value&&E.value===t||r)&&L(e)},[E.value]),X="popper"===r?nI:nO,Y=X===nI?{side:c,sideOffset:u,align:d,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:v,hideWhenDetached:g,avoidCollisions:x}:{};return(0,a.jsx)(nP,{scope:n,content:C,viewport:j,onViewportChange:R,itemRefCallback:K,selectedItem:N,onItemLeave:q,itemTextRefCallback:U,focusSelectedItem:H,selectedItemText:P,position:r,isPositioned:I,searchRef:G,children:(0,a.jsx)(ns,{as:nD,allowPinchZoom:!0,children:(0,a.jsx)(W,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:y(o,e=>{E.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,a.jsx)(A,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,a.jsx)(X,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...b,...Y,onPlaced:()=>_(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:y(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});nM.displayName="SelectContentImpl";var nO=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nx(nT,n),s=nL(nT,n),[c,u]=l.useState(null),[d,f]=l.useState(null),p=(0,w.s)(t,e=>f(e)),h=np(n),m=l.useRef(!1),v=l.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:b,focusSelectedItem:E}=s,C=l.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&d&&y&&x&&b){let e=i.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,a=e.left-i,l=e.width+a,s=Math.max(l,t.width),u=g(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.left=u+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,a=window.innerWidth-e.right-i,l=e.width+a,s=Math.max(l,t.width),u=g(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.right=u+"px"}let a=h(),l=window.innerHeight-20,s=y.scrollHeight,u=window.getComputedStyle(d),f=parseInt(u.borderTopWidth,10),p=parseInt(u.paddingTop,10),v=parseInt(u.borderBottomWidth,10),w=f+p+s+parseInt(u.paddingBottom,10)+v,E=Math.min(5*x.offsetHeight,w),C=window.getComputedStyle(y),S=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,k=x.offsetHeight/2,N=f+p+(x.offsetTop+k);if(N<=R){let e=a.length>0&&x===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(l-R,k+(e?j:0)+(d.clientHeight-y.offsetTop-y.offsetHeight)+v);c.style.height=N+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;c.style.top="0px";let t=Math.max(R,f+y.offsetTop+(e?S:0)+k);c.style.height=t+(w-N)+"px",y.scrollTop=N-R+y.offsetTop}c.style.margin="10px 0",c.style.minHeight=E+"px",c.style.maxHeight=l+"px",r?.(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,c,d,y,x,b,i.dir,r]);G(()=>C(),[C]);let[S,j]=l.useState();G(()=>{d&&j(window.getComputedStyle(d).zIndex)},[d]);let k=l.useCallback(e=>{e&&!0===v.current&&(C(),E?.(),v.current=!1)},[C,E]);return(0,a.jsx)(n_,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:m,onScrollButtonChange:k,children:(0,a.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,a.jsx)(R.sG.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nO.displayName="SelectItemAlignedPosition";var nI=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=ng(n);return(0,a.jsx)(th,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nI.displayName="SelectPopperPosition";var[n_,nW]=nm(nT,{}),nF="SelectViewport",nB=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nL(nF,n),s=nW(nF,n),c=(0,w.s)(t,i.onViewportChange),u=l.useRef(0);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,a.jsx)(nf.Slot,{scope:n,children:(0,a.jsx)(R.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:y(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){let e=Math.abs(u.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,a=Math.min(r,i),l=i-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});nB.displayName=nF;var nH="SelectGroup",[nz,n$]=nm(nH);l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=q();return(0,a.jsx)(nz,{scope:n,id:o,children:(0,a.jsx)(R.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=nH;var nG="SelectLabel";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=n$(nG,n);return(0,a.jsx)(R.sG.div,{id:o.id,...r,ref:t})}).displayName=nG;var nV="SelectItem",[nK,nq]=nm(nV),nU=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...s}=e,c=nx(nV,n),u=nL(nV,n),d=c.value===r,[f,p]=l.useState(i??""),[h,m]=l.useState(!1),v=(0,w.s)(t,e=>u.itemRefCallback?.(e,r,o)),g=q(),x=l.useRef("touch"),b=()=>{o||(c.onValueChange(r),c.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,a.jsx)(nK,{scope:n,value:r,disabled:o,textId:g,isSelected:d,onItemTextChange:l.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,a.jsx)(nf.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:(0,a.jsx)(R.sG.div,{role:"option","aria-labelledby":g,"data-highlighted":h?"":void 0,"aria-selected":d&&h,"data-state":d?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:v,onFocus:y(s.onFocus,()=>m(!0)),onBlur:y(s.onBlur,()=>m(!1)),onClick:y(s.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:y(s.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:y(s.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:y(s.onPointerMove,e=>{x.current=e.pointerType,o?u.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:y(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:y(s.onKeyDown,e=>{(u.searchRef?.current===""||" "!==e.key)&&(nu.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});nU.displayName=nV;var nX="SelectItemText",nY=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,s=nx(nX,n),c=nL(nX,n),u=nq(nX,n),d=nb(nX,n),[f,p]=l.useState(null),h=(0,w.s)(t,e=>p(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),m=f?.textContent,g=l.useMemo(()=>(0,a.jsx)("option",{value:u.value,disabled:u.disabled,children:m},u.value),[u.disabled,u.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=d;return G(()=>(y(g),()=>x(g)),[y,x,g]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(R.sG.span,{id:u.textId,...i,ref:h}),u.isSelected&&s.valueNode&&!s.valueNodeHasChildren?v.createPortal(i.children,s.valueNode):null]})});nY.displayName=nX;var nZ="SelectItemIndicator",nJ=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nq(nZ,n).isSelected?(0,a.jsx)(R.sG.span,{"aria-hidden":!0,...r,ref:t}):null});nJ.displayName=nZ;var nQ="SelectScrollUpButton",n0=l.forwardRef((e,t)=>{let n=nL(nQ,e.__scopeSelect),r=nW(nQ,e.__scopeSelect),[o,i]=l.useState(!1),s=(0,w.s)(t,r.onScrollButtonChange);return G(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,a.jsx)(n3,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});n0.displayName=nQ;var n1="SelectScrollDownButton",n2=l.forwardRef((e,t)=>{let n=nL(n1,e.__scopeSelect),r=nW(n1,e.__scopeSelect),[o,i]=l.useState(!1),s=(0,w.s)(t,r.onScrollButtonChange);return G(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,a.jsx)(n3,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});n2.displayName=n1;var n3=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nL("SelectScrollButton",n),s=l.useRef(null),c=np(n),u=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>u(),[u]),G(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,a.jsx)(R.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:y(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:y(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:y(o.onPointerLeave,()=>{u()})})});l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,a.jsx)(R.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var n4="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ng(n),i=nx(n4,n),l=nL(n4,n);return i.open&&"popper"===l.position?(0,a.jsx)(tg,{...o,...r,ref:t}):null}).displayName=n4;var n5=l.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{let o=l.useRef(null),i=(0,w.s)(r,o),s=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return l.useEffect(()=>{let e=o.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[s,t]),(0,a.jsx)(R.sG.select,{...n,style:{...tS,...n.style},ref:i,defaultValue:t})});function n6(e){return""===e||void 0===e}function n8(e){let t=k(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function n9(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,l=(r=e,o=Math.max(a,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(l=l.filter(e=>e!==n));let s=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}n5.displayName="SelectBubbleInput";let n7=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),re=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),rt=e=>{let t=re(e);return t.charAt(0).toUpperCase()+t.slice(1)},rn=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),rr=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var ro={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ri=(0,l.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:a,...s},c)=>(0,l.createElement)("svg",{ref:c,...ro,width:t,height:t,stroke:e,strokeWidth:r?24*Number(n)/Number(t):n,className:rn("lucide",o),...!i&&!rr(s)&&{"aria-hidden":"true"},...s},[...a.map(([e,t])=>(0,l.createElement)(e,t)),...Array.isArray(i)?i:[i]])),ra=(e,t)=>{let n=(0,l.forwardRef)(({className:n,...r},o)=>(0,l.createElement)(ri,{ref:o,iconNode:t,className:rn(`lucide-${n7(rt(e))}`,`lucide-${e}`,n),...r}));return n.displayName=rt(e),n},rl=ra("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),rs=ra("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),rc=ra("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var ru=n(4780);function rd({...e}){return(0,a.jsx)(nE,{"data-slot":"select",...e})}function rf({...e}){return(0,a.jsx)(nR,{"data-slot":"select-value",...e})}function rp({className:e,size:t="default",children:n,...r}){return(0,a.jsxs)(nS,{"data-slot":"select-trigger","data-size":t,className:(0,ru.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r,children:[n,(0,a.jsx)(nk,{asChild:!0,children:(0,a.jsx)(rl,{className:"size-4 opacity-50"})})]})}function rh({className:e,children:t,position:n="popper",...r}){return(0,a.jsx)(nN,{children:(0,a.jsxs)(nA,{"data-slot":"select-content",className:(0,ru.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[(0,a.jsx)(rv,{}),(0,a.jsx)(nB,{className:(0,ru.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(rg,{})]})})}function rm({className:e,children:t,...n}){return(0,a.jsxs)(nU,{"data-slot":"select-item",className:(0,ru.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...n,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(nJ,{children:(0,a.jsx)(rs,{className:"size-4"})})}),(0,a.jsx)(nY,{children:t})]})}function rv({className:e,...t}){return(0,a.jsx)(n0,{"data-slot":"select-scroll-up-button",className:(0,ru.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(rc,{className:"size-4"})})}function rg({className:e,...t}){return(0,a.jsx)(n2,{"data-slot":"select-scroll-down-button",className:(0,ru.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(rl,{className:"size-4"})})}var ry=n(6834);function rx(){let e=(0,c.useRouter)(),[t,n]=(0,l.useState)(!1),[r,o]=(0,l.useState)([]),[i,s]=(0,l.useState)({name:"",description:"",roleId:"",autoAssign:!1}),[u,v]=(0,l.useState)([]),[g,y]=(0,l.useState)({description:"",offsetDays:0,isRecurring:!1,isCritical:!1}),x=async t=>{t.preventDefault(),n(!0);try{let t={...i,roleId:parseInt(i.roleId),tasks:u},n=await fetch("/api/templates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(n.ok)e.push("/admin/templates");else{let e=await n.json();alert(`Error: ${e.message}`)}}catch{alert("An error occurred while creating the template")}finally{n(!1)}},w=e=>{let t="checkbox"===e.target.type?e.target.checked:e.target.value;s({...i,[e.target.name]:t})},b=e=>{v(u.filter((t,n)=>n!==e))},E=(e,t)=>{y({...g,[e]:t})};return(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Template"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Create a reusable task template for a specific role"})]}),(0,a.jsxs)("form",{onSubmit:x,className:"space-y-8",children:[(0,a.jsxs)(f.Zp,{children:[(0,a.jsxs)(f.aR,{children:[(0,a.jsx)(f.ZB,{children:"Template Details"}),(0,a.jsx)(f.BT,{children:"Basic information about the template"})]}),(0,a.jsxs)(f.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"name",children:"Template Name *"}),(0,a.jsx)(h.p,{id:"name",name:"name",type:"text",required:!0,value:i.name,onChange:w,placeholder:"e.g., Project Manager Checklist"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"roleId",children:"Role *"}),(0,a.jsxs)(rd,{value:i.roleId,onValueChange:e=>s({...i,roleId:e}),children:[(0,a.jsx)(rp,{children:(0,a.jsx)(rf,{placeholder:"Select a role"})}),(0,a.jsx)(rh,{children:r.map(e=>(0,a.jsx)(rm,{value:e.id.toString(),children:e.name},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"description",children:"Description"}),(0,a.jsx)("textarea",{id:"description",name:"description",value:i.description,onChange:w,placeholder:"Brief description of this template",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"autoAssign",name:"autoAssign",checked:i.autoAssign,onChange:w,className:"rounded border-gray-300"}),(0,a.jsx)(m.J,{htmlFor:"autoAssign",children:"Auto-assign when role is added to project"})]})]})]}),(0,a.jsxs)(f.Zp,{children:[(0,a.jsxs)(f.aR,{children:[(0,a.jsx)(f.ZB,{children:"Template Tasks"}),(0,a.jsx)(f.BT,{children:"Add tasks that will be created when this template is used"})]}),(0,a.jsxs)(f.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,a.jsx)("h3",{className:"font-semibold mb-4",children:"Add New Task"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)(m.J,{children:"Task Description *"}),(0,a.jsx)(h.p,{value:g.description,onChange:e=>E("description",e.target.value),placeholder:"e.g., Create project charter and scope document"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{children:"Offset Days"}),(0,a.jsx)(h.p,{type:"number",value:g.offsetDays,onChange:e=>E("offsetDays",parseInt(e.target.value)||0),placeholder:"0"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Days from project start (negative for pre-start)"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:g.isCritical,onChange:e=>E("isCritical",e.target.checked),className:"rounded border-gray-300"}),(0,a.jsx)(m.J,{children:"Critical Task"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:g.isRecurring,onChange:e=>E("isRecurring",e.target.checked),className:"rounded border-gray-300"}),(0,a.jsx)(m.J,{children:"Recurring Task"})]})]})]}),(0,a.jsx)(p.$,{type:"button",onClick:()=>{g.description.trim()&&(v([...u,{...g}]),y({description:"",offsetDays:0,isRecurring:!1,isCritical:!1}))},className:"mt-4",disabled:!g.description.trim(),children:"Add Task"})]}),u.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold mb-4",children:["Template Tasks (",u.length,")"]}),(0,a.jsx)("div",{className:"space-y-2",children:u.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.description}),(0,a.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,a.jsxs)(ry.E,{variant:"outline",children:[e.offsetDays>=0?`T+${e.offsetDays}`:`T${e.offsetDays}`," days"]}),e.isCritical&&(0,a.jsx)(ry.E,{variant:"destructive",children:"Critical"}),e.isRecurring&&(0,a.jsx)(ry.E,{variant:"secondary",children:"Recurring"})]})]}),(0,a.jsx)(p.$,{type:"button",variant:"outline",size:"sm",onClick:()=>b(t),children:"Remove"})]},t))})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(p.$,{type:"submit",disabled:t||!i.roleId||0===u.length,children:t?"Creating...":"Create Template"}),(0,a.jsx)(d(),{href:"/admin/templates",children:(0,a.jsx)(p.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3326:(e,t,n)=>{Promise.resolve().then(n.bind(n,932))},3873:e=>{"use strict";e.exports=require("path")},4163:(e,t,n)=>{"use strict";n.d(t,{hO:()=>s,sG:()=>l});var r=n(3210),o=n(1215),i=n(8730),a=n(687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},4300:(e,t,n)=>{"use strict";n.d(t,{J:()=>s});var r=n(687),o=n(3210),i=n(4163),a=o.forwardRef((e,t)=>(0,r.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=n(4780);function s({className:e,...t}){return(0,r.jsx)(a,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},6189:(e,t,n)=>{"use strict";var r=n(5773);n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},7011:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c});var r=n(5239),o=n(8088),i=n(8170),a=n.n(i),l=n(893),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);n.d(t,s);let c={children:["",{children:["admin",{children:["templates",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,161)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\new\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\new\\page.tsx"],d={require:n,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/templates/new/page",pathname:"/admin/templates/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9667:(e,t,n)=>{"use strict";n.d(t,{p:()=>i});var r=n(687);n(3210);var o=n(4780);function i({className:e,type:t,...n}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...n})}},9766:(e,t,n)=>{Promise.resolve().then(n.bind(n,161))}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,982,658,287],()=>n(7011));module.exports=r})();