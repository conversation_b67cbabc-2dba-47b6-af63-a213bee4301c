(()=>{var e={};e.id=843,e.ids=[843],e.modules={161:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\app\\\\admin\\\\templates\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\new\\page.tsx","default")},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},860:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(687),a=s(3210),i=s(6189),n=s(5814),l=s.n(n),o=s(4493),d=s(9523),c=s(9667),p=s(4300),u=s(5079),m=s(6834);function x(){let e=(0,i.useRouter)(),[t,s]=(0,a.useState)(!1),[n,x]=(0,a.useState)([]),[h,f]=(0,a.useState)({name:"",description:"",roleId:"",autoAssign:!1}),[g,v]=(0,a.useState)([]),[b,j]=(0,a.useState)({description:"",offsetDays:0,isRecurring:!1,isCritical:!1}),y=async t=>{t.preventDefault(),s(!0);try{let t={...h,roleId:parseInt(h.roleId),tasks:g},s=await fetch("/api/templates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(s.ok)e.push("/admin/templates");else{let e=await s.json();alert(`Error: ${e.message}`)}}catch{alert("An error occurred while creating the template")}finally{s(!1)}},w=e=>{let t="checkbox"===e.target.type?e.target.checked:e.target.value;f({...h,[e.target.name]:t})},N=e=>{v(g.filter((t,s)=>s!==e))},k=(e,t)=>{j({...b,[e]:t})};return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Template"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Create a reusable task template for a specific role"})]}),(0,r.jsxs)("form",{onSubmit:y,className:"space-y-8",children:[(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Template Details"}),(0,r.jsx)(o.BT,{children:"Basic information about the template"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p.J,{htmlFor:"name",children:"Template Name *"}),(0,r.jsx)(c.p,{id:"name",name:"name",type:"text",required:!0,value:h.name,onChange:w,placeholder:"e.g., Project Manager Checklist"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p.J,{htmlFor:"roleId",children:"Role *"}),(0,r.jsxs)(u.l6,{value:h.roleId,onValueChange:e=>f({...h,roleId:e}),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select a role"})}),(0,r.jsx)(u.gC,{children:n.map(e=>(0,r.jsx)(u.eb,{value:e.id.toString(),children:e.name},e.id))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(p.J,{htmlFor:"description",children:"Description"}),(0,r.jsx)("textarea",{id:"description",name:"description",value:h.description,onChange:w,placeholder:"Brief description of this template",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"autoAssign",name:"autoAssign",checked:h.autoAssign,onChange:w,className:"rounded border-gray-300"}),(0,r.jsx)(p.J,{htmlFor:"autoAssign",children:"Auto-assign when role is added to project"})]})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Template Tasks"}),(0,r.jsx)(o.BT,{children:"Add tasks that will be created when this template is used"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,r.jsx)("h3",{className:"font-semibold mb-4",children:"Add New Task"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)(p.J,{children:"Task Description *"}),(0,r.jsx)(c.p,{value:b.description,onChange:e=>k("description",e.target.value),placeholder:"e.g., Create project charter and scope document"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(p.J,{children:"Offset Days"}),(0,r.jsx)(c.p,{type:"number",value:b.offsetDays,onChange:e=>k("offsetDays",parseInt(e.target.value)||0),placeholder:"0"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Days from project start (negative for pre-start)"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:b.isCritical,onChange:e=>k("isCritical",e.target.checked),className:"rounded border-gray-300"}),(0,r.jsx)(p.J,{children:"Critical Task"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:b.isRecurring,onChange:e=>k("isRecurring",e.target.checked),className:"rounded border-gray-300"}),(0,r.jsx)(p.J,{children:"Recurring Task"})]})]})]}),(0,r.jsx)(d.$,{type:"button",onClick:()=>{b.description.trim()&&(v([...g,{...b}]),j({description:"",offsetDays:0,isRecurring:!1,isCritical:!1}))},className:"mt-4",disabled:!b.description.trim(),children:"Add Task"})]}),g.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-semibold mb-4",children:["Template Tasks (",g.length,")"]}),(0,r.jsx)("div",{className:"space-y-2",children:g.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"font-medium",children:e.description}),(0,r.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,r.jsxs)(m.E,{variant:"outline",children:[e.offsetDays>=0?`T+${e.offsetDays}`:`T${e.offsetDays}`," days"]}),e.isCritical&&(0,r.jsx)(m.E,{variant:"destructive",children:"Critical"}),e.isRecurring&&(0,r.jsx)(m.E,{variant:"secondary",children:"Recurring"})]})]}),(0,r.jsx)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>N(t),children:"Remove"})]},t))})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(d.$,{type:"submit",disabled:t||!h.roleId||0===g.length,children:t?"Creating...":"Create Template"}),(0,r.jsx)(l(),{href:"/admin/templates",children:(0,r.jsx)(d.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3326:(e,t,s)=>{Promise.resolve().then(s.bind(s,860))},3873:e=>{"use strict";e.exports=require("path")},4300:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var r=s(687),a=s(3210),i=s(4163),n=a.forwardRef((e,t)=>(0,r.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=s(4780);function o({className:e,...t}){return(0,r.jsx)(n,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},5079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>p,eb:()=>m,gC:()=>u,l6:()=>d,yv:()=>c});var r=s(687);s(3210);var a=s(6725),i=s(5891),n=s(3964),l=s(3589),o=s(4780);function d({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function p({className:e,size:t="default",children:s,...n}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:t,position:s="popper",...i}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,r.jsx)(x,{}),(0,r.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(h,{})]})})}function m({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"size-4"})})}function h({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"size-4"})})}},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},7011:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["templates",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,161)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\new\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\templates\\new\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/templates/new/page",pathname:"/admin/templates/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(687);s(3210);var a=s(4780);function i({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},9766:(e,t,s)=>{Promise.resolve().then(s.bind(s,161))}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,982,277,425,287],()=>s(7011));module.exports=r})();