{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "YUxBwTbl+HGgvIs2cD1IW84O3aXeB9xjiAAyOnC946M=", "__NEXT_PREVIEW_MODE_ID": "a63f9e195f83bb20f959520581931d17", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fac0a511b1793b9ae1b462ed21581ccb2d7a0dc0297e14ec468aa94eca0519e4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3556af46a4f1b8602182167343a015a9bbb4c7ad53d05cd928b18027957494b7"}}}, "sortedMiddleware": ["/"], "functions": {}}