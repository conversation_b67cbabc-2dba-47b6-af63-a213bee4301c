1:"$Sreact.fragment"
2:I[4541,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","177","static/chunks/app/layout-080e4baaf1003ef7.js"],"AuthProvider"]
3:I[5506,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","177","static/chunks/app/layout-080e4baaf1003ef7.js"],"default"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[2103,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","177","static/chunks/app/layout-080e4baaf1003ef7.js"],"default"]
7:I[9228,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","177","static/chunks/app/layout-080e4baaf1003ef7.js"],"default"]
9:I[9665,[],"MetadataBoundary"]
b:I[9665,[],"OutletBoundary"]
e:I[4911,[],"AsyncMetadataOutlet"]
10:I[9665,[],"ViewportBoundary"]
12:I[6614,[],""]
:HL["/_next/static/css/45cb61c7b2c6283b.css","style"]
0:{"P":null,"b":"r6FI7KjMv__46R3dlvPEd","p":"","c":["","admin","templates"],"i":false,"f":[[["",{"children":["admin",{"children":["templates",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/45cb61c7b2c6283b.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}],["$","meta",null,{"name":"theme-color","content":"#3b82f6"}],["$","meta",null,{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta",null,{"name":"apple-mobile-web-app-title","content":"Rollout Ready"}],["$","meta",null,{"name":"mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"msapplication-TileColor","content":"#3b82f6"}],["$","meta",null,{"name":"msapplication-tap-highlight","content":"no"}],["$","link",null,{"rel":"icon","href":"/icons/icon-192x192.svg"}],["$","link",null,{"rel":"apple-touch-icon","href":"/icons/icon-192x192.svg"}],["$","link",null,{"rel":"manifest","href":"/manifest.json"}]]}],["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased min-h-screen bg-background","children":["$","$L2",null,{"children":[["$","$L3",null,{}],["$","main",null,{"className":"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}],["$","$L7",null,{}]]}]}]]}]]}],{"children":["admin",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["templates",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":["$L8",["$","$L9",null,{"children":"$La"}],null,["$","$Lb",null,{"children":["$Lc","$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","WohuD9pDKXza4yWrGfWIA",{"children":[["$","$L10",null,{"children":"$L11"}],null]}],null]}],false]],"m":"$undefined","G":["$12","$undefined"],"s":false,"S":true}
13:"$Sreact.suspense"
14:I[4911,[],"AsyncMetadata"]
a:["$","$13",null,{"fallback":null,"children":["$","$L14",null,{"promise":"$@15"}]}]
d:null
11:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
15:{"metadata":[["$","title","0",{"children":"Rollout Ready"}],["$","meta","1",{"name":"description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","link","2",{"rel":"manifest","href":"/manifest.json","crossOrigin":"$undefined"}],["$","meta","3",{"name":"format-detection","content":"telephone=no"}],["$","meta","4",{"name":"mobile-web-app-capable","content":"yes"}],["$","meta","5",{"name":"apple-mobile-web-app-title","content":"Rollout Ready"}],["$","meta","6",{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta","7",{"property":"og:title","content":"Rollout Ready"}],["$","meta","8",{"property":"og:description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","meta","9",{"property":"og:site_name","content":"Rollout Ready"}],["$","meta","10",{"property":"og:type","content":"website"}],["$","meta","11",{"name":"twitter:card","content":"summary"}],["$","meta","12",{"name":"twitter:title","content":"Rollout Ready"}],["$","meta","13",{"name":"twitter:description","content":"Role-based checklist and task management system for large-scale implementation projects"}],["$","link","14",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
f:{"metadata":"$15:metadata","error":null,"digest":"$undefined"}
16:I[6874,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","317","static/chunks/app/admin/templates/page-0a447f675afc5398.js"],""]
17:I[5127,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","317","static/chunks/app/admin/templates/page-0a447f675afc5398.js"],"Table"]
18:I[5127,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","317","static/chunks/app/admin/templates/page-0a447f675afc5398.js"],"TableHeader"]
19:I[5127,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","317","static/chunks/app/admin/templates/page-0a447f675afc5398.js"],"TableRow"]
1a:I[5127,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","317","static/chunks/app/admin/templates/page-0a447f675afc5398.js"],"TableHead"]
1b:I[5127,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","317","static/chunks/app/admin/templates/page-0a447f675afc5398.js"],"TableBody"]
1c:I[5127,["874","static/chunks/874-09cd528922313eb8.js","277","static/chunks/277-9852fd43da8910cd.js","317","static/chunks/app/admin/templates/page-0a447f675afc5398.js"],"TableCell"]
8:["$","div",null,{"className":"space-y-8","children":[["$","div",null,{"className":"flex justify-between items-center","children":[["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"Manage Templates"}],["$","p",null,{"className":"text-gray-600 mt-2","children":"Create and manage task templates for different roles"}]]}],["$","$L16",null,{"href":"/admin/templates/new","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3","children":"Create New Template"}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"All Templates"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Overview of all task templates in the system"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","$L17",null,{"children":[["$","$L18",null,{"children":["$","$L19",null,{"children":[["$","$L1a",null,{"children":"Template Name"}],["$","$L1a",null,{"children":"Role"}],["$","$L1a",null,{"children":"Description"}],["$","$L1a",null,{"children":"Tasks"}],["$","$L1a",null,{"children":"Auto-Assign"}],["$","$L1a",null,{"children":"Actions"}]]}]}],["$","$L1b",null,{"children":[["$","$L19","2",{"children":[["$","$L1c",null,{"className":"font-medium","children":"Infrastructure Setup Checklist"}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":"Infrastructure Lead"}]}],["$","$L1c",null,{"children":"Technical infrastructure preparation tasks"}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":[4," task","s"]}]}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-primary/90 bg-green-100 text-green-800 border-green-200","children":"Auto"}]}],["$","$L1c",null,{"children":["$","div",null,{"className":"flex gap-2","children":[["$","$L16",null,{"href":"/admin/templates/2","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}],["$","$L16",null,{"href":"/admin/templates/2/edit","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"Edit"}]}]]}]}]]}],["$","$L19","1",{"children":[["$","$L1c",null,{"className":"font-medium","children":"Project Manager Checklist"}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":"Project Manager"}]}],["$","$L1c",null,{"children":"Standard tasks for project managers"}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":[4," task","s"]}]}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-primary/90 bg-green-100 text-green-800 border-green-200","children":"Auto"}]}],["$","$L1c",null,{"children":["$","div",null,{"className":"flex gap-2","children":[["$","$L16",null,{"href":"/admin/templates/1","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}],["$","$L16",null,{"href":"/admin/templates/1/edit","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"Edit"}]}]]}]}]]}],["$","$L19","3",{"children":[["$","$L1c",null,{"className":"font-medium","children":"Security Assessment Checklist"}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":"Security Architect"}]}],["$","$L1c",null,{"children":"Security review and implementation tasks"}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":[4," task","s"]}]}],["$","$L1c",null,{"children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-primary/90 bg-green-100 text-green-800 border-green-200","children":"Auto"}]}],["$","$L1c",null,{"children":["$","div",null,{"className":"flex gap-2","children":[["$","$L16",null,{"href":"/admin/templates/3","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}],["$","$L16",null,{"href":"/admin/templates/3/edit","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"Edit"}]}]]}]}]]}]]}]]}]}]]}],["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-3 gap-6","children":[["$","div","Infrastructure Lead",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Infrastructure Lead"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":[1," template",""]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"space-y-2","children":[["$","div","2",{"className":"flex justify-between items-center p-2 border rounded","children":[["$","div",null,{"children":[["$","p",null,{"className":"font-medium text-sm","children":"Infrastructure Setup Checklist"}],["$","p",null,{"className":"text-xs text-gray-500","children":[4," tasks"," • Auto-assign"]}]]}],["$","$L16",null,{"href":"/admin/templates/2","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}]]}]]}]}]]}],["$","div","Project Manager",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Project Manager"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":[1," template",""]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"space-y-2","children":[["$","div","1",{"className":"flex justify-between items-center p-2 border rounded","children":[["$","div",null,{"children":[["$","p",null,{"className":"font-medium text-sm","children":"Project Manager Checklist"}],["$","p",null,{"className":"text-xs text-gray-500","children":[4," tasks"," • Auto-assign"]}]]}],["$","$L16",null,{"href":"/admin/templates/1","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}]]}]]}]}]]}],["$","div","Security Architect",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Security Architect"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":[1," template",""]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"space-y-2","children":[["$","div","3",{"className":"flex justify-between items-center p-2 border rounded","children":[["$","div",null,{"children":[["$","p",null,{"className":"font-medium text-sm","children":"Security Assessment Checklist"}],["$","p",null,{"className":"text-xs text-gray-500","children":[4," tasks"," • Auto-assign"]}]]}],["$","$L16",null,{"href":"/admin/templates/3","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5","children":"View"}]}]]}]]}]}]]}]]}],["$","div",null,{"className":"grid md:grid-cols-4 gap-6","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Total Templates"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-3xl font-bold","children":3}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Total Tasks"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-3xl font-bold","children":12}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Auto-Assign"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-3xl font-bold text-green-600","children":3}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Manual Assign"}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"text-3xl font-bold text-blue-600","children":0}]}]]}]]}]]}]
