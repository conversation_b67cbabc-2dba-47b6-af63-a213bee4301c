# Rollout Ready - Unified Deployment System
# PowerShell Version

function Show-Header {
    Clear-Host
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "   ROLLOUT READY - DEPLOYMENT CENTER" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Unified Start/Deploy Button" -ForegroundColor Yellow
    Write-Host ""
}

function Show-Menu {
    Write-Host "Select deployment mode:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "[1] Development Mode (Hot Reload)" -ForegroundColor White
    Write-Host "[2] Production Mode (Optimized)" -ForegroundColor White
    Write-Host "[3] Build Only (No Server)" -ForegroundColor White
    Write-Host "[4] Database Setup (First Time)" -ForegroundColor White
    Write-Host "[5] Full Reset (Clean + Setup)" -ForegroundColor White
    Write-Host "[6] Exit" -ForegroundColor White
    Write-Host ""
}

function Start-Development {
    Write-Host ""
    Write-Host "🔧 Starting Development Mode..." -ForegroundColor Yellow
    Write-Host "================================" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "Installing dependencies..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Setting up database..." -ForegroundColor Cyan
    npx prisma db push
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Database setup failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Seeding database..." -ForegroundColor Cyan
    npm run seed
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ Database seeding failed, continuing anyway..." -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "🚀 Starting development server..." -ForegroundColor Green
    Write-Host ""
    Write-Host "✅ Server will be available at: http://localhost:3000" -ForegroundColor Green
    Write-Host "💡 Press Ctrl+C to stop the server" -ForegroundColor Yellow
    Write-Host ""
    
    npm run dev
}

function Start-Production {
    Write-Host ""
    Write-Host "🏭 Starting Production Mode..." -ForegroundColor Yellow
    Write-Host "===============================" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "Installing dependencies..." -ForegroundColor Cyan
    npm install --production
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Building application..." -ForegroundColor Cyan
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Setting up database..." -ForegroundColor Cyan
    npx prisma db push
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Database setup failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Seeding database..." -ForegroundColor Cyan
    npm run seed
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ Database seeding failed, continuing anyway..." -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "🚀 Starting production server..." -ForegroundColor Green
    Write-Host ""
    Write-Host "✅ Server will be available at: http://localhost:3000" -ForegroundColor Green
    Write-Host "💡 Press Ctrl+C to stop the server" -ForegroundColor Yellow
    Write-Host ""
    
    npm start
}

function Start-BuildOnly {
    Write-Host ""
    Write-Host "🔨 Build Only Mode..." -ForegroundColor Yellow
    Write-Host "====================" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "Installing dependencies..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Building application..." -ForegroundColor Cyan
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
    Write-Host "📁 Built files are in the .next directory" -ForegroundColor Cyan
    Write-Host ""
    Read-Host "Press Enter to continue"
}

function Start-DatabaseSetup {
    Write-Host ""
    Write-Host "🛠️ Database Setup Mode..." -ForegroundColor Yellow
    Write-Host "=========================" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "Installing dependencies..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Setting up database schema..." -ForegroundColor Cyan
    npx prisma db push
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Database setup failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Generating Prisma client..." -ForegroundColor Cyan
    npx prisma generate
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Prisma client generation failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Seeding database with initial data..." -ForegroundColor Cyan
    npm run seed
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Database seeding failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "✅ Database setup completed successfully!" -ForegroundColor Green
    Write-Host "📊 Demo users and data have been created" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Default Admin Login:" -ForegroundColor Yellow
    Write-Host "Username: admin" -ForegroundColor White
    Write-Host "Password: admin123" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to continue"
}

function Start-FullReset {
    Write-Host ""
    Write-Host "🔄 Full Reset Mode..." -ForegroundColor Yellow
    Write-Host "====================" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "⚠️  WARNING: This will delete all data!" -ForegroundColor Red
    Write-Host ""
    
    $confirm = Read-Host "Are you sure? Type 'YES' to continue"
    if ($confirm -ne "YES") {
        Write-Host "Reset cancelled." -ForegroundColor Yellow
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Cleaning node_modules..." -ForegroundColor Cyan
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules"
    }

    Write-Host "Cleaning build files..." -ForegroundColor Cyan
    if (Test-Path ".next") {
        Remove-Item -Recurse -Force ".next"
    }
    if (Test-Path "dist") {
        Remove-Item -Recurse -Force "dist"
    }

    Write-Host "Cleaning database..." -ForegroundColor Cyan
    if (Test-Path "prisma/dev.db") {
        Remove-Item -Force "prisma/dev.db"
    }

    Write-Host ""
    Write-Host "Installing fresh dependencies..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Setting up fresh database..." -ForegroundColor Cyan
    npx prisma db push
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Database setup failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "Seeding fresh database..." -ForegroundColor Cyan
    npm run seed
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Database seeding failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        return
    }

    Write-Host ""
    Write-Host "✅ Full reset completed successfully!" -ForegroundColor Green
    Write-Host "🎉 System is ready for fresh start" -ForegroundColor Cyan
    Write-Host ""
    Read-Host "Press Enter to continue"
}

# Main execution loop
do {
    Show-Header
    Show-Menu
    
    $choice = Read-Host "Enter your choice (1-6)"
    
    switch ($choice) {
        "1" { Start-Development }
        "2" { Start-Production }
        "3" { Start-BuildOnly }
        "4" { Start-DatabaseSetup }
        "5" { Start-FullReset }
        "6" { 
            Write-Host ""
            Write-Host "👋 Goodbye!" -ForegroundColor Green
            Write-Host ""
            exit 0
        }
        default {
            Write-Host ""
            Write-Host "❌ Invalid choice. Please select 1-6." -ForegroundColor Red
            Write-Host ""
            Read-Host "Press Enter to continue"
        }
    }
    
    if ($choice -in @("1", "2")) {
        Write-Host ""
        Write-Host "🛑 Server stopped." -ForegroundColor Yellow
        Write-Host ""
        Read-Host "Press Enter to return to menu"
    }
    
} while ($true)
