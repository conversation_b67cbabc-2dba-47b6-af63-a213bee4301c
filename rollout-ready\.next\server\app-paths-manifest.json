{"/_not-found/page": "app/_not-found/page.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/attachments/[id]/route": "app/api/attachments/[id]/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/tasks/[id]/attachments/route": "app/api/tasks/[id]/attachments/route.js", "/api/projects/[id]/route": "app/api/projects/[id]/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/roles/[id]/route": "app/api/roles/[id]/route.js", "/api/tasks/user/[username]/route": "app/api/tasks/user/[username]/route.js", "/api/roles/route": "app/api/roles/route.js", "/api/templates/[id]/route": "app/api/templates/[id]/route.js", "/api/tasks/[id]/route": "app/api/tasks/[id]/route.js", "/api/users/[id]/reset-password/route": "app/api/users/[id]/reset-password/route.js", "/api/users/route": "app/api/users/route.js", "/api/templates/route": "app/api/templates/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/admin/projects/[id]/page": "app/admin/projects/[id]/page.js", "/admin/projects/[id]/edit/page": "app/admin/projects/[id]/edit/page.js", "/admin/page": "app/admin/page.js", "/admin/roles/[id]/edit/page": "app/admin/roles/[id]/edit/page.js", "/admin/projects/new/page": "app/admin/projects/new/page.js", "/admin/roles/page": "app/admin/roles/page.js", "/admin/roles/[id]/page": "app/admin/roles/[id]/page.js", "/admin/templates/page": "app/admin/templates/page.js", "/admin/roles/new/page": "app/admin/roles/new/page.js", "/admin/templates/[id]/page": "app/admin/templates/[id]/page.js", "/admin/users/[id]/page": "app/admin/users/[id]/page.js", "/admin/users/new/page": "app/admin/users/new/page.js", "/admin/templates/new/page": "app/admin/templates/new/page.js", "/admin/users/[id]/edit/page": "app/admin/users/[id]/edit/page.js", "/admin/users/page": "app/admin/users/page.js", "/dashboard/page": "app/dashboard/page.js", "/login/page": "app/login/page.js", "/dashboard/[user]/page": "app/dashboard/[user]/page.js", "/page": "app/page.js", "/register/page": "app/register/page.js", "/tasks/[id]/page": "app/tasks/[id]/page.js"}