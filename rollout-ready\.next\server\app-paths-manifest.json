{"/_not-found/page": "app/_not-found/page.js", "/admin/page": "app/admin/page.js", "/admin/projects/[id]/page": "app/admin/projects/[id]/page.js", "/admin/projects/new/page": "app/admin/projects/new/page.js", "/admin/roles/new/page": "app/admin/roles/new/page.js", "/admin/roles/page": "app/admin/roles/page.js", "/admin/templates/[id]/page": "app/admin/templates/[id]/page.js", "/admin/templates/new/page": "app/admin/templates/new/page.js", "/admin/templates/page": "app/admin/templates/page.js", "/admin/users/[id]/edit/page": "app/admin/users/[id]/edit/page.js", "/admin/users/[id]/page": "app/admin/users/[id]/page.js", "/admin/users/new/page": "app/admin/users/new/page.js", "/admin/users/page": "app/admin/users/page.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/roles/route": "app/api/roles/route.js", "/api/templates/route": "app/api/templates/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/api/users/route": "app/api/users/route.js", "/dashboard/[user]/page": "app/dashboard/[user]/page.js", "/login/page": "app/login/page.js", "/page": "app/page.js"}