(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[43],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...d})}},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(5155);s(2115);var r=s(9434);function n(e){let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i});var a=s(5155);s(2115);var r=s(9434);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},8306:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,8583))},8583:(e,t,s)=>{"use strict";s.d(t,{default:()=>v});var a=s(5155),r=s(2115),n=s(6695),i=s(285),l=s(9409),o=s(9434);let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...r})});d.displayName="Textarea";var c=s(2523),u=s(8979),p=s(7565);let m=(0,p.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),x=(0,p.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),f=(0,p.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),h=(0,p.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var g=s(5695);function v(e){let{task:t}=e,s=(0,g.useRouter)(),[o,p]=(0,r.useState)(!1),[v,b]=(0,r.useState)(!1),[j,y]=(0,r.useState)(t.status),[w,N]=(0,r.useState)(t.comments||""),[k,S]=(0,r.useState)(t.timeSpentMinutes?Math.floor(t.timeSpentMinutes/60).toString():""),[z,_]=(0,r.useState)(t.timeSpentMinutes?(t.timeSpentMinutes%60).toString():""),[M,C]=(0,r.useState)(null),P=async()=>{p(!0);try{let e=60*(parseInt(k)||0)+(parseInt(z)||0),a=await fetch("/api/tasks/".concat(t.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:j,comments:w.trim()||null,timeSpentMinutes:e>0?e:null,completedAt:"DONE"===j?new Date().toISOString():null})});if(a.ok)s.refresh();else{let e=await a.json();alert("Error: ".concat(e.message))}}catch(e){alert("An error occurred while updating the task")}finally{p(!1)}},A=async()=>{if(M){b(!0);try{let e=new FormData;e.append("file",M),e.append("uploadedBy",t.projectRole.user.username);let a=await fetch("/api/tasks/".concat(t.id,"/attachments"),{method:"POST",body:e});if(a.ok){C(null);let e=document.getElementById("file-upload");e&&(e.value=""),s.refresh()}else{let e=await a.json();alert("Error: ".concat(e.message))}}catch(e){alert("An error occurred while uploading the file")}finally{b(!1)}}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m,{className:"h-5 w-5"}),"Update Task"]}),(0,a.jsx)(n.BT,{children:"Change task status, add comments, and record time spent"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"status",children:"Task Status"}),(0,a.jsxs)(l.l6,{value:j,onValueChange:y,children:[(0,a.jsx)(l.bq,{children:(0,a.jsx)(l.yv,{placeholder:"Select status"})}),(0,a.jsxs)(l.gC,{children:[(0,a.jsx)(l.eb,{value:"TODO",children:"To Do"}),(0,a.jsx)(l.eb,{value:"IN_PROGRESS",children:"In Progress"}),(0,a.jsx)(l.eb,{value:"DONE",children:"Done"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(u.J,{className:"flex items-center gap-2",children:[(0,a.jsx)(x,{className:"h-4 w-4"}),"Time Spent"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(c.p,{type:"number",placeholder:"Hours",value:k,onChange:e=>S(e.target.value),min:"0",max:"999"}),(0,a.jsx)(u.J,{className:"text-xs text-gray-500 mt-1",children:"Hours"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(c.p,{type:"number",placeholder:"Minutes",value:z,onChange:e=>_(e.target.value),min:"0",max:"59"}),(0,a.jsx)(u.J,{className:"text-xs text-gray-500 mt-1",children:"Minutes"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(u.J,{htmlFor:"comments",className:"flex items-center gap-2",children:[(0,a.jsx)(f,{className:"h-4 w-4"}),"Comments / Notes"]}),(0,a.jsx)(d,{id:"comments",placeholder:"Add any comments, notes, or updates about this task...",value:w,onChange:e=>N(e.target.value),rows:4})]}),(0,a.jsx)(i.$,{onClick:P,disabled:o,className:"w-full",children:o?"Updating...":"Update Task"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(h,{className:"h-5 w-5"}),"Upload Attachment"]}),(0,a.jsx)(n.BT,{children:"Upload files as proof of completion or supporting documents"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"file-upload",children:"Select File"}),(0,a.jsx)(c.p,{id:"file-upload",type:"file",onChange:e=>{var t;return C((null==(t=e.target.files)?void 0:t[0])||null)},accept:".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt,.xlsx,.xls,.ppt,.pptx"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Supported formats: PDF, Word, Excel, PowerPoint, Images, Text files (Max 10MB)"})]}),M&&(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Selected file:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[M.name," (",(M.size/1024).toFixed(1)," KB)"]})]}),(0,a.jsx)(i.$,{onClick:A,disabled:!M||v,className:"w-full",variant:"outline",children:v?"Uploading...":"Upload File"})]})]})]})}},8979:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var a=s(5155),r=s(2115),n=s(3655),i=r.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=s(9434);function o(e){let{className:t,...s}=e;return(0,a.jsx)(i,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},9409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>d,yv:()=>c});var a=s(5155);s(2115);var r=s(1396),n=s(6474),i=s(5196),l=s(7863),o=s(9434);function d(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:i,...l}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[i,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:s,position:n="popper",...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(f,{})]})})}function m(e){let{className:t,children:s,...n}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function x(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(l.A,{className:"size-4"})})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(n.A,{className:"size-4"})})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[277,874,537,441,684,358],()=>t(8306)),_N_E=e.O()}]);