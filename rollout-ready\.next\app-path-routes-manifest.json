{"/_not-found/page": "/_not-found", "/api/attachments/[id]/route": "/api/attachments/[id]", "/api/auth/me/route": "/api/auth/me", "/api/auth/login/route": "/api/auth/login", "/api/auth/logout/route": "/api/auth/logout", "/api/projects/route": "/api/projects", "/api/projects/[id]/route": "/api/projects/[id]", "/api/roles/[id]/route": "/api/roles/[id]", "/api/users/route": "/api/users", "/api/tasks/[id]/route": "/api/tasks/[id]", "/api/roles/route": "/api/roles", "/api/tasks/[id]/attachments/route": "/api/tasks/[id]/attachments", "/api/tasks/user/[username]/route": "/api/tasks/user/[username]", "/api/templates/route": "/api/templates", "/api/users/[id]/route": "/api/users/[id]", "/favicon.ico/route": "/favicon.ico", "/admin/page": "/admin", "/admin/projects/[id]/edit/page": "/admin/projects/[id]/edit", "/admin/projects/[id]/page": "/admin/projects/[id]", "/admin/projects/new/page": "/admin/projects/new", "/admin/roles/[id]/page": "/admin/roles/[id]", "/admin/roles/new/page": "/admin/roles/new", "/admin/roles/[id]/edit/page": "/admin/roles/[id]/edit", "/admin/roles/page": "/admin/roles", "/admin/users/new/page": "/admin/users/new", "/admin/templates/[id]/page": "/admin/templates/[id]", "/admin/templates/new/page": "/admin/templates/new", "/admin/templates/page": "/admin/templates", "/admin/users/page": "/admin/users", "/tasks/[id]/page": "/tasks/[id]", "/page": "/", "/login/page": "/login", "/dashboard/[user]/page": "/dashboard/[user]"}