{"/_not-found/page": "/_not-found", "/api/auth/login/route": "/api/auth/login", "/api/auth/logout/route": "/api/auth/logout", "/api/auth/me/route": "/api/auth/me", "/api/tasks/[id]/route": "/api/tasks/[id]", "/api/auth/register/route": "/api/auth/register", "/api/projects/[id]/route": "/api/projects/[id]", "/api/projects/route": "/api/projects", "/api/roles/route": "/api/roles", "/api/roles/[id]/route": "/api/roles/[id]", "/api/templates/route": "/api/templates", "/api/tasks/[id]/attachments/route": "/api/tasks/[id]/attachments", "/api/attachments/[id]/route": "/api/attachments/[id]", "/api/tasks/user/[username]/route": "/api/tasks/user/[username]", "/api/users/[id]/route": "/api/users/[id]", "/api/users/[id]/reset-password/route": "/api/users/[id]/reset-password", "/favicon.ico/route": "/favicon.ico", "/api/users/route": "/api/users", "/api/templates/[id]/route": "/api/templates/[id]", "/admin/projects/[id]/page": "/admin/projects/[id]", "/admin/page": "/admin", "/admin/projects/[id]/edit/page": "/admin/projects/[id]/edit", "/admin/roles/[id]/edit/page": "/admin/roles/[id]/edit", "/admin/roles/new/page": "/admin/roles/new", "/admin/projects/new/page": "/admin/projects/new", "/admin/roles/[id]/page": "/admin/roles/[id]", "/admin/roles/page": "/admin/roles", "/admin/templates/[id]/page": "/admin/templates/[id]", "/admin/templates/page": "/admin/templates", "/admin/templates/new/page": "/admin/templates/new", "/admin/users/new/page": "/admin/users/new", "/admin/users/page": "/admin/users", "/admin/users/[id]/edit/page": "/admin/users/[id]/edit", "/admin/users/[id]/page": "/admin/users/[id]", "/dashboard/page": "/dashboard", "/dashboard/[user]/page": "/dashboard/[user]", "/login/page": "/login", "/page": "/", "/tasks/[id]/page": "/tasks/[id]", "/register/page": "/register"}