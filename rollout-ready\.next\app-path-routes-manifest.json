{"/_not-found/page": "/_not-found", "/api/auth/logout/route": "/api/auth/logout", "/api/attachments/[id]/route": "/api/attachments/[id]", "/api/auth/login/route": "/api/auth/login", "/api/auth/register/route": "/api/auth/register", "/api/auth/me/route": "/api/auth/me", "/api/tasks/[id]/attachments/route": "/api/tasks/[id]/attachments", "/api/projects/[id]/route": "/api/projects/[id]", "/api/projects/route": "/api/projects", "/api/roles/[id]/route": "/api/roles/[id]", "/api/tasks/user/[username]/route": "/api/tasks/user/[username]", "/api/roles/route": "/api/roles", "/api/templates/[id]/route": "/api/templates/[id]", "/api/tasks/[id]/route": "/api/tasks/[id]", "/api/users/[id]/reset-password/route": "/api/users/[id]/reset-password", "/api/users/route": "/api/users", "/api/templates/route": "/api/templates", "/api/users/[id]/route": "/api/users/[id]", "/favicon.ico/route": "/favicon.ico", "/admin/projects/[id]/page": "/admin/projects/[id]", "/admin/projects/[id]/edit/page": "/admin/projects/[id]/edit", "/admin/page": "/admin", "/admin/roles/[id]/edit/page": "/admin/roles/[id]/edit", "/admin/projects/new/page": "/admin/projects/new", "/admin/roles/page": "/admin/roles", "/admin/roles/[id]/page": "/admin/roles/[id]", "/admin/templates/page": "/admin/templates", "/admin/roles/new/page": "/admin/roles/new", "/admin/templates/[id]/page": "/admin/templates/[id]", "/admin/users/[id]/page": "/admin/users/[id]", "/admin/users/new/page": "/admin/users/new", "/admin/templates/new/page": "/admin/templates/new", "/admin/users/[id]/edit/page": "/admin/users/[id]/edit", "/admin/users/page": "/admin/users", "/dashboard/page": "/dashboard", "/login/page": "/login", "/dashboard/[user]/page": "/dashboard/[user]", "/page": "/", "/register/page": "/register", "/tasks/[id]/page": "/tasks/[id]"}