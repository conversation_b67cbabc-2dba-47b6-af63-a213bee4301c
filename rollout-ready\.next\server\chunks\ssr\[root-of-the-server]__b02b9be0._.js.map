{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/PWAInstallPrompt.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\n\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[];\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed';\n    platform: string;\n  }>;\n  prompt(): Promise<void>;\n}\n\nexport default function PWAInstallPrompt() {\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);\n  const [showInstallPrompt, setShowInstallPrompt] = useState(false);\n  const [isInstalled, setIsInstalled] = useState(false);\n\n  useEffect(() => {\n    // Check if app is already installed\n    if (window.matchMedia('(display-mode: standalone)').matches) {\n      setIsInstalled(true);\n      return;\n    }\n\n    // Listen for the beforeinstallprompt event\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault();\n      setDeferredPrompt(e as BeforeInstallPromptEvent);\n      setShowInstallPrompt(true);\n    };\n\n    // Listen for app installed event\n    const handleAppInstalled = () => {\n      setIsInstalled(true);\n      setShowInstallPrompt(false);\n      setDeferredPrompt(null);\n    };\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n    window.addEventListener('appinstalled', handleAppInstalled);\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      window.removeEventListener('appinstalled', handleAppInstalled);\n    };\n  }, []);\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return;\n\n    deferredPrompt.prompt();\n    const { outcome } = await deferredPrompt.userChoice;\n    \n    if (outcome === 'accepted') {\n      setShowInstallPrompt(false);\n    }\n    \n    setDeferredPrompt(null);\n  };\n\n  const handleDismiss = () => {\n    setShowInstallPrompt(false);\n    // Remember user dismissed for this session\n    sessionStorage.setItem('pwa-install-dismissed', 'true');\n  };\n\n  // Don't show if already installed, dismissed, or no prompt available\n  if (isInstalled || !showInstallPrompt || !deferredPrompt) {\n    return null;\n  }\n\n  // Check if user already dismissed this session\n  if (sessionStorage.getItem('pwa-install-dismissed')) {\n    return null;\n  }\n\n  return (\n    <Card className=\"fixed bottom-4 left-4 right-4 z-50 shadow-lg border-blue-200 bg-blue-50 md:left-auto md:right-4 md:w-96\">\n      <CardHeader className=\"pb-3\">\n        <CardTitle className=\"text-lg flex items-center gap-2\">\n          <svg className=\"w-5 h-5 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n          </svg>\n          Install Rollout Ready\n        </CardTitle>\n        <CardDescription>\n          Install this app on your device for quick access and offline use\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"pt-0\">\n        <div className=\"flex gap-2\">\n          <Button onClick={handleInstallClick} className=\"flex-1\">\n            Install App\n          </Button>\n          <Button variant=\"outline\" onClick={handleDismiss}>\n            Not Now\n          </Button>\n        </div>\n        <div className=\"mt-3 text-xs text-gray-600\">\n          <p>✓ Works offline</p>\n          <p>✓ Quick access from home screen</p>\n          <p>✓ Native app experience</p>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;YAC3D,eAAe;YACf;QACF;QAEA,2CAA2C;QAC3C,MAAM,4BAA4B,CAAC;YACjC,EAAE,cAAc;YAChB,kBAAkB;YAClB,qBAAqB;QACvB;QAEA,iCAAiC;QACjC,MAAM,qBAAqB;YACzB,eAAe;YACf,qBAAqB;YACrB,kBAAkB;QACpB;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAC/C,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;YAClD,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,eAAe,MAAM;QACrB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;QAEnD,IAAI,YAAY,YAAY;YAC1B,qBAAqB;QACvB;QAEA,kBAAkB;IACpB;IAEA,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,2CAA2C;QAC3C,eAAe,OAAO,CAAC,yBAAyB;IAClD;IAEA,qEAAqE;IACrE,IAAI,eAAe,CAAC,qBAAqB,CAAC,gBAAgB;QACxD,OAAO;IACT;IAEA,+CAA+C;IAC/C,IAAI,eAAe,OAAO,CAAC,0BAA0B;QACnD,OAAO;IACT;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAwL,UAAS;;;;;;;;;;;4BACxN;;;;;;;kCAGR,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAoB,WAAU;0CAAS;;;;;;0CAGxD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;0CAAe;;;;;;;;;;;;kCAIpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ConnectionStatus.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Badge } from \"@/components/ui/badge\";\n\nexport default function ConnectionStatus() {\n  const [isOnline, setIsOnline] = useState(true);\n  const [showStatus, setShowStatus] = useState(false);\n\n  useEffect(() => {\n    // Set initial online status\n    setIsOnline(navigator.onLine);\n\n    const handleOnline = () => {\n      setIsOnline(true);\n      setShowStatus(true);\n      // Hide status after 3 seconds\n      setTimeout(() => setShowStatus(false), 3000);\n    };\n\n    const handleOffline = () => {\n      setIsOnline(false);\n      setShowStatus(true);\n    };\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  }, []);\n\n  if (!showStatus && isOnline) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50\">\n      <Badge \n        variant={isOnline ? \"default\" : \"destructive\"}\n        className={`transition-all duration-300 ${\n          isOnline \n            ? \"bg-green-100 text-green-800 border-green-200\" \n            : \"bg-red-100 text-red-800 border-red-200\"\n        }`}\n      >\n        <div className=\"flex items-center gap-2\">\n          <div \n            className={`w-2 h-2 rounded-full ${\n              isOnline ? \"bg-green-500\" : \"bg-red-500\"\n            }`}\n          />\n          {isOnline ? \"Back Online\" : \"Offline\"}\n        </div>\n      </Badge>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,YAAY,UAAU,MAAM;QAE5B,MAAM,eAAe;YACnB,YAAY;YACZ,cAAc;YACd,8BAA8B;YAC9B,WAAW,IAAM,cAAc,QAAQ;QACzC;QAEA,MAAM,gBAAgB;YACpB,YAAY;YACZ,cAAc;QAChB;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,cAAc,UAAU;QAC3B,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;YACJ,SAAS,WAAW,YAAY;YAChC,WAAW,CAAC,4BAA4B,EACtC,WACI,iDACA,0CACJ;sBAEF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAW,CAAC,qBAAqB,EAC/B,WAAW,iBAAiB,cAC5B;;;;;;oBAEH,WAAW,gBAAgB;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/AuthProvider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext, useEffect, useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\ninterface User {\n  id: number;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  systemRole: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  login: (username: string, password: string) => Promise<boolean>;\n  logout: () => Promise<void>;\n  checkAuth: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  const checkAuth = async () => {\n    try {\n      const response = await fetch(\"/api/auth/me\");\n      if (response.ok) {\n        const data = await response.json();\n        setUser(data.user);\n      } else {\n        setUser(null);\n      }\n    } catch (error) {\n      console.error(\"Auth check failed:\", error);\n      setUser(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const login = async (username: string, password: string): Promise<boolean> => {\n    try {\n      const response = await fetch(\"/api/auth/login\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ username, password }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setUser(data.user);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(\"Login failed:\", error);\n      return false;\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await fetch(\"/api/auth/logout\", { method: \"POST\" });\n    } catch (error) {\n      console.error(\"Logout failed:\", error);\n    } finally {\n      setUser(null);\n      router.push(\"/login\");\n    }\n  };\n\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  return (\n    <AuthContext.Provider value={{ user, isLoading, login, logout, checkAuth }}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAsBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI;YACnB,OAAO;gBACL,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,QAAQ;QACV,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OAAO,UAAkB;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAU;gBAAS;YAC5C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI;gBACjB,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;QACT;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,QAAQ;YACR,OAAO,IAAI,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAW;YAAO;YAAQ;QAAU;kBACtE;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { Button } from \"@/components/ui/button\";\nimport { useAuth } from \"@/components/AuthProvider\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function Navigation() {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    await logout();\n    router.push(\"/login\");\n  };\n\n  if (!user) {\n    return (\n      <nav className=\"border-b bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Rollout Ready</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/login\">\n                <Button variant=\"outline\">Login</Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n    );\n  }\n\n  return (\n    <nav className=\"border-b bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors\">\n              Rollout Ready\n            </Link>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/\" className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\">\n              Home\n            </Link>\n            \n            {/* System Admin Navigation */}\n            {user.systemRole === 'ADMIN' && (\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\">\n                System Admin\n              </Link>\n            )}\n\n            {/* Manager Navigation */}\n            {user.systemRole === 'MANAGER' && (\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\">\n                Project Admin\n              </Link>\n            )}\n            \n            {/* User Dashboard */}\n            <Link \n              href={`/dashboard/${user.username}`} \n              className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              My Tasks\n            </Link>\n            \n            {/* User Info */}\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm text-gray-600\">\n                {user.firstName || user.username}\n              </span>\n              <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                {user.systemRole}\n              </span>\n            </div>\n            \n            <Button variant=\"outline\" onClick={handleLogout}>\n              Logout\n            </Button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;;;;;;sCAElD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAwE;;;;;;;;;;;kCAInG,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA6E;;;;;;4BAKrG,KAAK,UAAU,KAAK,yBACnB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA6E;;;;;;4BAM5G,KAAK,UAAU,KAAK,2BACnB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA6E;;;;;;0CAM7G,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;gCACnC,WAAU;0CACX;;;;;;0CAKD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,KAAK,SAAS,IAAI,KAAK,QAAQ;;;;;;kDAElC,8OAAC;wCAAK,WAAU;kDACb,KAAK,UAAU;;;;;;;;;;;;0CAIpB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D", "debugId": null}}]}