(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[843],{138:(e,t,s)=>{Promise.resolve().then(s.bind(s,9141))},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var a=s(5155);s(2115);var r=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:i,asChild:l=!1,...o}=e,c=l?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:s,size:i,className:t})),...o})}},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,type:s,...i}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},5057:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(5155);s(2115);var r=s(968),i=s(9434);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(5155);s(2115);var r=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,asChild:i=!1,...l}=e,o=i?r.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(d({variant:s}),t),...l})}},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},9141:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var a=s(5155),r=s(2115),i=s(5695),n=s(6874),d=s.n(n),l=s(6695),o=s(285),c=s(2523),u=s(5057),p=s(1396),x=s(6474),h=s(5196),m=s(7863),g=s(9434);function f(e){let{...t}=e;return(0,a.jsx)(p.bL,{"data-slot":"select",...t})}function v(e){let{...t}=e;return(0,a.jsx)(p.WT,{"data-slot":"select-value",...t})}function b(e){let{className:t,size:s="default",children:r,...i}=e;return(0,a.jsxs)(p.l9,{"data-slot":"select-trigger","data-size":s,className:(0,g.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[r,(0,a.jsx)(p.In,{asChild:!0,children:(0,a.jsx)(x.A,{className:"size-4 opacity-50"})})]})}function j(e){let{className:t,children:s,position:r="popper",...i}=e;return(0,a.jsx)(p.ZL,{children:(0,a.jsxs)(p.UC,{"data-slot":"select-content",className:(0,g.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...i,children:[(0,a.jsx)(N,{}),(0,a.jsx)(p.LM,{className:(0,g.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(w,{})]})})}function y(e){let{className:t,children:s,...r}=e;return(0,a.jsxs)(p.q7,{"data-slot":"select-item",className:(0,g.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(p.VF,{children:(0,a.jsx)(h.A,{className:"size-4"})})}),(0,a.jsx)(p.p4,{children:s})]})}function N(e){let{className:t,...s}=e;return(0,a.jsx)(p.PP,{"data-slot":"select-scroll-up-button",className:(0,g.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(m.A,{className:"size-4"})})}function w(e){let{className:t,...s}=e;return(0,a.jsx)(p.wn,{"data-slot":"select-scroll-down-button",className:(0,g.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(x.A,{className:"size-4"})})}var k=s(6126);function C(){let e=(0,i.useRouter)(),[t,s]=(0,r.useState)(!1),[n,p]=(0,r.useState)([]),[x,h]=(0,r.useState)({name:"",description:"",roleId:"",autoAssign:!1}),[m,g]=(0,r.useState)([]),[N,w]=(0,r.useState)({description:"",offsetDays:0,isRecurring:!1,isCritical:!1});(0,r.useEffect)(()=>{C()},[]);let C=async()=>{try{let e=await fetch("/api/roles");if(e.ok){let t=await e.json();p(t)}}catch(e){console.error("Error fetching roles:",e)}},z=async t=>{t.preventDefault(),s(!0);try{let t={...x,roleId:parseInt(x.roleId),tasks:m},s=await fetch("/api/templates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(s.ok)e.push("/admin/templates");else{let e=await s.json();alert("Error: ".concat(e.message))}}catch(e){alert("An error occurred while creating the template")}finally{s(!1)}},T=e=>{let t="checkbox"===e.target.type?e.target.checked:e.target.value;h({...x,[e.target.name]:t})},_=e=>{g(m.filter((t,s)=>s!==e))},A=(e,t)=>{w({...N,[e]:t})};return(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Template"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Create a reusable task template for a specific role"})]}),(0,a.jsxs)("form",{onSubmit:z,className:"space-y-8",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"Template Details"}),(0,a.jsx)(l.BT,{children:"Basic information about the template"})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"name",children:"Template Name *"}),(0,a.jsx)(c.p,{id:"name",name:"name",type:"text",required:!0,value:x.name,onChange:T,placeholder:"e.g., Project Manager Checklist"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"roleId",children:"Role *"}),(0,a.jsxs)(f,{value:x.roleId,onValueChange:e=>h({...x,roleId:e}),children:[(0,a.jsx)(b,{children:(0,a.jsx)(v,{placeholder:"Select a role"})}),(0,a.jsx)(j,{children:n.map(e=>(0,a.jsx)(y,{value:e.id.toString(),children:e.name},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"description",children:"Description"}),(0,a.jsx)("textarea",{id:"description",name:"description",value:x.description,onChange:T,placeholder:"Brief description of this template",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"autoAssign",name:"autoAssign",checked:x.autoAssign,onChange:T,className:"rounded border-gray-300"}),(0,a.jsx)(u.J,{htmlFor:"autoAssign",children:"Auto-assign when role is added to project"})]})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.ZB,{children:"Template Tasks"}),(0,a.jsx)(l.BT,{children:"Add tasks that will be created when this template is used"})]}),(0,a.jsxs)(l.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,a.jsx)("h3",{className:"font-semibold mb-4",children:"Add New Task"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)(u.J,{children:"Task Description *"}),(0,a.jsx)(c.p,{value:N.description,onChange:e=>A("description",e.target.value),placeholder:"e.g., Create project charter and scope document"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u.J,{children:"Offset Days"}),(0,a.jsx)(c.p,{type:"number",value:N.offsetDays,onChange:e=>A("offsetDays",parseInt(e.target.value)||0),placeholder:"0"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Days from project start (negative for pre-start)"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:N.isCritical,onChange:e=>A("isCritical",e.target.checked),className:"rounded border-gray-300"}),(0,a.jsx)(u.J,{children:"Critical Task"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:N.isRecurring,onChange:e=>A("isRecurring",e.target.checked),className:"rounded border-gray-300"}),(0,a.jsx)(u.J,{children:"Recurring Task"})]})]})]}),(0,a.jsx)(o.$,{type:"button",onClick:()=>{N.description.trim()&&(g([...m,{...N}]),w({description:"",offsetDays:0,isRecurring:!1,isCritical:!1}))},className:"mt-4",disabled:!N.description.trim(),children:"Add Task"})]}),m.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold mb-4",children:["Template Tasks (",m.length,")"]}),(0,a.jsx)("div",{className:"space-y-2",children:m.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.description}),(0,a.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,a.jsxs)(k.E,{variant:"outline",children:[e.offsetDays>=0?"T+".concat(e.offsetDays):"T".concat(e.offsetDays)," days"]}),e.isCritical&&(0,a.jsx)(k.E,{variant:"destructive",children:"Critical"}),e.isRecurring&&(0,a.jsx)(k.E,{variant:"secondary",children:"Recurring"})]})]}),(0,a.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>_(t),children:"Remove"})]},t))})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(o.$,{type:"submit",disabled:t||!x.roleId||0===m.length,children:t?"Creating...":"Create Template"}),(0,a.jsx)(d(),{href:"/admin/templates",children:(0,a.jsx)(o.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})]})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(2596),r=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,277,232,441,684,358],()=>t(138)),_N_E=e.O()}]);