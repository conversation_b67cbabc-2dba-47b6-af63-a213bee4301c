(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var n=r(5155);r(2115);var s=r(9708),a=r(2085),i=r(9434);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:a,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:a,className:t})),...d})}},347:()=>{},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(2596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let a=s(t)||s(n);return i[e][a]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},2103:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(5155),s=r(2115),a=r(285),i=r(6695);function l(){let[e,t]=(0,s.useState)(null),[r,l]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(window.matchMedia("(display-mode: standalone)").matches)return void d(!0);let e=e=>{e.preventDefault(),t(e),l(!0)},r=()=>{d(!0),l(!1),t(null)};return window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",r),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",r)}},[]);let c=async()=>{if(!e)return;e.prompt();let{outcome:r}=await e.userChoice;"accepted"===r&&l(!1),t(null)};return o||!r||!e||sessionStorage.getItem("pwa-install-dismissed")?null:(0,n.jsxs)(i.Zp,{className:"fixed bottom-4 left-4 right-4 z-50 shadow-lg border-blue-200 bg-blue-50 md:left-auto md:right-4 md:w-96",children:[(0,n.jsxs)(i.aR,{className:"pb-3",children:[(0,n.jsxs)(i.ZB,{className:"text-lg flex items-center gap-2",children:[(0,n.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Install Rollout Ready"]}),(0,n.jsx)(i.BT,{children:"Install this app on your device for quick access and offline use"})]}),(0,n.jsxs)(i.Wu,{className:"pt-0",children:[(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(a.$,{onClick:c,className:"flex-1",children:"Install App"}),(0,n.jsx)(a.$,{variant:"outline",onClick:()=>{l(!1),sessionStorage.setItem("pwa-install-dismissed","true")},children:"Not Now"})]}),(0,n.jsxs)("div",{className:"mt-3 text-xs text-gray-600",children:[(0,n.jsx)("p",{children:"✓ Works offline"}),(0,n.jsx)("p",{children:"✓ Quick access from home screen"}),(0,n.jsx)("p",{children:"✓ Native app experience"})]})]})]})}},4541:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,AuthProvider:()=>l});var n=r(5155),s=r(2115),a=r(5695);let i=(0,s.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,s.useState)(null),[o,d]=(0,s.useState)(!0),c=(0,a.useRouter)(),u=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let t=await e.json();l(t.user)}else l(null)}catch(e){console.error("Auth check failed:",e),l(null)}finally{d(!1)}},f=async(e,t)=>{try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t})});if(r.ok){let e=await r.json();return l(e.user),!0}return!1}catch(e){return console.error("Login failed:",e),!1}},v=async()=>{try{await fetch("/api/auth/logout",{method:"POST"})}catch(e){console.error("Logout failed:",e)}finally{l(null),c.push("/login")}};return(0,s.useEffect)(()=>{u()},[]),(0,n.jsx)(i.Provider,{value:{user:r,isLoading:o,login:f,logout:v,checkAuth:u},children:t})}function o(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4985:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2093,23)),Promise.resolve().then(r.t.bind(r,7735,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,4541)),Promise.resolve().then(r.bind(r,9228)),Promise.resolve().then(r.bind(r,5506)),Promise.resolve().then(r.bind(r,2103))},5506:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(5155),s=r(6874),a=r.n(s),i=r(285),l=r(4541),o=r(5695);function d(){let{user:e,logout:t}=(0,l.A)(),r=(0,o.useRouter)(),s=async()=>{await t(),r.push("/login")};return e?(0,n.jsx)("nav",{className:"border-b bg-white",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Rollout Ready"})}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(a(),{href:"/",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"Home"}),("ADMIN"===e.systemRole||"MANAGER"===e.systemRole)&&(0,n.jsx)(a(),{href:"/admin",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,n.jsx)(a(),{href:"/dashboard/".concat(e.username),className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"My Tasks"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:e.firstName||e.username}),(0,n.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded",children:e.systemRole})]}),(0,n.jsx)(i.$,{variant:"outline",onClick:s,children:"Logout"})]})]})})}):(0,n.jsx)("nav",{className:"border-b bg-white",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Rollout Ready"})}),(0,n.jsx)("div",{className:"flex items-center space-x-4",children:(0,n.jsx)(a(),{href:"/login",children:(0,n.jsx)(i.$,{variant:"outline",children:"Login"})})})]})})})}},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>a});var n=r(2115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(5155);r(2115);var s=r(9708),a=r(2085),i=r(9434);let l=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:a=!1,...o}=e,d=a?s.DX:"span";return(0,n.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(l({variant:r}),t),...o})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>a,aR:()=>i});var n=r(5155);r(2115);var s=r(9434);function a(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9228:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(5155),s=r(2115),a=r(6126);function i(){let[e,t]=(0,s.useState)(!0),[r,i]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{t(navigator.onLine);let e=()=>{t(!0),i(!0),setTimeout(()=>i(!1),3e3)},r=()=>{t(!1),i(!0)};return window.addEventListener("online",e),window.addEventListener("offline",r),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",r)}},[]),!r&&e)?null:(0,n.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,n.jsx)(a.E,{variant:e?"default":"destructive",className:"transition-all duration-300 ".concat(e?"bg-green-100 text-green-800 border-green-200":"bg-red-100 text-red-800 border-red-200"),children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(e?"bg-green-500":"bg-red-500")}),e?"Back Online":"Offline"]})})})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(2596),s=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,TL:()=>i});var n=r(2115),s=r(6101),a=r(5155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var i;let e,l,o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let n in t){let s=e[n],a=t[n];/^on[A-Z]/.test(n)?s&&a?r[n]=(...e)=>{let t=a(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...a}:"className"===n&&(r[n]=[s,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,s.t)(t,o):o),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...i}=e,l=n.Children.toArray(s),o=l.find(d);if(o){let e=o.props.children,s=l.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,a.jsx)(t,{...i,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,874,277,441,684,358],()=>t(4985)),_N_E=e.O()}]);