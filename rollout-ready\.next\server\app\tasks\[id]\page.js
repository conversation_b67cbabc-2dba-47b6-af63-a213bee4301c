(()=>{var e={};e.id=43,e.ids=[43],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>p,eb:()=>x,gC:()=>m,l6:()=>o,yv:()=>c});var a=s(60687);s(43210);var r=s(16725),i=s(78272),n=s(13964),l=s(3589),d=s(4780);function o({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function p({className:e,size:t="default",children:s,...n}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:s="popper",...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,a.jsx)(u,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function x({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function u({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28305:(e,t,s)=>{"use strict";s.d(t,{default:()=>j});var a=s(60687),r=s(43210),i=s(44493),n=s(29523),l=s(15079),d=s(4780);let o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,d.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));o.displayName="Textarea";var c=s(89667),p=s(54300),m=s(62688);let x=(0,m.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),u=(0,m.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),h=(0,m.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),f=(0,m.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var g=s(16189);function j({task:e}){let t=(0,g.useRouter)(),[s,d]=(0,r.useState)(!1),[m,j]=(0,r.useState)(!1),[v,y]=(0,r.useState)(e.status),[N,b]=(0,r.useState)(e.comments||""),[w,k]=(0,r.useState)(e.timeSpentMinutes?Math.floor(e.timeSpentMinutes/60).toString():""),[D,M]=(0,r.useState)(e.timeSpentMinutes?(e.timeSpentMinutes%60).toString():""),[C,S]=(0,r.useState)(null),P=async()=>{d(!0);try{let s=60*(parseInt(w)||0)+(parseInt(D)||0),a=await fetch(`/api/tasks/${e.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:v,comments:N.trim()||null,timeSpentMinutes:s>0?s:null,completedAt:"DONE"===v?new Date().toISOString():null})});if(a.ok)t.refresh();else{let e=await a.json();alert(`Error: ${e.message}`)}}catch(e){alert("An error occurred while updating the task")}finally{d(!1)}},R=async()=>{if(C){j(!0);try{let s=new FormData;s.append("file",C),s.append("uploadedBy",e.projectRole.user.username);let a=await fetch(`/api/tasks/${e.id}/attachments`,{method:"POST",body:s});if(a.ok){S(null);let e=document.getElementById("file-upload");e&&(e.value=""),t.refresh()}else{let e=await a.json();alert(`Error: ${e.message}`)}}catch(e){alert("An error occurred while uploading the file")}finally{j(!1)}}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x,{className:"h-5 w-5"}),"Update Task"]}),(0,a.jsx)(i.BT,{children:"Change task status, add comments, and record time spent"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"status",children:"Task Status"}),(0,a.jsxs)(l.l6,{value:v,onValueChange:y,children:[(0,a.jsx)(l.bq,{children:(0,a.jsx)(l.yv,{placeholder:"Select status"})}),(0,a.jsxs)(l.gC,{children:[(0,a.jsx)(l.eb,{value:"TODO",children:"To Do"}),(0,a.jsx)(l.eb,{value:"IN_PROGRESS",children:"In Progress"}),(0,a.jsx)(l.eb,{value:"DONE",children:"Done"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(p.J,{className:"flex items-center gap-2",children:[(0,a.jsx)(u,{className:"h-4 w-4"}),"Time Spent"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(c.p,{type:"number",placeholder:"Hours",value:w,onChange:e=>k(e.target.value),min:"0",max:"999"}),(0,a.jsx)(p.J,{className:"text-xs text-gray-500 mt-1",children:"Hours"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(c.p,{type:"number",placeholder:"Minutes",value:D,onChange:e=>M(e.target.value),min:"0",max:"59"}),(0,a.jsx)(p.J,{className:"text-xs text-gray-500 mt-1",children:"Minutes"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(p.J,{htmlFor:"comments",className:"flex items-center gap-2",children:[(0,a.jsx)(h,{className:"h-4 w-4"}),"Comments / Notes"]}),(0,a.jsx)(o,{id:"comments",placeholder:"Add any comments, notes, or updates about this task...",value:N,onChange:e=>b(e.target.value),rows:4})]}),(0,a.jsx)(n.$,{onClick:P,disabled:s,className:"w-full",children:s?"Updating...":"Update Task"})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f,{className:"h-5 w-5"}),"Upload Attachment"]}),(0,a.jsx)(i.BT,{children:"Upload files as proof of completion or supporting documents"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"file-upload",children:"Select File"}),(0,a.jsx)(c.p,{id:"file-upload",type:"file",onChange:e=>S(e.target.files?.[0]||null),accept:".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt,.xlsx,.xls,.ppt,.pptx"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Supported formats: PDF, Word, Excel, PowerPoint, Images, Text files (Max 10MB)"})]}),C&&(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Selected file:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[C.name," (",(C.size/1024).toFixed(1)," KB)"]})]}),(0,a.jsx)(n.$,{onClick:R,disabled:!C||m,className:"w-full",variant:"outline",children:m?"Uploading...":"Upload File"})]})]})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44534:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,28305))},46903:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["tasks",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,52141)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\tasks\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\tasks\\[id]\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/tasks/[id]/page",pathname:"/tasks/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},52141:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(37413),r=s(78963),i=s(30084),n=s(23469),l=s(5069),d=s(39916),o=s(62988),c=s(61120);let p=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),m=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),x=e=>{let t=m(e);return t.charAt(0).toUpperCase()+t.slice(1)},u=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),h=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var f={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let g=(0,c.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:r="",children:i,iconNode:n,...l},d)=>(0,c.createElement)("svg",{ref:d,...f,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:u("lucide",r),...!i&&!h(l)&&{"aria-hidden":"true"},...l},[...n.map(([e,t])=>(0,c.createElement)(e,t)),...Array.isArray(i)?i:[i]])),j=(e,t)=>{let s=(0,c.forwardRef)(({className:s,...a},r)=>(0,c.createElement)(g,{ref:r,iconNode:t,className:u(`lucide-${p(x(e))}`,`lucide-${e}`,s),...a}));return s.displayName=x(e),s},v=j("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),y=j("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),N=j("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),b=j("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),w=j("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var k=s(4536),D=s.n(k);async function M(e){return await l.z.projectTask.findUnique({where:{id:e},include:{project:{select:{id:!0,name:!0,description:!0,startDate:!0}},projectRole:{include:{role:{select:{id:!0,name:!0,description:!0}},user:{select:{id:!0,username:!0,firstName:!0,lastName:!0}}}},templateTask:{select:{id:!0,description:!0,isRecurring:!0,isCritical:!0}},attachments:{orderBy:{createdAt:"desc"}}}})}function C(e,t){return"DONE"!==t&&new Date(e)<new Date}async function S({params:e}){let t=parseInt((await e).id);isNaN(t)&&(0,d.notFound)();let s=await M(t);s||(0,d.notFound)();let l=s.projectRole.user,c=l.firstName&&l.lastName?`${l.firstName} ${l.lastName}`:l.username;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(D(),{href:`/dashboard/${l.username}`,children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(v,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Task Details"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage task progress and attachments"})]})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(r.ZB,{className:"text-xl",children:s.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(y,{className:"h-4 w-4"}),s.project.name]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(N,{className:"h-4 w-4"}),s.projectRole.role.name]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[function(e){switch(e){case"TODO":return(0,a.jsx)(i.E,{variant:"secondary",children:"To Do"});case"IN_PROGRESS":return(0,a.jsx)(i.E,{variant:"default",children:"In Progress"});case"DONE":return(0,a.jsx)(i.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"Done"});default:return(0,a.jsx)(i.E,{variant:"secondary",children:e})}}(s.status),s.templateTask?.isCritical&&(0,a.jsx)(i.E,{variant:"destructive",children:"Critical"}),C(s.dueDate,s.status)&&(0,a.jsx)(i.E,{variant:"destructive",children:"Overdue"})]})]})}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(b,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Due Date:"}),(0,a.jsx)("span",{className:C(s.dueDate,s.status)?"text-red-600 font-medium":"",children:new Date(s.dueDate).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(w,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Time Spent:"}),(0,a.jsx)("span",{children:function(e){if(!e)return"Not recorded";let t=Math.floor(e/60),s=e%60;return 0===t?`${s} minutes`:0===s?`${t} hour${t>1?"s":""}`:`${t} hour${t>1?"s":""} ${s} minutes`}(s.timeSpentMinutes)})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(N,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Assigned to:"}),(0,a.jsx)("span",{children:c})]}),s.completedAt&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(b,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Completed:"}),(0,a.jsx)("span",{children:new Date(s.completedAt).toLocaleDateString()})]})]}),s.comments&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Comments:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 whitespace-pre-wrap",children:s.comments})]})]})]}),(0,a.jsx)(o.default,{task:s})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{className:"text-lg",children:"Project Information"})}),(0,a.jsxs)(r.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"Project Name"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:s.project.name})]}),s.project.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"Description"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:s.project.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"Start Date"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:new Date(s.project.startDate).toLocaleDateString()})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{className:"text-lg",children:"Attachments"}),(0,a.jsx)(r.BT,{children:"Files uploaded for this task"})]}),(0,a.jsx)(r.Wu,{children:0===s.attachments.length?(0,a.jsx)("p",{className:"text-sm text-gray-500 text-center py-4",children:"No attachments yet"}):(0,a.jsx)("div",{className:"space-y-2",children:s.attachments.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 border rounded",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:e.originalName}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[(e.fileSize/1024).toFixed(1)," KB • ",e.uploadedBy]})]}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsx)("a",{href:`/api/attachments/${e.id}`,download:!0,children:"Download"})})]},e.id))})})]})]})]})]})}},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var a=s(60687),r=s(43210),i=s(14163),n=r.forwardRef((e,t)=>(0,a.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=s(4780);function d({className:e,...t}){return(0,a.jsx)(n,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},62988:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\components\\\\TaskDetailForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\components\\TaskDetailForm.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},81486:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,62988))},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(60687);s(43210);var r=s(4780);function i({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,205,277,923,425,811,113],()=>s(46903));module.exports=a})();