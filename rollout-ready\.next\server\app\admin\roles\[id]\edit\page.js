(()=>{var e={};e.id=467,e.ids=[467],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>o});var s=r(43210),i=r(51215),a=r(8730),n=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:i,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(i?r:t,{...a,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26281:(e,t,r)=>{Promise.resolve().then(r.bind(r,47499))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47499:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),a=r(16189),n=r(85814),o=r.n(n),l=r(44493),d=r(29523),c=r(89667),p=r(54300);function u({params:e}){let t=(0,a.useRouter)(),[r,n]=(0,i.useState)(!1),[u,x]=(0,i.useState)(null),[m,h]=(0,i.useState)({name:"",description:""}),[f,j]=(0,i.useState)(""),v=async e=>{if(e.preventDefault(),u){n(!0),j("");try{let e=await fetch(`/api/roles/${u.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)});if(e.ok)t.push(`/admin/roles/${u.id}`);else{let t=await e.json();j(t.message||"Failed to update role")}}catch(e){j("An error occurred while updating the role")}finally{n(!1)}}},b=e=>{h({...m,[e.target.name]:e.target.value})},g=async()=>{if(u){if(u._count.projectRoles>0)return void j("Cannot delete role that is currently used in projects");if(confirm(`Are you sure you want to delete the role "${u.name}"? This action cannot be undone.`)){n(!0),j("");try{let e=await fetch(`/api/roles/${u.id}`,{method:"DELETE"});if(e.ok)t.push("/admin/roles");else{let t=await e.json();j(t.message||"Failed to delete role")}}catch(e){j("An error occurred while deleting the role")}finally{n(!1)}}}};return f?(0,s.jsx)("div",{className:"space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Error"}),(0,s.jsx)("p",{className:"text-red-600 mt-2",children:f}),(0,s.jsx)(o(),{href:"/admin/roles",children:(0,s.jsx)(d.$,{className:"mt-4",children:"Back to Roles"})})]})}):u?(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Edit Role: ",u.name]}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Modify role details and settings"})]}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-8",children:[(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"Role Details"}),(0,s.jsx)(l.BT,{children:"Update basic role information"})]}),(0,s.jsxs)(l.Wu,{className:"space-y-6",children:[f&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:f}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p.J,{htmlFor:"name",children:"Role Name *"}),(0,s.jsx)(c.p,{id:"name",name:"name",type:"text",required:!0,value:m.name,onChange:b,placeholder:"e.g., Project Manager, Infrastructure Lead"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p.J,{htmlFor:"description",children:"Description"}),(0,s.jsx)("textarea",{id:"description",name:"description",value:m.description,onChange:b,placeholder:"Brief description of this role's responsibilities",className:"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"Role Usage"}),(0,s.jsx)(l.BT,{children:"Current usage of this role in the system"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Templates"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:u._count.templates}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:"Associated templates"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Project Usage"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:u._count.projectRoles}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:"Times used in projects"})]})]})})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(d.$,{type:"submit",disabled:r,children:r?"Updating...":"Update Role"}),(0,s.jsx)(o(),{href:`/admin/roles/${u.id}`,children:(0,s.jsx)(d.$,{type:"button",variant:"outline",children:"Cancel"})}),0===u._count.projectRoles&&(0,s.jsx)(d.$,{type:"button",variant:"destructive",onClick:g,disabled:r,children:"Delete Role"})]})]}),u._count.projectRoles>0&&(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Cannot Delete"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("p",{className:"text-gray-600",children:["This role cannot be deleted because it is currently used in ",u._count.projectRoles," project",1!==u._count.projectRoles?"s":"",". Remove the role from all projects before deleting it."]})})]})]}):(0,s.jsx)("div",{className:"space-y-8",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Loading..."})})})}},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var s=r(60687),i=r(43210),a=r(14163),n=i.forwardRef((e,t)=>(0,s.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=r(4780);function l({className:e,...t}){return(0,s.jsx)(n,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},57261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RR\\\\rollout-ready\\\\src\\\\app\\\\admin\\\\roles\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\[id]\\edit\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81755:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["roles",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57261)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\admin\\roles\\[id]\\edit\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/roles/[id]/edit/page",pathname:"/admin/roles/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},84425:(e,t,r)=>{Promise.resolve().then(r.bind(r,57261))},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(60687);r(43210);var i=r(4780);function a({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,205,277,811],()=>r(81755));module.exports=s})();