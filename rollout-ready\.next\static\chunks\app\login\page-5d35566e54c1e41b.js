(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var n=t(5155);t(2115);var s=t(9708),a=t(2085),i=t(9434);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:a,asChild:d=!1,...o}=e,u=d?s.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:a,className:r})),...o})}},351:(e,r,t)=>{Promise.resolve().then(t.bind(t,9690))},2085:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var n=t(2596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,i=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:l}=r,d=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],n=null==l?void 0:l[e];if(null===r)return null;let a=s(r)||s(n);return i[e][a]}),o=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return a(e,d,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...o}[r]):({...l,...o})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var n=t(5155);t(2115);var s=t(9434);function a(e){let{className:r,type:t,...a}=e;return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}},3655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>d,sG:()=>l});var n=t(2115),s=t(7650),a=t(9708),i=t(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,a.TL)(`Primitive.${r}`),s=n.forwardRef((e,n)=>{let{asChild:s,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?t:r,{...a,ref:n})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{});function d(e,r){e&&s.flushSync(()=>e.dispatchEvent(r))}},5695:(e,r,t)=>{"use strict";var n=t(8999);t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>i,t:()=>a});var n=t(2115);function s(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=s(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():s(e[r],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>a,aR:()=>i});var n=t(5155);t(2115);var s=t(9434);function a(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function l(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function d(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function o(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}},8979:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var n=t(5155),s=t(2115),a=t(3655),i=s.forwardRef((e,r)=>(0,n.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var l=t(9434);function d(e){let{className:r,...t}=e;return(0,n.jsx)(i,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var n=t(2596),s=t(9688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,n.$)(r))}},9690:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var n=t(5155),s=t(2115),a=t(5695),i=t(6695),l=t(285),d=t(2523),o=t(8979);function u(){let[e,r]=(0,s.useState)(""),[t,u]=(0,s.useState)(""),[c,p]=(0,s.useState)(!1),[f,m]=(0,s.useState)(""),v=(0,a.useRouter)(),g=(0,a.useSearchParams)().get("redirect")||"/",x=async r=>{r.preventDefault(),p(!0),m("");try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t})}),n=await r.json();r.ok?"ADMIN"===n.user.systemRole||"MANAGER"===n.user.systemRole?v.push("/"===g?"/admin":g):v.push("/"===g?"/dashboard/".concat(n.user.username):g):m(n.message||"Login failed")}catch(e){m("An error occurred. Please try again.")}finally{p(!1)}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Rollout Ready"}),(0,n.jsx)("p",{className:"mt-2 text-gray-600",children:"Sign in to your account"})]}),(0,n.jsxs)(i.Zp,{children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{children:"Login"}),(0,n.jsx)(i.BT,{children:"Enter your credentials to access the system"})]}),(0,n.jsx)(i.Wu,{children:(0,n.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[f&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:f}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(o.J,{htmlFor:"username",children:"Username or Email"}),(0,n.jsx)(d.p,{id:"username",type:"text",value:e,onChange:e=>r(e.target.value),required:!0,disabled:c,placeholder:"Enter your username or email"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(o.J,{htmlFor:"password",children:"Password"}),(0,n.jsx)(d.p,{id:"password",type:"password",value:t,onChange:e=>u(e.target.value),required:!0,disabled:c,placeholder:"Enter your password"})]}),(0,n.jsx)(l.$,{type:"submit",className:"w-full",disabled:c,children:c?"Signing in...":"Sign In"})]})})]}),(0,n.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"Demo Accounts"}),(0,n.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Admin:"})," admin / admin123"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Manager:"})," manager / manager123"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Business Analyst:"})," alice / user123"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Project Manager:"})," bob / user123"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Infrastructure Lead:"})," charlie / user123"]})]})]})]})})}function c(){return(0,n.jsx)(s.Suspense,{fallback:(0,n.jsx)("div",{children:"Loading..."}),children:(0,n.jsx)(u,{})})}},9708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,TL:()=>i});var n=t(2115),s=t(6101),a=t(5155);function i(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...a}=e;if(n.isValidElement(t)){var i;let e,l,d=(i=t,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),o=function(e,r){let t={...r};for(let n in r){let s=e[n],a=r[n];/^on[A-Z]/.test(n)?s&&a?t[n]=(...e)=>{let r=a(...e);return s(...e),r}:s&&(t[n]=s):"style"===n?t[n]={...s,...a}:"className"===n&&(t[n]=[s,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==n.Fragment&&(o.ref=r?(0,s.t)(r,d):d),n.cloneElement(t,o)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:s,...i}=e,l=n.Children.toArray(s),d=l.find(o);if(d){let e=d.props.children,s=l.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,a.jsx)(r,{...i,ref:t,children:s})});return t.displayName=`${e}.Slot`,t}var l=i("Slot"),d=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}}},e=>{var r=r=>e(e.s=r);e.O(0,[277,441,684,358],()=>r(351)),_N_E=e.O()}]);