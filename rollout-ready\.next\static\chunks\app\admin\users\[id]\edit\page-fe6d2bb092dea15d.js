(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9766],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>o});var t=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:a,size:n,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:s})),...d})}},1686:(e,s,a)=>{Promise.resolve().then(a.bind(a,4723))},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(5155);a(2115);var r=a(9434);function n(e){let{className:s,type:a,...n}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},4695:(e,s,a)=>{"use strict";a.d(s,{default:()=>o});var t=a(5155),r=a(6874),n=a.n(r),i=a(7340);let l=(0,a(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function o(e){let{items:s,showHome:a=!0}=e;return(0,t.jsxs)("nav",{className:"flex items-center space-x-1 text-sm text-gray-500 mb-4",children:[a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(n(),{href:"/",className:"flex items-center hover:text-gray-700 transition-colors",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"ml-1",children:"Home"})]}),s.length>0&&(0,t.jsx)(l,{className:"h-4 w-4"})]}),s.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[e.href?(0,t.jsx)(n(),{href:e.href,className:"hover:text-gray-700 transition-colors",children:e.label}):(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:e.label}),a<s.length-1&&(0,t.jsx)(l,{className:"h-4 w-4 ml-1"})]},a))]})}},4723:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var t=a(5155),r=a(2115),n=a(5695),i=a(6874),l=a.n(i),o=a(285),d=a(6695),c=a(2523),u=a(8979),m=a(9409),h=a(9434);let p=r.forwardRef((e,s)=>{let{className:a,checked:r,onCheckedChange:n,...i}=e;return(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",className:"sr-only peer",ref:s,checked:r,onChange:e=>null==n?void 0:n(e.target.checked),...i}),(0,t.jsx)("div",{className:(0,h.cn)("relative w-11 h-6 bg-gray-200 rounded-full peer peer-checked:bg-blue-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 transition-colors","after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all","peer-checked:after:translate-x-full peer-checked:after:border-white",a)})]})});p.displayName="Switch";var x=a(5182),f=a(4695);function g(e){let{params:s}=e,a=(0,n.useRouter)(),[i,h]=(0,r.useState)(null),[g,v]=(0,r.useState)(!1),[b,j]=(0,r.useState)(""),[w,y]=(0,r.useState)([]),[N,P]=(0,r.useState)(null),[k,R]=(0,r.useState)({username:"",email:"",firstName:"",lastName:"",systemRole:"USER",jobRoleId:"none",isActive:!0}),[C,A]=(0,r.useState)({newPassword:"",confirmPassword:""}),[S,F]=(0,r.useState)(!1),[z,_]=(0,r.useState)("");(0,r.useEffect)(()=>{(async()=>{let e=parseInt((await s).id);isNaN(e)&&(0,n.notFound)(),h(e)})()},[s]),(0,r.useEffect)(()=>{i&&(async()=>{try{var e;let s=await fetch("/api/users/".concat(i));if(!s.ok)throw 404===s.status&&(0,n.notFound)(),Error("Failed to fetch user");let a=await s.json();P(a),R({username:a.username||"",email:a.email||"",firstName:a.firstName||"",lastName:a.lastName||"",systemRole:a.systemRole||"USER",jobRoleId:a.jobRoleId?a.jobRoleId.toString():"none",isActive:null==(e=a.isActive)||e});let t=await fetch("/api/roles");if(t.ok){let e=await t.json();y(e)}}catch(e){console.error("Failed to fetch data:",e),j("Failed to load user data")}})()},[i]);let E=e=>{let{name:s,value:a}=e.target;R(e=>({...e,[s]:a}))},U=e=>{let{name:s,value:a}=e.target;A(e=>({...e,[s]:a})),_("")},J=async()=>{if(i){if(!C.newPassword||C.newPassword.length<6)return void _("Password must be at least 6 characters long");if(C.newPassword!==C.confirmPassword)return void _("Passwords do not match");v(!0),_("");try{let e=await fetch("/api/users/".concat(i,"/reset-password"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({newPassword:C.newPassword})}),s=await e.json();e.ok?(A({newPassword:"",confirmPassword:""}),F(!1),alert("Password reset successfully! The user can now login with the new password.")):_(s.message||"Failed to reset password")}catch(e){_("An error occurred. Please try again.")}finally{v(!1)}}},T=async()=>{if(i){v(!0),_("");try{let e=await fetch("/api/users/".concat(i,"/reset-password"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({generateRandom:!0})}),s=await e.json();e.ok?(A({newPassword:"",confirmPassword:""}),F(!1),alert("Password reset successfully!\n\nTemporary Password: ".concat(s.temporaryPassword,"\n\nPlease share this password securely with the user. They should change it after first login."))):_(s.message||"Failed to generate password")}catch(e){_("An error occurred. Please try again.")}finally{v(!1)}}},I=async e=>{if(e.preventDefault(),i){v(!0),j("");try{let e=await fetch("/api/users/".concat(i),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:k.username,email:k.email,firstName:k.firstName||null,lastName:k.lastName||null,systemRole:k.systemRole,jobRoleId:"none"===k.jobRoleId?null:parseInt(k.jobRoleId),isActive:k.isActive})}),s=await e.json();e.ok?a.push("/admin/users/".concat(i)):j(s.message||"Failed to update user")}catch(e){j("An error occurred. Please try again.")}finally{v(!1)}}},M=async()=>{if(confirm('Are you sure you want to deactivate the user "'.concat(N.username,'"? This will set their account to inactive.'))){v(!0),j("");try{let e=await fetch("/api/users/".concat(i),{method:"DELETE"});if(e.ok)a.push("/admin/users");else{let s=await e.json();j(s.message||"Failed to deactivate user")}}catch(e){j("An error occurred while deactivating the user")}finally{v(!1)}}};if(!N)return(0,t.jsx)("div",{children:"Loading..."});let $=N.firstName&&N.lastName?"".concat(N.firstName," ").concat(N.lastName):N.username;return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)(f.default,{items:[{label:"Admin",href:"/admin"},{label:"Users",href:"/admin/users"},{label:$,href:"/admin/users/".concat(i)},{label:"Edit"}]}),(0,t.jsx)(x.default,{title:"Edit User: ".concat($),description:"Update user information and permissions",backUrl:"/admin/users/".concat(i)}),(0,t.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"User Information"}),(0,t.jsx)(d.BT,{children:"Update user details and credentials"})]}),(0,t.jsxs)(d.Wu,{className:"space-y-6",children:[b&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:b}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"firstName",children:"First Name"}),(0,t.jsx)(c.p,{id:"firstName",name:"firstName",type:"text",value:k.firstName,onChange:E,placeholder:"John"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"lastName",children:"Last Name"}),(0,t.jsx)(c.p,{id:"lastName",name:"lastName",type:"text",value:k.lastName,onChange:E,placeholder:"Doe"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"username",children:"Username *"}),(0,t.jsx)(c.p,{id:"username",name:"username",type:"text",required:!0,value:k.username,onChange:E,placeholder:"johndoe"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"email",children:"Email *"}),(0,t.jsx)(c.p,{id:"email",name:"email",type:"email",required:!0,value:k.email,onChange:E,placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"systemRole",children:"System Role *"}),(0,t.jsxs)(m.l6,{value:k.systemRole,onValueChange:e=>{R(s=>({...s,systemRole:e}))},children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"Select a system role"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"USER",children:"User - Can view and update assigned tasks"}),(0,t.jsx)(m.eb,{value:"MANAGER",children:"Manager - Can create projects and assign users"}),(0,t.jsx)(m.eb,{value:"ADMIN",children:"Admin - Full system access"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"jobRole",children:"Job Role"}),(0,t.jsxs)(m.l6,{value:k.jobRoleId,onValueChange:e=>{R(s=>({...s,jobRoleId:e}))},children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"Select a job role (optional)"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"none",children:"No specific job role"}),w.map(e=>(0,t.jsxs)(m.eb,{value:e.id.toString(),children:[e.name,e.description&&" - ".concat(e.description)]},e.id))]})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Job role determines which project roles this user can be assigned to"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p,{id:"isActive",checked:k.isActive,onCheckedChange:e=>{R(s=>({...s,isActive:e}))}}),(0,t.jsx)(u.J,{htmlFor:"isActive",children:"Account is active"})]})]})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsxs)(d.ZB,{className:"flex items-center justify-between",children:["Password Management",(0,t.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>F(!S),children:S?"Cancel":"Reset Password"})]}),(0,t.jsx)(d.BT,{children:"Reset the user's password. The user will need to use the new password to login."})]}),S&&(0,t.jsxs)(d.Wu,{className:"space-y-4",children:[z&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:z}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"newPassword",children:"New Password *"}),(0,t.jsx)(c.p,{id:"newPassword",name:"newPassword",type:"password",value:C.newPassword,onChange:U,placeholder:"Enter new password",minLength:6}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Minimum 6 characters"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"confirmPassword",children:"Confirm New Password *"}),(0,t.jsx)(c.p,{id:"confirmPassword",name:"confirmPassword",type:"password",value:C.confirmPassword,onChange:U,placeholder:"Confirm new password",minLength:6})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,t.jsx)(o.$,{type:"button",onClick:J,disabled:g||!C.newPassword||!C.confirmPassword,variant:"destructive",children:g?"Resetting...":"Set Password"}),(0,t.jsx)(o.$,{type:"button",onClick:T,disabled:g,variant:"secondary",children:g?"Generating...":"Generate Random Password"}),(0,t.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>{F(!1),A({newPassword:"",confirmPassword:""}),_("")},children:"Cancel"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm text-blue-800",children:(0,t.jsx)("strong",{children:"Options:"})}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 mt-1 space-y-1",children:[(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Set Password:"})," Enter a specific password for the user"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Generate Random:"})," Create a secure random password automatically"]})]})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(o.$,{type:"submit",disabled:g,children:g?"Updating...":"Update User"}),(0,t.jsx)(l(),{href:"/admin/users/".concat(i),children:(0,t.jsx)(o.$,{type:"button",variant:"outline",children:"Cancel"})}),(0,t.jsx)(o.$,{type:"button",variant:"destructive",onClick:M,disabled:g,children:g?"Deactivating...":"Deactivate User"})]})]})]})}},5182:(e,s,a)=>{"use strict";a.d(s,{default:()=>c});var t=a(5155),r=a(5695),n=a(285);let i=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var l=a(7340),o=a(6874),d=a.n(o);function c(e){let{title:s,description:a,showBackButton:o=!0,backUrl:c,showHomeButton:u=!1,children:m}=e,h=(0,r.useRouter)();return(0,t.jsxs)("div",{className:"space-y-4",children:[(o||u)&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[o&&(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{c?h.push(c):h.back()},className:"flex items-center gap-2",children:[(0,t.jsx)(i,{className:"h-4 w-4"}),"Back"]}),u&&(0,t.jsx)(d(),{href:"/",children:(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),"Home"]})})]}),(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s}),a&&(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:a})]}),m&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:m})]})]})}},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"notFound")&&a.d(s,{notFound:function(){return t.notFound}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i});var t=a(5155);a(2115);var r=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}},7340:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},8979:(e,s,a)=>{"use strict";a.d(s,{J:()=>o});var t=a(5155),r=a(2115),n=a(3655),i=r.forwardRef((e,s)=>(0,t.jsx)(n.sG.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var l=a(9434);function o(e){let{className:s,...a}=e;return(0,t.jsx)(i,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},9409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>u,eb:()=>h,gC:()=>m,l6:()=>d,yv:()=>c});var t=a(5155);a(2115);var r=a(1396),n=a(6474),i=a(5196),l=a(7863),o=a(9434);function d(e){let{...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"select",...s})}function c(e){let{...s}=e;return(0,t.jsx)(r.WT,{"data-slot":"select-value",...s})}function u(e){let{className:s,size:a="default",children:i,...l}=e;return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...l,children:[i,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:s,children:a,position:n="popper",...i}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,t.jsx)(p,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(x,{})]})})}function h(e){let{className:s,children:a,...n}=e;return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...n,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(i.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function p(e){let{className:s,...a}=e;return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,t.jsx)(l.A,{className:"size-4"})})}function x(e){let{className:s,...a}=e;return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,t.jsx)(n.A,{className:"size-4"})})}},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>n});var t=a(2596),r=a(9688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,6874,3537,8441,1684,7358],()=>s(1686)),_N_E=e.O()}]);