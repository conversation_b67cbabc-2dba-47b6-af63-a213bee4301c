{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/UserPicker.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface User {\n  id: number;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  systemRole: string;\n  jobRoleId?: number;\n  jobRole?: {\n    id: number;\n    name: string;\n    description?: string;\n  };\n  isActive: boolean;\n}\n\ninterface UserPickerProps {\n  value?: number;\n  onValueChange: (userId: number | undefined) => void;\n  placeholder?: string;\n  disabled?: boolean;\n  excludeUserIds?: number[];\n  projectRoleName?: string; // Filter users based on project role\n}\n\nexport default function UserPicker({\n  value,\n  onValueChange,\n  placeholder = \"Select a user\",\n  disabled = false,\n  excludeUserIds = [],\n  projectRoleName\n}: UserPickerProps) {\n  const [users, setUsers] = useState<User[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    fetchUsers();\n  }, [projectRoleName, excludeUserIds]);\n\n  // Filter users based on project role name - should match job role\n  const shouldIncludeUser = (user: User, projectRoleName: string): boolean => {\n    // If user has a job role, it should match the project role name\n    if (user.jobRole) {\n      return user.jobRole.name.toLowerCase() === projectRoleName.toLowerCase();\n    }\n\n    // If user has no job role, fall back to system role filtering for backward compatibility\n    const roleName = projectRoleName.toLowerCase();\n    if (roleName.includes('manager') || roleName.includes('lead') || roleName.includes('director')) {\n      return ['MANAGER', 'ADMIN'].includes(user.systemRole);\n    }\n\n    // For specialist roles, allow USER system role users without specific job roles\n    return user.systemRole === 'USER';\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const response = await fetch(\"/api/users\");\n      if (response.ok) {\n        const data = await response.json();\n\n        // Filter users based on active status, exclusions, and role matching\n        let filteredUsers = data.filter((user: User) =>\n          user.isActive && !excludeUserIds.includes(user.id)\n        );\n\n        // Apply role filtering if projectRoleName is provided\n        if (projectRoleName) {\n          filteredUsers = filteredUsers.filter((user: User) =>\n            shouldIncludeUser(user, projectRoleName)\n          );\n        }\n\n        setUsers(filteredUsers);\n      } else {\n        setError(\"Failed to load users\");\n      }\n    } catch (err) {\n      setError(\"Error loading users\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleValueChange = (stringValue: string) => {\n    if (stringValue === \"none\") {\n      onValueChange(undefined);\n    } else {\n      const userId = parseInt(stringValue);\n      onValueChange(userId);\n    }\n  };\n\n  const getDisplayName = (user: User) => {\n    if (user.firstName && user.lastName) {\n      return `${user.firstName} ${user.lastName} (@${user.username})`;\n    }\n    return `@${user.username}`;\n  };\n\n  const getRoleBadgeVariant = (role: string) => {\n    switch (role) {\n      case 'ADMIN':\n        return 'destructive';\n      case 'MANAGER':\n        return 'default';\n      case 'USER':\n        return 'secondary';\n      default:\n        return 'outline';\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Select disabled>\n        <SelectTrigger>\n          <SelectValue placeholder=\"Loading users...\" />\n        </SelectTrigger>\n      </Select>\n    );\n  }\n\n  if (error) {\n    return (\n      <Select disabled>\n        <SelectTrigger>\n          <SelectValue placeholder={error} />\n        </SelectTrigger>\n      </Select>\n    );\n  }\n\n  // Get appropriate placeholder text\n  const getPlaceholderText = () => {\n    if (projectRoleName) {\n      return `Select user for ${projectRoleName} role`;\n    }\n    return placeholder;\n  };\n\n  return (\n    <Select \n      value={value ? value.toString() : \"none\"} \n      onValueChange={handleValueChange}\n      disabled={disabled}\n    >\n      <SelectTrigger>\n        <SelectValue placeholder={getPlaceholderText()} />\n      </SelectTrigger>\n      <SelectContent>\n        <SelectItem value=\"none\">\n          <span className=\"text-gray-500\">No user assigned</span>\n        </SelectItem>\n        {users.map((user) => (\n          <SelectItem key={user.id} value={user.id.toString()}>\n            <div className=\"flex items-center justify-between w-full\">\n              <div className=\"flex flex-col\">\n                <span>{getDisplayName(user)}</span>\n                {user.jobRole && (\n                  <span className=\"text-xs text-gray-500\">{user.jobRole.name}</span>\n                )}\n              </div>\n              <div className=\"flex gap-1\">\n                {user.jobRole && (\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    {user.jobRole.name}\n                  </Badge>\n                )}\n                <Badge\n                  variant={getRoleBadgeVariant(user.systemRole)}\n                  className=\"text-xs\"\n                >\n                  {user.systemRole}\n                </Badge>\n              </div>\n            </div>\n          </SelectItem>\n        ))}\n      </SelectContent>\n    </Select>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AA+Be,SAAS,WAAW,EACjC,KAAK,EACL,aAAa,EACb,cAAc,eAAe,EAC7B,WAAW,KAAK,EAChB,iBAAiB,EAAE,EACnB,eAAe,EACC;IAChB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAiB;KAAe;IAEpC,kEAAkE;IAClE,MAAM,oBAAoB,CAAC,MAAY;QACrC,gEAAgE;QAChE,IAAI,KAAK,OAAO,EAAE;YAChB,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,gBAAgB,WAAW;QACxE;QAEA,yFAAyF;QACzF,MAAM,WAAW,gBAAgB,WAAW;QAC5C,IAAI,SAAS,QAAQ,CAAC,cAAc,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,aAAa;YAC9F,OAAO;gBAAC;gBAAW;aAAQ,CAAC,QAAQ,CAAC,KAAK,UAAU;QACtD;QAEA,gFAAgF;QAChF,OAAO,KAAK,UAAU,KAAK;IAC7B;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,qEAAqE;gBACrE,IAAI,gBAAgB,KAAK,MAAM,CAAC,CAAC,OAC/B,KAAK,QAAQ,IAAI,CAAC,eAAe,QAAQ,CAAC,KAAK,EAAE;gBAGnD,sDAAsD;gBACtD,IAAI,iBAAiB;oBACnB,gBAAgB,cAAc,MAAM,CAAC,CAAC,OACpC,kBAAkB,MAAM;gBAE5B;gBAEA,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,gBAAgB,QAAQ;YAC1B,cAAc;QAChB,OAAO;YACL,MAAM,SAAS,SAAS;YACxB,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,EAAE;YACnC,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;QACjE;QACA,OAAO,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;IAC5B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,QAAQ;sBACd,cAAA,8OAAC,kIAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oBAAC,aAAY;;;;;;;;;;;;;;;;IAIjC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,QAAQ;sBACd,cAAA,8OAAC,kIAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oBAAC,aAAa;;;;;;;;;;;;;;;;IAIlC;IAEA,mCAAmC;IACnC,MAAM,qBAAqB;QACzB,IAAI,iBAAiB;YACnB,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,KAAK,CAAC;QAClD;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,OAAO,QAAQ,MAAM,QAAQ,KAAK;QAClC,eAAe;QACf,UAAU;;0BAEV,8OAAC,kIAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oBAAC,aAAa;;;;;;;;;;;0BAE5B,8OAAC,kIAAA,CAAA,gBAAa;;kCACZ,8OAAC,kIAAA,CAAA,aAAU;wBAAC,OAAM;kCAChB,cAAA,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;oBAEjC,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,kIAAA,CAAA,aAAU;4BAAe,OAAO,KAAK,EAAE,CAAC,QAAQ;sCAC/C,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAM,eAAe;;;;;;4CACrB,KAAK,OAAO,kBACX,8OAAC;gDAAK,WAAU;0DAAyB,KAAK,OAAO,CAAC,IAAI;;;;;;;;;;;;kDAG9D,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO,kBACX,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAChC,KAAK,OAAO,CAAC,IAAI;;;;;;0DAGtB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,oBAAoB,KAAK,UAAU;gDAC5C,WAAU;0DAET,KAAK,UAAU;;;;;;;;;;;;;;;;;;2BAlBP,KAAK,EAAE;;;;;;;;;;;;;;;;;AA2BlC", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/admin/projects/new/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport UserPicker from \"@/components/UserPicker\";\n\ninterface Role {\n  id: number;\n  name: string;\n  description: string;\n}\n\nexport default function NewProjectPage() {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    description: \"\",\n    startDate: \"\",\n  });\n  const [roleAssignments, setRoleAssignments] = useState<Record<number, number | undefined>>({});\n\n  useEffect(() => {\n    fetchRoles();\n  }, []);\n\n  const fetchRoles = async () => {\n    try {\n      const response = await fetch(\"/api/roles\");\n      if (response.ok) {\n        const rolesData = await response.json();\n        setRoles(rolesData);\n      }\n    } catch (error) {\n      console.error(\"Error fetching roles:\", error);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    try {\n      const projectData = {\n        ...formData,\n        roleAssignments,\n      };\n\n      const response = await fetch(\"/api/projects\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(projectData),\n      });\n\n      if (response.ok) {\n        router.push(\"/admin\");\n      } else {\n        const error = await response.json();\n        alert(`Error: ${error.message}`);\n      }\n    } catch {\n      alert(\"An error occurred while creating the project\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n  };\n\n  const handleRoleAssignment = (roleId: number, userId: number | undefined) => {\n    setRoleAssignments({\n      ...roleAssignments,\n      [roleId]: userId,\n    });\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Create New Project</h1>\n        <p className=\"text-gray-600 mt-2\">\n          Set up a new implementation project with templates and team assignments\n        </p>\n        <div className=\"mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <h3 className=\"font-semibold text-blue-800 mb-2\">Project Creation Workflow:</h3>\n          <ol className=\"list-decimal list-inside text-sm text-blue-700 space-y-1\">\n            <li>Define project details (name, description, start date)</li>\n            <li>Assign team members to project roles</li>\n            <li>System automatically applies relevant task templates</li>\n            <li>Tasks are generated based on role assignments and templates</li>\n          </ol>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Project Details */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Project Details</CardTitle>\n            <CardDescription>\n              Basic information about the project\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\">Project Name *</Label>\n              <Input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={handleChange}\n                placeholder=\"e.g., Deploy MES at Avonmouth\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Description</Label>\n              <textarea\n                id=\"description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleChange}\n                placeholder=\"Brief description of the project\"\n                className=\"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"startDate\">Start Date *</Label>\n              <Input\n                id=\"startDate\"\n                name=\"startDate\"\n                type=\"date\"\n                required\n                value={formData.startDate}\n                onChange={handleChange}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Role Assignments */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Team Assignments</CardTitle>\n            <CardDescription>\n              Assign team members to project roles. Task templates will be automatically applied based on these assignments.\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {roles.length === 0 ? (\n              <p className=\"text-gray-500\">No roles available. Please create roles first.</p>\n            ) : (\n              <div className=\"space-y-4\">\n                {roles.map((role) => (\n                  <div key={role.id} className=\"flex items-center gap-4 p-4 border rounded-lg\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold\">{role.name}</h3>\n                      <p className=\"text-sm text-gray-600\">{role.description}</p>\n                    </div>\n                    <div className=\"w-64\">\n                      <UserPicker\n                        value={roleAssignments[role.id]}\n                        onValueChange={(userId) => handleRoleAssignment(role.id, userId)}\n                        placeholder={`Select user for ${role.name}`}\n                        excludeUserIds={Object.values(roleAssignments).filter(id => id !== undefined && id !== roleAssignments[role.id]) as number[]}\n                        projectRoleName={role.name}\n                      />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Submit */}\n        <div className=\"flex gap-4\">\n          <Button type=\"submit\" disabled={isLoading}>\n            {isLoading ? \"Creating...\" : \"Create Project\"}\n          </Button>\n          <Link href=\"/admin\">\n            <Button type=\"button\" variant=\"outline\">\n              Cancel\n            </Button>\n          </Link>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,WAAW;IACb;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC,CAAC;IAE5F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE;YACjC;QACF,EAAE,OAAM;YACN,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,uBAAuB,CAAC,QAAgB;QAC5C,mBAAmB;YACjB,GAAG,eAAe;YAClB,CAAC,OAAO,EAAE;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAKV,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,SAAS;gDACzB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACT,MAAM,MAAM,KAAK,kBAChB,8OAAC;oCAAE,WAAU;8CAAgB;;;;;yDAE7B,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAiB,KAAK,IAAI;;;;;;sEACxC,8OAAC;4DAAE,WAAU;sEAAyB,KAAK,WAAW;;;;;;;;;;;;8DAExD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gIAAA,CAAA,UAAU;wDACT,OAAO,eAAe,CAAC,KAAK,EAAE,CAAC;wDAC/B,eAAe,CAAC,SAAW,qBAAqB,KAAK,EAAE,EAAE;wDACzD,aAAa,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;wDAC3C,gBAAgB,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,CAAA,KAAM,OAAO,aAAa,OAAO,eAAe,CAAC,KAAK,EAAE,CAAC;wDAC/G,iBAAiB,KAAK,IAAI;;;;;;;;;;;;2CAXtB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;kCAsB3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU;0CAC7B,YAAY,gBAAgB;;;;;;0CAE/B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD", "debugId": null}}]}