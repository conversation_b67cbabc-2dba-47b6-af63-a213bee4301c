"use strict";exports.id=425,exports.ids=[425],exports.modules={2688:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(3210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:i="",children:l,iconNode:s,...d},f)=>(0,r.createElement)("svg",{ref:f,...c,width:t,height:t,stroke:e,strokeWidth:o?24*Number(n)/Number(t):n,className:a("lucide",i),...!l&&!u(d)&&{"aria-hidden":"true"},...d},[...s.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(l)?l:[l]])),d=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...i},u)=>(0,r.createElement)(s,{ref:u,iconNode:t,className:a(`lucide-${o(l(e))}`,`lucide-${e}`,n),...i}));return n.displayName=l(e),n}},3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},4163:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(3210),o=n(1215),i=n(8730),l=n(687),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5891:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6725:(e,t,n)=>{n.d(t,{UC:()=>n4,In:()=>n8,q7:()=>n7,VF:()=>rt,p4:()=>re,ZL:()=>n3,bL:()=>n2,wn:()=>rr,PP:()=>rn,l9:()=>n5,WT:()=>n6,LM:()=>n9});var r,o,i,l=n(3210),a=n.t(l,2),u=n(1215);function c(e,[t,n]){return Math.min(n,Math.max(t,e))}function s(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var d=n(687);function f(e,t=[]){let n=[],r=()=>{let t=n.map(e=>l.createContext(e));return function(n){let r=n?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=l.createContext(r),i=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[i]||o,c=l.useMemo(()=>a,Object.values(a));return(0,d.jsx)(u.Provider,{value:c,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[i]||o,c=l.useContext(u);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var p=n(8599),h=n(8730),m=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=g(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function g(e){return e!=e||0===e?0:Math.trunc(e)}var y=l.createContext(void 0),w=n(4163);function x(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var b="dismissableLayer.update",E=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),S=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:u,onDismiss:c,...f}=e,h=l.useContext(E),[m,v]=l.useState(null),g=m?.ownerDocument??globalThis?.document,[,y]=l.useState({}),S=(0,p.s)(t,e=>v(e)),A=Array.from(h.layers),[T]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),L=A.indexOf(T),P=m?A.indexOf(m):-1,k=h.layersWithOutsidePointerEventsDisabled.size>0,N=P>=L,M=function(e,t=globalThis?.document){let n=x(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){R("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));N&&!n&&(i?.(e),u?.(e),e.defaultPrevented||c?.())},g),j=function(e,t=globalThis?.document){let n=x(e),r=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!r.current&&R("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(a?.(e),u?.(e),e.defaultPrevented||c?.())},g);return!function(e,t=globalThis?.document){let n=x(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===h.layers.size-1&&(r?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},g),l.useEffect(()=>{if(m)return n&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(o=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(m)),h.layers.add(m),C(),()=>{n&&1===h.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=o)}},[m,g,n,h]),l.useEffect(()=>()=>{m&&(h.layers.delete(m),h.layersWithOutsidePointerEventsDisabled.delete(m),C())},[m,h]),l.useEffect(()=>{let e=()=>y({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)},[]),(0,d.jsx)(w.sG.div,{...f,ref:S,style:{pointerEvents:k?N?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,j.onFocusCapture),onBlurCapture:s(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,M.onPointerDownCapture)})});function C(){let e=new CustomEvent(b);document.dispatchEvent(e)}function R(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,w.hO)(o,i):o.dispatchEvent(i)}S.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(E),r=l.useRef(null),o=(0,p.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,d.jsx)(w.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var A=0;function T(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var L="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",k={bubbles:!1,cancelable:!0},N=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[u,c]=l.useState(null),s=x(o),f=x(i),h=l.useRef(null),m=(0,p.s)(t,e=>c(e)),v=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(v.paused||!u)return;let t=e.target;u.contains(t)?h.current=t:O(h.current,{select:!0})},t=function(e){if(v.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||O(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&O(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,v.paused]),l.useEffect(()=>{if(u){D.add(v);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(L,k);u.addEventListener(L,s),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(O(r,{select:t}),document.activeElement!==n)return}(M(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&O(u))}return()=>{u.removeEventListener(L,s),setTimeout(()=>{let t=new CustomEvent(P,k);u.addEventListener(P,f),u.dispatchEvent(t),t.defaultPrevented||O(e??document.body,{select:!0}),u.removeEventListener(P,f),D.remove(v)},0)}}},[u,s,f,v]);let g=l.useCallback(e=>{if(!n&&!r||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=M(e);return[j(t,e),j(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&O(i,{select:!0})):(e.preventDefault(),n&&O(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,v.paused]);return(0,d.jsx)(w.sG.div,{tabIndex:-1,...a,ref:m,onKeyDown:g})});function M(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function j(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function O(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}N.displayName="FocusScope";var D=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=I(e,t)).unshift(t)},remove(t){e=I(e,t),e[0]?.resume()}}}();function I(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var W=globalThis?.document?l.useLayoutEffect:()=>{},F=a[" useId ".trim().toString()]||(()=>void 0),H=0;function B(e){let[t,n]=l.useState(F());return W(()=>{e||n(e=>e??String(H++))},[e]),e||(t?`radix-${t}`:"")}let _=["top","right","bottom","left"],$=Math.min,z=Math.max,V=Math.round,G=Math.floor,K=e=>({x:e,y:e}),U={left:"right",right:"left",bottom:"top",top:"bottom"},Y={start:"end",end:"start"};function X(e,t){return"function"==typeof e?e(t):e}function q(e){return e.split("-")[0]}function Z(e){return e.split("-")[1]}function J(e){return"x"===e?"y":"x"}function Q(e){return"y"===e?"height":"width"}function ee(e){return["top","bottom"].includes(q(e))?"y":"x"}function et(e){return e.replace(/start|end/g,e=>Y[e])}function en(e){return e.replace(/left|right|bottom|top/g,e=>U[e])}function er(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function eo(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ei(e,t,n){let r,{reference:o,floating:i}=e,l=ee(t),a=J(ee(t)),u=Q(a),c=q(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(Z(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let el=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=ei(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=ei(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function ea(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=X(t,e),h=er(p),m=a[f?"floating"===d?"reference":"floating":d],v=eo(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=eo(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function eu(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ec(e){return _.some(t=>e[t]>=0)}async function es(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=q(n),a=Z(n),u="y"===ee(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,d=X(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function ed(){return"undefined"!=typeof window}function ef(e){return em(e)?(e.nodeName||"").toLowerCase():"#document"}function ep(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eh(e){var t;return null==(t=(em(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function em(e){return!!ed()&&(e instanceof Node||e instanceof ep(e).Node)}function ev(e){return!!ed()&&(e instanceof Element||e instanceof ep(e).Element)}function eg(e){return!!ed()&&(e instanceof HTMLElement||e instanceof ep(e).HTMLElement)}function ey(e){return!!ed()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ep(e).ShadowRoot)}function ew(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eC(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ex(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eb(e){let t=eE(),n=ev(e)?eC(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function eE(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eS(e){return["html","body","#document"].includes(ef(e))}function eC(e){return ep(e).getComputedStyle(e)}function eR(e){return ev(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eA(e){if("html"===ef(e))return e;let t=e.assignedSlot||e.parentNode||ey(e)&&e.host||eh(e);return ey(t)?t.host:t}function eT(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eA(t);return eS(n)?t.ownerDocument?t.ownerDocument.body:t.body:eg(n)&&ew(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=ep(o);if(i){let e=eL(l);return t.concat(l,l.visualViewport||[],ew(o)?o:[],e&&n?eT(e):[])}return t.concat(o,eT(o,[],n))}function eL(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eP(e){let t=eC(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eg(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=V(n)!==i||V(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function ek(e){return ev(e)?e:e.contextElement}function eN(e){let t=ek(e);if(!eg(t))return K(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eP(t),l=(i?V(n.width):n.width)/r,a=(i?V(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eM=K(0);function ej(e){let t=ep(e);return eE()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eM}function eO(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=ek(e),a=K(1);t&&(r?ev(r)&&(a=eN(r)):a=eN(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ep(l))&&o)?ej(l):K(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=ep(l),t=r&&ev(r)?ep(r):r,n=e,o=eL(n);for(;o&&r&&t!==n;){let e=eN(o),t=o.getBoundingClientRect(),r=eC(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eL(n=ep(o))}}return eo({width:d,height:f,x:c,y:s})}function eD(e,t){let n=eR(e).scrollLeft;return t?t.left+n:eO(eh(e)).left+n}function eI(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eD(e,r)),y:r.top+t.scrollTop}}function eW(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ep(e),r=eh(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eE();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eh(e),n=eR(e),r=e.ownerDocument.body,o=z(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=z(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eD(e),a=-n.scrollTop;return"rtl"===eC(r).direction&&(l+=z(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eh(e));else if(ev(t))r=function(e,t){let n=eO(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eg(e)?eN(e):K(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=ej(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return eo(r)}function eF(e){return"static"===eC(e).position}function eH(e,t){if(!eg(e)||"fixed"===eC(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eh(e)===n&&(n=n.ownerDocument.body),n}function eB(e,t){let n=ep(e);if(ex(e))return n;if(!eg(e)){let t=eA(e);for(;t&&!eS(t);){if(ev(t)&&!eF(t))return t;t=eA(t)}return n}let r=eH(e,t);for(;r&&["table","td","th"].includes(ef(r))&&eF(r);)r=eH(r,t);return r&&eS(r)&&eF(r)&&!eb(r)?n:r||function(e){let t=eA(e);for(;eg(t)&&!eS(t);){if(eb(t))return t;if(ex(t))break;t=eA(t)}return null}(e)||n}let e_=async function(e){let t=this.getOffsetParent||eB,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eg(t),o=eh(t),i="fixed"===n,l=eO(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=K(0);if(r||!r&&!i)if(("body"!==ef(t)||ew(o))&&(a=eR(t)),r){let e=eO(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eD(o));i&&!r&&o&&(u.x=eD(o));let c=!o||r||i?K(0):eI(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},e$={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eh(r),a=!!t&&ex(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=K(1),s=K(0),d=eg(r);if((d||!d&&!i)&&(("body"!==ef(r)||ew(l))&&(u=eR(r)),eg(r))){let e=eO(r);c=eN(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?K(0):eI(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:eh,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ex(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eT(e,[],!1).filter(e=>ev(e)&&"body"!==ef(e)),o=null,i="fixed"===eC(e).position,l=i?eA(e):e;for(;ev(l)&&!eS(l);){let t=eC(l),n=eb(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||ew(l)&&!n&&function e(t,n){let r=eA(t);return!(r===n||!ev(r)||eS(r))&&("fixed"===eC(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eA(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eW(t,n,o);return e.top=z(r.top,e.top),e.right=$(r.right,e.right),e.bottom=$(r.bottom,e.bottom),e.left=z(r.left,e.left),e},eW(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eB,getElementRects:e_,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eP(e);return{width:t,height:n}},getScale:eN,isElement:ev,isRTL:function(e){return"rtl"===eC(e).direction}};function ez(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eV=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=X(e,t)||{};if(null==c)return{};let d=er(s),f={x:n,y:r},p=J(ee(o)),h=Q(p),m=await l.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),b=x?x[g]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[g]||i.floating[h]);let E=b/2-m[h]/2-1,S=$(d[v?"top":"left"],E),C=$(d[v?"bottom":"right"],E),R=b-m[h]-C,A=b/2-m[h]/2+(y/2-w/2),T=z(S,$(A,R)),L=!u.arrow&&null!=Z(o)&&A!==T&&i.reference[h]/2-(A<S?S:C)-m[h]/2<0,P=L?A<S?A-S:A-R:0;return{[p]:f[p]+P,data:{[p]:T,centerOffset:A-T-P,...L&&{alignmentOffset:P}},reset:L}}}),eG=(e,t,n)=>{let r=new Map,o={platform:e$,...n},i={...o.platform,_c:r};return el(e,t,{...o,platform:i})};var eK="undefined"!=typeof document?l.useLayoutEffect:function(){};function eU(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eU(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eU(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eY(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eX(e,t){let n=eY(e);return Math.round(t*n)/n}function eq(e){let t=l.useRef(e);return eK(()=>{t.current=e}),t}let eZ=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eV({element:n.current,padding:r}).fn(t):{}:n?eV({element:n,padding:r}).fn(t):{}}}),eJ=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await es(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eQ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=X(e,t),c={x:n,y:r},s=await ea(t,u),d=ee(q(o)),f=J(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=z(n,$(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=z(n,$(h,r))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),e0=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=X(e,t),s={x:n,y:r},d=ee(o),f=J(d),p=s[f],h=s[d],m=X(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(q(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),e1=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=X(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=q(a),b=ee(s),E=q(s)===s,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=m||(E||!y?[en(s)]:function(e){let t=en(e);return[et(e),t,et(t)]}(s)),R="none"!==g;!m&&R&&C.push(...function(e,t,n,r){let o=Z(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(q(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(et)))),i}(s,y,g,S));let A=[s,...C],T=await ea(t,w),L=[],P=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&L.push(T[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=Z(e),o=J(ee(e)),i=Q(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=en(l)),[l,en(l)]}(a,c,S);L.push(T[e[0]],T[e[1]])}if(P=[...P,{placement:a,overflows:L}],!L.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=A[e];if(t&&("alignment"!==h||b===ee(t)||P.every(e=>e.overflows[0]>0&&ee(e.placement)===b)))return{data:{index:e,overflows:P},reset:{placement:t}};let n=null==(i=P.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=P.filter(e=>{if(R){let t=ee(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),e2=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=X(e,t),f=await ea(t,d),p=q(l),h=Z(l),m="y"===ee(l),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=v-f.left-f.right,x=$(g-f[o],y),b=$(v-f[i],w),E=!t.middlewareData.shift,S=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!h){let e=z(f.left,0),t=z(f.right,0),n=z(f.top,0),r=z(f.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:z(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:z(f.top,f.bottom))}await s({...t,availableWidth:C,availableHeight:S});let R=await u.getDimensions(c.floating);return v!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e5=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=X(e,t);switch(r){case"referenceHidden":{let e=eu(await ea(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ec(e)}}}case"escaped":{let e=eu(await ea(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ec(e)}}}default:return{}}}}}(e),options:[e,t]}),e6=(e,t)=>({...eZ(e),options:[e,t]});var e8=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,d.jsx)(w.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e8.displayName="Arrow";var e3="Popper",[e4,e9]=f(e3),[e7,te]=e4(e3),tt=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,d.jsx)(e7,{scope:t,anchor:r,onAnchorChange:o,children:n})};tt.displayName=e3;var tn="PopperAnchor",tr=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=te(tn,n),a=l.useRef(null),u=(0,p.s)(t,a);return l.useEffect(()=>{i.onAnchorChange(r?.current||a.current)}),r?null:(0,d.jsx)(w.sG.div,{...o,ref:u})});tr.displayName=tn;var to="PopperContent",[ti,tl]=e4(to),ta=l.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:s=!0,collisionBoundary:f=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...b}=e,E=te(to,n),[S,C]=l.useState(null),R=(0,p.s)(t,e=>C(e)),[A,T]=l.useState(null),L=function(e){let[t,n]=l.useState(void 0);return W(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(A),P=L?.width??0,k=L?.height??0,N="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(f)?f:[f],j=M.length>0,O={padding:N,boundary:M.filter(td),altBoundary:j},{refs:D,floatingStyles:I,placement:F,isPositioned:H,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=l.useState(r);eU(h,r)||m(r);let[v,g]=l.useState(null),[y,w]=l.useState(null),x=l.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=l.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||v,S=a||y,C=l.useRef(null),R=l.useRef(null),A=l.useRef(f),T=null!=s,L=eq(s),P=eq(o),k=eq(d),N=l.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};P.current&&(e.platform=P.current),eG(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};M.current&&!eU(A.current,t)&&(A.current=t,u.flushSync(()=>{p(t)}))})},[h,t,n,P,k]);eK(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let M=l.useRef(!1);eK(()=>(M.current=!0,()=>{M.current=!1}),[]),eK(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(L.current)return L.current(E,S,N);N()}},[E,S,N,L,T]);let j=l.useMemo(()=>({reference:C,floating:R,setReference:x,setFloating:b}),[x,b]),O=l.useMemo(()=>({reference:E,floating:S}),[E,S]),D=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eX(O.floating,f.x),r=eX(O.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...eY(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,O.floating,f.x,f.y]);return l.useMemo(()=>({...f,update:N,refs:j,elements:O,floatingStyles:D}),[f,N,j,O,D])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=ek(e),d=i||l?[...s?eT(s):[],...eT(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=eh(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=G(d),m=G(o.clientWidth-(s+f)),v={rootMargin:-h+"px "+-m+"px "+-G(o.clientHeight-(d+p))+"px "+-G(s)+"px",threshold:z(0,$(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||ez(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?eO(e):null;return c&&function t(){let r=eO(e);m&&!ez(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:E.anchor},middleware:[eJ({mainAxis:o+k,alignmentAxis:a}),s&&eQ({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?e0():void 0,...O}),s&&e1({...O}),e2({...O,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),A&&e6({element:A,padding:c}),tf({arrowWidth:P,arrowHeight:k}),v&&e5({strategy:"referenceHidden",...O})]}),[_,V]=tp(F),K=x(y);W(()=>{H&&K?.()},[H,K]);let U=B.arrow?.x,Y=B.arrow?.y,X=B.arrow?.centerOffset!==0,[q,Z]=l.useState();return W(()=>{S&&Z(window.getComputedStyle(S).zIndex)},[S]),(0,d.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:H?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(ti,{scope:n,placedSide:_,onArrowChange:T,arrowX:U,arrowY:Y,shouldHideArrow:X,children:(0,d.jsx)(w.sG.div,{"data-side":_,"data-align":V,...b,ref:R,style:{...b.style,animation:H?void 0:"none"}})})})});ta.displayName=to;var tu="PopperArrow",tc={top:"bottom",right:"left",bottom:"top",left:"right"},ts=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tl(tu,n),i=tc[o.placedSide];return(0,d.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(e8,{...r,ref:t,style:{...r.style,display:"block"}})})});function td(e){return null!==e}ts.displayName=tu;var tf=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,c]=tp(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===u?(p=i?s:`${d}px`,h=`${-a}px`):"top"===u?(p=i?s:`${d}px`,h=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,h=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+a}px`,h=i?s:`${f}px`),{data:{x:p,y:h}}}});function tp(e){let[t,n="center"]=e.split("-");return[t,n]}var th=l.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=l.useState(!1);W(()=>i(!0),[]);let a=n||o&&globalThis?.document?.body;return a?u.createPortal((0,d.jsx)(w.sG.div,{...r,ref:t}),a):null});th.displayName="Portal";var tm=a[" useInsertionEffect ".trim().toString()]||W;function tv({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return tm(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,l.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[u,e,i,a])]}Symbol("RADIX:SYNC_STATE");var tg=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,d.jsx)(w.sG.span,{...e,ref:t,style:{...tg,...e.style}})).displayName="VisuallyHidden";var ty=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tw=new WeakMap,tx=new WeakMap,tb={},tE=0,tS=function(e){return e&&(e.host||tS(e.parentNode))},tC=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tS(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tb[n]||(tb[n]=new WeakMap);var i=tb[n],l=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tw.get(e)||0)+1,c=(i.get(e)||0)+1;tw.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tx.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tE++,function(){l.forEach(function(e){var t=tw.get(e)-1,o=i.get(e)-1;tw.set(e,t),i.set(e,o),t||(tx.has(e)||e.removeAttribute(r),tx.delete(e)),o||e.removeAttribute(n)}),--tE||(tw=new WeakMap,tw=new WeakMap,tx=new WeakMap,tb={})}},tR=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||ty(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tC(r,o,n,"aria-hidden")):function(){return null}},tA=function(){return(tA=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tT(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tL=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tP="width-before-scroll-bar";function tk(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tN="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,tM=new WeakMap;function tj(e){return e}var tO=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=tj),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return i.options=tA({async:!0,ssr:!1},e),i}(),tD=function(){},tI=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),u=l.useState({onScrollCapture:tD,onWheelCapture:tD,onTouchMoveCapture:tD}),c=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,x=e.inert,b=e.allowPinchZoom,E=e.as,S=e.gapMode,C=tT(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[a,t],r=function(e){return n.forEach(function(t){return tk(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tN(function(){var e=tM.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tk(e,null)}),r.forEach(function(e){t.has(e)||tk(e,o)})}tM.set(i,n)},[n]),i),A=tA(tA({},C),c);return l.createElement(l.Fragment,null,m&&l.createElement(g,{sideCar:tO,removeScrollBar:h,shards:v,noRelative:y,noIsolation:w,inert:x,setCallbacks:s,allowPinchZoom:!!b,lockRef:a,gapMode:S}),d?l.cloneElement(l.Children.only(f),tA(tA({},A),{ref:R})):l.createElement(void 0===E?"div":E,tA({},A,{className:p,ref:R}),f))});tI.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tI.classNames={fullWidth:tP,zeroRight:tL};var tW=function(e){var t=e.sideCar,n=tT(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,tA({},n))};tW.isSideCarExport=!0;var tF=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tH=function(){var e=tF();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tB=function(){var e=tH();return function(t){return e(t.styles,t.dynamic),null}},t_={left:0,top:0,right:0,gap:0},t$=function(e){return parseInt(e||"",10)||0},tz=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t$(n),t$(r),t$(o)]},tV=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t_;var t=tz(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tG=tB(),tK="data-scroll-locked",tU=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(tK,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tL," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tP," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tL," .").concat(tL," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tP," .").concat(tP," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(tK,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},tY=function(){var e=parseInt(document.body.getAttribute(tK)||"0",10);return isFinite(e)?e:0},tX=function(){l.useEffect(function(){return document.body.setAttribute(tK,(tY()+1).toString()),function(){var e=tY()-1;e<=0?document.body.removeAttribute(tK):document.body.setAttribute(tK,e.toString())}},[])},tq=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;tX();var i=l.useMemo(function(){return tV(o)},[o]);return l.createElement(tG,{styles:tU(i,!t,o,n?"":"!important")})},tZ=!1;if("undefined"!=typeof window)try{var tJ=Object.defineProperty({},"passive",{get:function(){return tZ=!0,!0}});window.addEventListener("test",tJ,tJ),window.removeEventListener("test",tJ,tJ)}catch(e){tZ=!1}var tQ=!!tZ&&{passive:!1},t0=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},t1=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),t2(e,r)){var o=t5(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},t2=function(e,t){return"v"===e?t0(t,"overflowY"):t0(t,"overflowX")},t5=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},t6=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=t5(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&t2(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},t8=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},t3=function(e){return[e.deltaX,e.deltaY]},t4=function(e){return e&&"current"in e?e.current:e},t9=0,t7=[];let ne=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(t9++)[0],i=l.useState(tB)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(t4),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=t8(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=t1(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t1(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return t6(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(t7.length&&t7[t7.length-1]===i){var n="deltaY"in e?t3(e):t8(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(t4).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=t8(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,t3(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,t8(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return t7.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,tQ),document.addEventListener("touchmove",c,tQ),document.addEventListener("touchstart",d,tQ),function(){t7=t7.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,tQ),document.removeEventListener("touchmove",c,tQ),document.removeEventListener("touchstart",d,tQ)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(tq,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tO.useMedium(r),tW);var nt=l.forwardRef(function(e,t){return l.createElement(tI,tA({},e,{ref:t,sideCar:ne}))});nt.classNames=tI.classNames;var nn=[" ","Enter","ArrowUp","ArrowDown"],nr=[" ","Enter"],no="Select",[ni,nl,na]=function(e){let t=e+"CollectionProvider",[n,r]=f(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=l.useRef(null),i=l.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",c=(0,h.TL)(u),s=l.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(u,n),l=(0,p.s)(t,o.collectionRef);return(0,d.jsx)(c,{ref:l,children:r})});s.displayName=u;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,h.TL)(m),y=l.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=l.useRef(null),u=(0,p.s)(t,a),c=i(m,n);return l.useEffect(()=>(c.itemMap.set(a,{ref:a,...o}),()=>void c.itemMap.delete(a))),(0,d.jsx)(g,{...{[v]:""},ref:u,children:r})});return y.displayName=m,[{Provider:a,Slot:s,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(no),[nu,nc]=f(no,[na,e9]),ns=e9(),[nd,nf]=nu(no),[np,nh]=nu(no),nm=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:a,defaultValue:u,onValueChange:c,dir:s,name:f,autoComplete:p,disabled:h,required:m,form:v}=e,g=ns(t),[w,x]=l.useState(null),[b,E]=l.useState(null),[S,C]=l.useState(!1),R=function(e){let t=l.useContext(y);return e||t||"ltr"}(s),[A,T]=tv({prop:r,defaultProp:o??!1,onChange:i,caller:no}),[L,P]=tv({prop:a,defaultProp:u,onChange:c,caller:no}),k=l.useRef(null),N=!w||v||!!w.closest("form"),[M,j]=l.useState(new Set),O=Array.from(M).map(e=>e.props.value).join(";");return(0,d.jsx)(tt,{...g,children:(0,d.jsxs)(nd,{required:m,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:B(),value:L,onValueChange:P,open:A,onOpenChange:T,dir:R,triggerPointerDownPosRef:k,disabled:h,children:[(0,d.jsx)(ni.Provider,{scope:t,children:(0,d.jsx)(np,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{j(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{j(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,d.jsxs)(nJ,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:L,onChange:e=>P(e.target.value),disabled:h,form:v,children:[void 0===L?(0,d.jsx)("option",{value:""}):null,Array.from(M)]},O):null]})})};nm.displayName=no;var nv="SelectTrigger",ng=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=ns(n),a=nf(nv,n),u=a.disabled||r,c=(0,p.s)(t,a.onTriggerChange),f=nl(n),h=l.useRef("touch"),[m,v,g]=n0(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=n1(t,e,n);void 0!==r&&a.onValueChange(r.value)}),y=e=>{u||(a.onOpenChange(!0),g()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,d.jsx)(tr,{asChild:!0,...i,children:(0,d.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":nQ(a.value)?"":void 0,...o,ref:c,onClick:s(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&y(e)}),onPointerDown:s(o.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:s(o.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&nn.includes(e.key)&&(y(),e.preventDefault())})})})});ng.displayName=nv;var ny="SelectValue",nw=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nf(ny,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,f=(0,p.s)(t,u.onValueNodeChange);return W(()=>{c(s)},[c,s]),(0,d.jsx)(w.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:nQ(u.value)?(0,d.jsx)(d.Fragment,{children:l}):i})});nw.displayName=ny;var nx=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,d.jsx)(w.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nx.displayName="SelectIcon";var nb=e=>(0,d.jsx)(th,{asChild:!0,...e});nb.displayName="SelectPortal";var nE="SelectContent",nS=l.forwardRef((e,t)=>{let n=nf(nE,e.__scopeSelect),[r,o]=l.useState();return(W(()=>{o(new DocumentFragment)},[]),n.open)?(0,d.jsx)(nT,{...e,ref:t}):r?u.createPortal((0,d.jsx)(nC,{scope:e.__scopeSelect,children:(0,d.jsx)(ni.Slot,{scope:e.__scopeSelect,children:(0,d.jsx)("div",{children:e.children})})}),r):null});nS.displayName=nE;var[nC,nR]=nu(nE),nA=(0,h.TL)("SelectContent.RemoveScroll"),nT=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:a,side:u,sideOffset:c,align:f,alignOffset:h,arrowPadding:m,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:w,avoidCollisions:x,...b}=e,E=nf(nE,n),[C,R]=l.useState(null),[L,P]=l.useState(null),k=(0,p.s)(t,e=>R(e)),[M,j]=l.useState(null),[O,D]=l.useState(null),I=nl(n),[W,F]=l.useState(!1),H=l.useRef(!1);l.useEffect(()=>{if(C)return tR(C)},[C]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??T()),document.body.insertAdjacentElement("beforeend",e[1]??T()),A++,()=>{1===A&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),A--}},[]);let B=l.useCallback(e=>{let[t,...n]=I().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&L&&(L.scrollTop=0),n===r&&L&&(L.scrollTop=L.scrollHeight),n?.focus(),document.activeElement!==o))return},[I,L]),_=l.useCallback(()=>B([M,C]),[B,M,C]);l.useEffect(()=>{W&&_()},[W,_]);let{onOpenChange:$,triggerPointerDownPosRef:z}=E;l.useEffect(()=>{if(C){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():C.contains(n.target)||$(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[C,$,z]),l.useEffect(()=>{let e=()=>$(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[$]);let[V,G]=n0(e=>{let t=I().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=n1(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=l.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==E.value&&E.value===t||r)&&(j(e),r&&(H.current=!0))},[E.value]),U=l.useCallback(()=>C?.focus(),[C]),Y=l.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==E.value&&E.value===t||r)&&D(e)},[E.value]),X="popper"===r?nP:nL,q=X===nP?{side:u,sideOffset:c,align:f,alignOffset:h,arrowPadding:m,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:w,avoidCollisions:x}:{};return(0,d.jsx)(nC,{scope:n,content:C,viewport:L,onViewportChange:P,itemRefCallback:K,selectedItem:M,onItemLeave:U,itemTextRefCallback:Y,focusSelectedItem:_,selectedItemText:O,position:r,isPositioned:W,searchRef:V,children:(0,d.jsx)(nt,{as:nA,allowPinchZoom:!0,children:(0,d.jsx)(N,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:s(o,e=>{E.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,d.jsx)(S,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,d.jsx)(X,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...b,...q,onPlaced:()=>F(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:s(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});nT.displayName="SelectContentImpl";var nL=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nf(nE,n),a=nR(nE,n),[u,s]=l.useState(null),[f,h]=l.useState(null),m=(0,p.s)(t,e=>h(e)),v=nl(n),g=l.useRef(!1),y=l.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:S}=a,C=l.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&f&&x&&b&&E){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,a=e.width+l,s=Math.max(a,t.width),d=c(i,[10,Math.max(10,window.innerWidth-10-s)]);u.style.minWidth=a+"px",u.style.left=d+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,a=e.width+l,s=Math.max(a,t.width),d=c(i,[10,Math.max(10,window.innerWidth-10-s)]);u.style.minWidth=a+"px",u.style.right=d+"px"}let l=v(),a=window.innerHeight-20,s=x.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),y=p+h+s+parseInt(d.paddingBottom,10)+m,w=Math.min(5*b.offsetHeight,y),S=window.getComputedStyle(x),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,T=b.offsetHeight/2,L=p+h+(b.offsetTop+T);if(L<=A){let e=l.length>0&&b===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(a-A,T+(e?R:0)+(f.clientHeight-x.offsetTop-x.offsetHeight)+m);u.style.height=L+t+"px"}else{let e=l.length>0&&b===l[0].ref.current;u.style.top="0px";let t=Math.max(A,p+x.offsetTop+(e?C:0)+T);u.style.height=t+(y-L)+"px",x.scrollTop=L-A+x.offsetTop}u.style.margin="10px 0",u.style.minHeight=w+"px",u.style.maxHeight=a+"px",r?.(),requestAnimationFrame(()=>g.current=!0)}},[v,i.trigger,i.valueNode,u,f,x,b,E,i.dir,r]);W(()=>C(),[C]);let[R,A]=l.useState();W(()=>{f&&A(window.getComputedStyle(f).zIndex)},[f]);let T=l.useCallback(e=>{e&&!0===y.current&&(C(),S?.(),y.current=!1)},[C,S]);return(0,d.jsx)(nk,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:g,onScrollButtonChange:T,children:(0,d.jsx)("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,d.jsx)(w.sG.div,{...o,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nL.displayName="SelectItemAlignedPosition";var nP=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=ns(n);return(0,d.jsx)(ta,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nP.displayName="SelectPopperPosition";var[nk,nN]=nu(nE,{}),nM="SelectViewport",nj=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nR(nM,n),a=nN(nM,n),u=(0,p.s)(t,i.onViewportChange),c=l.useRef(0);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,d.jsx)(ni.Slot,{scope:n,children:(0,d.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:s(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if(r?.current&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});nj.displayName=nM;var nO="SelectGroup",[nD,nI]=nu(nO);l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=B();return(0,d.jsx)(nD,{scope:n,id:o,children:(0,d.jsx)(w.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=nO;var nW="SelectLabel";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nI(nW,n);return(0,d.jsx)(w.sG.div,{id:o.id,...r,ref:t})}).displayName=nW;var nF="SelectItem",[nH,nB]=nu(nF),n_=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...a}=e,u=nf(nF,n),c=nR(nF,n),f=u.value===r,[h,m]=l.useState(i??""),[v,g]=l.useState(!1),y=(0,p.s)(t,e=>c.itemRefCallback?.(e,r,o)),x=B(),b=l.useRef("touch"),E=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,d.jsx)(nH,{scope:n,value:r,disabled:o,textId:x,isSelected:f,onItemTextChange:l.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,d.jsx)(ni.ItemSlot,{scope:n,value:r,disabled:o,textValue:h,children:(0,d.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:y,onFocus:s(a.onFocus,()=>g(!0)),onBlur:s(a.onBlur,()=>g(!1)),onClick:s(a.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:s(a.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:s(a.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:s(a.onPointerMove,e=>{b.current=e.pointerType,o?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:s(a.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:s(a.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(nr.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});n_.displayName=nF;var n$="SelectItemText",nz=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,a=nf(n$,n),c=nR(n$,n),s=nB(n$,n),f=nh(n$,n),[h,m]=l.useState(null),v=(0,p.s)(t,e=>m(e),s.onItemTextChange,e=>c.itemTextRefCallback?.(e,s.value,s.disabled)),g=h?.textContent,y=l.useMemo(()=>(0,d.jsx)("option",{value:s.value,disabled:s.disabled,children:g},s.value),[s.disabled,s.value,g]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=f;return W(()=>(x(y),()=>b(y)),[x,b,y]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(w.sG.span,{id:s.textId,...i,ref:v}),s.isSelected&&a.valueNode&&!a.valueNodeHasChildren?u.createPortal(i.children,a.valueNode):null]})});nz.displayName=n$;var nV="SelectItemIndicator",nG=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nB(nV,n).isSelected?(0,d.jsx)(w.sG.span,{"aria-hidden":!0,...r,ref:t}):null});nG.displayName=nV;var nK="SelectScrollUpButton",nU=l.forwardRef((e,t)=>{let n=nR(nK,e.__scopeSelect),r=nN(nK,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,p.s)(t,r.onScrollButtonChange);return W(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,d.jsx)(nq,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nU.displayName=nK;var nY="SelectScrollDownButton",nX=l.forwardRef((e,t)=>{let n=nR(nY,e.__scopeSelect),r=nN(nY,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,p.s)(t,r.onScrollButtonChange);return W(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,d.jsx)(nq,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nX.displayName=nY;var nq=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nR("SelectScrollButton",n),a=l.useRef(null),u=nl(n),c=l.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return l.useEffect(()=>()=>c(),[c]),W(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,d.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:s(o.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(r,50))}),onPointerMove:s(o.onPointerMove,()=>{i.onItemLeave?.(),null===a.current&&(a.current=window.setInterval(r,50))}),onPointerLeave:s(o.onPointerLeave,()=>{c()})})});l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,d.jsx)(w.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var nZ="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ns(n),i=nf(nZ,n),l=nR(nZ,n);return i.open&&"popper"===l.position?(0,d.jsx)(ts,{...o,...r,ref:t}):null}).displayName=nZ;var nJ=l.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{let o=l.useRef(null),i=(0,p.s)(r,o),a=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return l.useEffect(()=>{let e=o.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,d.jsx)(w.sG.select,{...n,style:{...tg,...n.style},ref:i,defaultValue:t})});function nQ(e){return""===e||void 0===e}function n0(e){let t=x(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function n1(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}nJ.displayName="SelectBubbleInput";var n2=nm,n5=ng,n6=nw,n8=nx,n3=nb,n4=nS,n9=nj,n7=n_,re=nz,rt=nG,rn=nU,rr=nX}};