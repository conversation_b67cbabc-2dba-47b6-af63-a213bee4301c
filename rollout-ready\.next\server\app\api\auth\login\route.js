(()=>{var e={};e.id=758,e.ids=[758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,r,s)=>{"use strict";s.d(r,{z:()=>a});var t=s(96330);let a=globalThis.prisma??new t.PrismaClient({log:["query"]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,s)=>{"use strict";s.d(r,{BE:()=>l,Er:()=>o,dk:()=>d,jw:()=>c,ri:()=>p});var t=s(85663),a=s(43205),n=s.n(a),i=s(5069);s(96330);let u=process.env.JWT_SECRET||"your-secret-key-change-in-production";async function o(e){return t.Ay.hash(e,12)}async function l(e,r){return t.Ay.compare(e,r)}async function c(e){var r;let s=await i.z.user.findUnique({where:{id:e},select:{id:!0,username:!0,systemRole:!0}});if(!s)throw Error("User not found");let t=(r={userId:s.id,username:s.username,systemRole:s.systemRole},n().sign(r,u,{expiresIn:"7d"}));return await i.z.session.create({data:{userId:e,token:t,expiresAt:new Date(Date.now()+6048e5)}}),t}async function d(e){try{let r=await i.z.session.findUnique({where:{token:e},include:{user:!0}});if(!r||r.expiresAt<new Date)return r&&await i.z.session.delete({where:{id:r.id}}),null;if(!function(e){try{return n().verify(e,u)}catch{return null}}(e))return null;return{id:r.user.id,username:r.user.username,email:r.user.email,firstName:r.user.firstName||void 0,lastName:r.user.lastName||void 0,systemRole:r.user.systemRole}}catch{return null}}async function p(e){await i.z.session.deleteMany({where:{token:e}})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},84311:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>w,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{POST:()=>c});var a=s(96559),n=s(48088),i=s(37719),u=s(32190),o=s(5069),l=s(12909);async function c(e){try{let{username:r,password:s}=await e.json();if(!r||!s)return u.NextResponse.json({message:"Username and password are required"},{status:400});let t=await o.z.user.findFirst({where:{OR:[{username:r.toLowerCase()},{email:r.toLowerCase()}],isActive:!0}});if(!t||!await (0,l.BE)(s,t.password))return u.NextResponse.json({message:"Invalid credentials"},{status:401});let a=await (0,l.jw)(t.id),n=u.NextResponse.json({message:"Login successful",user:{id:t.id,username:t.username,email:t.email,firstName:t.firstName,lastName:t.lastName,systemRole:t.systemRole}});return n.cookies.set("auth-token",a,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:604800}),n}catch(e){return console.error("Login error:",e),u.NextResponse.json({message:"Internal server error"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\RR\\rollout-ready\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:w}=d;function x(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,580,315],()=>s(84311));module.exports=t})();