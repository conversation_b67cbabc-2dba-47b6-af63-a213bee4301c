{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/src/app/admin/roles/%5Bid%5D/edit/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\n\ninterface RoleEditProps {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\ninterface Role {\n  id: number;\n  name: string;\n  description: string;\n  _count: {\n    templates: number;\n    projectRoles: number;\n  };\n}\n\nexport default function RoleEditPage({ params }: RoleEditProps) {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [role, setRole] = useState<Role | null>(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    description: \"\",\n  });\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadRoleData();\n  }, []);\n\n  const loadRoleData = async () => {\n    try {\n      const resolvedParams = await params;\n      const roleId = parseInt(resolvedParams.id);\n      \n      if (isNaN(roleId)) {\n        setError(\"Invalid role ID\");\n        return;\n      }\n\n      // Load role data\n      const response = await fetch(`/api/roles/${roleId}`);\n      if (!response.ok) {\n        setError(\"Role not found\");\n        return;\n      }\n      \n      const roleData = await response.json();\n      setRole(roleData);\n      \n      // Set form data\n      setFormData({\n        name: roleData.name,\n        description: roleData.description || \"\",\n      });\n    } catch (err) {\n      setError(\"Failed to load role data\");\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!role) return;\n    \n    setIsLoading(true);\n    setError(\"\");\n\n    try {\n      const response = await fetch(`/api/roles/${role.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(formData),\n      });\n\n      if (response.ok) {\n        router.push(`/admin/roles/${role.id}`);\n      } else {\n        const errorData = await response.json();\n        setError(errorData.message || \"Failed to update role\");\n      }\n    } catch (err) {\n      setError(\"An error occurred while updating the role\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n  };\n\n  const handleDelete = async () => {\n    if (!role) return;\n    \n    if (role._count.projectRoles > 0) {\n      setError(\"Cannot delete role that is currently used in projects\");\n      return;\n    }\n\n    if (!confirm(`Are you sure you want to delete the role \"${role.name}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(\"\");\n\n    try {\n      const response = await fetch(`/api/roles/${role.id}`, {\n        method: \"DELETE\",\n      });\n\n      if (response.ok) {\n        router.push(\"/admin/roles\");\n      } else {\n        const errorData = await response.json();\n        setError(errorData.message || \"Failed to delete role\");\n      }\n    } catch (err) {\n      setError(\"An error occurred while deleting the role\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (error) {\n    return (\n      <div className=\"space-y-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Error</h1>\n          <p className=\"text-red-600 mt-2\">{error}</p>\n          <Link href=\"/admin/roles\">\n            <Button className=\"mt-4\">Back to Roles</Button>\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  if (!role) {\n    return (\n      <div className=\"space-y-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Loading...</h1>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Edit Role: {role.name}</h1>\n        <p className=\"text-gray-600 mt-2\">\n          Modify role details and settings\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Role Details */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Role Details</CardTitle>\n            <CardDescription>\n              Update basic role information\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\">Role Name *</Label>\n              <Input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={handleChange}\n                placeholder=\"e.g., Project Manager, Infrastructure Lead\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Description</Label>\n              <textarea\n                id=\"description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleChange}\n                placeholder=\"Brief description of this role's responsibilities\"\n                className=\"w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Role Usage Info */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Role Usage</CardTitle>\n            <CardDescription>\n              Current usage of this role in the system\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <p className=\"text-sm font-medium\">Templates</p>\n                <p className=\"text-2xl font-bold\">{role._count.templates}</p>\n                <p className=\"text-xs text-gray-600\">Associated templates</p>\n              </div>\n              <div>\n                <p className=\"text-sm font-medium\">Project Usage</p>\n                <p className=\"text-2xl font-bold\">{role._count.projectRoles}</p>\n                <p className=\"text-xs text-gray-600\">Times used in projects</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Actions */}\n        <div className=\"flex gap-4\">\n          <Button type=\"submit\" disabled={isLoading}>\n            {isLoading ? \"Updating...\" : \"Update Role\"}\n          </Button>\n          <Link href={`/admin/roles/${role.id}`}>\n            <Button type=\"button\" variant=\"outline\">\n              Cancel\n            </Button>\n          </Link>\n          {role._count.projectRoles === 0 && (\n            <Button \n              type=\"button\" \n              variant=\"destructive\" \n              onClick={handleDelete}\n              disabled={isLoading}\n            >\n              Delete Role\n            </Button>\n          )}\n        </div>\n      </form>\n\n      {role._count.projectRoles > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Cannot Delete</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-gray-600\">\n              This role cannot be deleted because it is currently used in {role._count.projectRoles} project{role._count.projectRoles !== 1 ? 's' : ''}. \n              Remove the role from all projects before deleting it.\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AA0Be,SAAS,aAAa,EAAE,MAAM,EAAiB;IAC5D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;IACf;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,iBAAiB,MAAM;YAC7B,MAAM,SAAS,SAAS,eAAe,EAAE;YAEzC,IAAI,MAAM,SAAS;gBACjB,SAAS;gBACT;YACF;YAEA,iBAAiB;YACjB,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;YACnD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS;gBACT;YACF;YAEA,MAAM,WAAW,MAAM,SAAS,IAAI;YACpC,QAAQ;YAER,gBAAgB;YAChB,YAAY;gBACV,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW,IAAI;YACvC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;YACvC,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;YAChC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;QAEX,IAAI,KAAK,MAAM,CAAC,YAAY,GAAG,GAAG;YAChC,SAAS;YACT;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,0CAA0C,EAAE,KAAK,IAAI,CAAC,gCAAgC,CAAC,GAAG;YACtG;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE;gBACpD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;YAChC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKnC;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAmC;;;;;;;;;;;;;;;;IAIzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;;4BAAmC;4BAAY,KAAK,IAAI;;;;;;;kCACtE,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;oCACpB,uBACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAsB,KAAK,MAAM,CAAC,SAAS;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAsB,KAAK,MAAM,CAAC,YAAY;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU;0CAC7B,YAAY,gBAAgB;;;;;;0CAE/B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;0CACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;8CAAU;;;;;;;;;;;4BAIzC,KAAK,MAAM,CAAC,YAAY,KAAK,mBAC5B,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;0CACX;;;;;;;;;;;;;;;;;;YAON,KAAK,MAAM,CAAC,YAAY,GAAG,mBAC1B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAE,WAAU;;gCAAgB;gCACkC,KAAK,MAAM,CAAC,YAAY;gCAAC;gCAAS,KAAK,MAAM,CAAC,YAAY,KAAK,IAAI,MAAM;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAQvJ", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,kBAAkB;AA4ChB;;;;;AA1CX,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,4KAAO,aAAA,EAAW,CAAA,UAAA,EAAa,IAAI,EAAE;IAC3C,MAAM,iNAAa,aAAA,EAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,UAAU,OAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;YAChC,MAAA,CAAe,OAAO,GAAA,CAAI,UAAU,CAAC,CAAA,GAAI;QAC5C;QAEA,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,CAAA,UAAA,EAAa,IAAI,EAAA;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,kNAAS,YAAA,EAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/RR/rollout-ready/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,kNAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}]}