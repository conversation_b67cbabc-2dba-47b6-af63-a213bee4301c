(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[708],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>o});var t=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:a,size:n,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:s})),...d})}},1243:(e,s,a)=>{Promise.resolve().then(a.bind(a,4822))},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(5155);a(2115);var r=a(9434);function n(e){let{className:s,type:a,...n}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},4822:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var t=a(5155),r=a(2115),n=a(5695),i=a(6874),l=a.n(i),o=a(285),d=a(6695),c=a(2523),u=a(8979),m=a(9409);function p(){let e=(0,n.useRouter)(),[s,a]=(0,r.useState)(!1),[i,p]=(0,r.useState)(""),[h,x]=(0,r.useState)([]),[g,f]=(0,r.useState)({username:"",email:"",password:"",confirmPassword:"",firstName:"",lastName:"",systemRole:"USER",jobRoleId:"none"});(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/roles");if(e.ok){let s=await e.json();x(s)}}catch(e){console.error("Failed to fetch roles:",e)}})()},[]);let v=e=>{let{name:s,value:a}=e.target;f(e=>({...e,[s]:a}))},b=async s=>{if(s.preventDefault(),a(!0),p(""),g.password!==g.confirmPassword){p("Passwords do not match"),a(!1);return}if(g.password.length<6){p("Password must be at least 6 characters long"),a(!1);return}try{let s=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:g.username,email:g.email,password:g.password,firstName:g.firstName,lastName:g.lastName,systemRole:g.systemRole,jobRoleId:"none"===g.jobRoleId?null:g.jobRoleId})}),a=await s.json();s.ok?e.push("/admin/users"):p(a.message||"Failed to create user")}catch(e){p("An error occurred. Please try again.")}finally{a(!1)}};return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New User"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Add a new user to the system"})]}),(0,t.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"User Information"}),(0,t.jsx)(d.BT,{children:"Basic user details and credentials"})]}),(0,t.jsxs)(d.Wu,{className:"space-y-6",children:[i&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:i}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"firstName",children:"First Name"}),(0,t.jsx)(c.p,{id:"firstName",name:"firstName",type:"text",value:g.firstName,onChange:v,placeholder:"John"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"lastName",children:"Last Name"}),(0,t.jsx)(c.p,{id:"lastName",name:"lastName",type:"text",value:g.lastName,onChange:v,placeholder:"Doe"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"username",children:"Username *"}),(0,t.jsx)(c.p,{id:"username",name:"username",type:"text",required:!0,value:g.username,onChange:v,placeholder:"johndoe"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"email",children:"Email *"}),(0,t.jsx)(c.p,{id:"email",name:"email",type:"email",required:!0,value:g.email,onChange:v,placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"password",children:"Password *"}),(0,t.jsx)(c.p,{id:"password",name:"password",type:"password",required:!0,value:g.password,onChange:v,placeholder:"Minimum 6 characters"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"confirmPassword",children:"Confirm Password *"}),(0,t.jsx)(c.p,{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:g.confirmPassword,onChange:v,placeholder:"Confirm password"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"systemRole",children:"System Role *"}),(0,t.jsxs)(m.l6,{value:g.systemRole,onValueChange:e=>{f(s=>({...s,systemRole:e}))},children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"Select a system role"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"USER",children:"User - Can view and update assigned tasks"}),(0,t.jsx)(m.eb,{value:"MANAGER",children:"Manager - Can create projects and assign users"}),(0,t.jsx)(m.eb,{value:"ADMIN",children:"Admin - Full system access"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"jobRole",children:"Job Role"}),(0,t.jsxs)(m.l6,{value:g.jobRoleId,onValueChange:e=>{f(s=>({...s,jobRoleId:e}))},children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"Select a job role (optional)"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"none",children:"No specific job role"}),h.map(e=>(0,t.jsxs)(m.eb,{value:e.id.toString(),children:[e.name,e.description&&" - ".concat(e.description)]},e.id))]})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Job role determines which project roles this user can be assigned to"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(o.$,{type:"submit",disabled:s,children:s?"Creating...":"Create User"}),(0,t.jsx)(l(),{href:"/admin/users",children:(0,t.jsx)(o.$,{type:"button",variant:"outline",children:"Cancel"})})]})]})]})}},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i});var t=a(5155);a(2115);var r=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}},8979:(e,s,a)=>{"use strict";a.d(s,{J:()=>o});var t=a(5155),r=a(2115),n=a(3655),i=r.forwardRef((e,s)=>(0,t.jsx)(n.sG.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var l=a(9434);function o(e){let{className:s,...a}=e;return(0,t.jsx)(i,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},9409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>c});var t=a(5155);a(2115);var r=a(1396),n=a(6474),i=a(5196),l=a(7863),o=a(9434);function d(e){let{...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"select",...s})}function c(e){let{...s}=e;return(0,t.jsx)(r.WT,{"data-slot":"select-value",...s})}function u(e){let{className:s,size:a="default",children:i,...l}=e;return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...l,children:[i,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:s,children:a,position:n="popper",...i}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,t.jsx)(h,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(x,{})]})})}function p(e){let{className:s,children:a,...n}=e;return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...n,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(i.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function h(e){let{className:s,...a}=e;return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,t.jsx)(l.A,{className:"size-4"})})}function x(e){let{className:s,...a}=e;return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,t.jsx)(n.A,{className:"size-4"})})}},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>n});var t=a(2596),r=a(9688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[277,874,537,441,684,358],()=>s(1243)),_N_E=e.O()}]);